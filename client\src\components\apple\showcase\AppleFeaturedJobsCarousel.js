import React, { useRef, useEffect, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { SplitText } from 'gsap/SplitText';
import { Physics2DPlugin } from 'gsap/Physics2DPlugin';
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin';
import { CustomEase } from 'gsap/CustomEase';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import { useLanguage } from '../../../contexts/LanguageContext';
import {
  MapPinIcon,
  ClockIcon,
  CurrencyDollarIcon,
  StarIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  BookmarkIcon,
} from '@heroicons/react/24/outline';
import { BookmarkIcon as BookmarkSolidIcon } from '@heroicons/react/24/solid';

// Register GSAP plugins
gsap.registerPlugin(<PERSON><PERSON><PERSON><PERSON><PERSON>, SplitText, Physics2DPlugin, MorphSVGPlugin, CustomEase, MotionPathPlugin);

// Premium easing curves for carousel animations
const carouselEases = {
  elastic: CustomEase.create("elastic", "M0,0 C0.25,0 0.4,1.4 0.7,1 C0.85,0.8 1,1 1,1"),
  bounce: CustomEase.create("bounce", "M0,0 C0.14,0 0.242,0.438 0.272,0.561 0.313,0.728 0.354,0.963 0.362,1 0.37,0.985 0.414,0.928 0.455,0.879 0.504,0.822 0.565,0.729 0.621,0.653 0.681,0.573 0.737,0.5 0.785,0.5 0.856,0.5 0.923,0.717 1,1"),
  liquid: CustomEase.create("liquid", "M0,0 C0.29,0.01 0.49,1.53 0.59,1.23 C0.69,0.93 1,1 1,1"),
  magnetic: CustomEase.create("magnetic", "M0,0 C0.5,0 0.5,1 1,1"),
  wave: CustomEase.create("wave", "M0,0 C0.2,0.8 0.8,0.2 1,1"),
  carousel: CustomEase.create("carousel", "M0,0 C0.4,0 0.6,1 1,1")
};

const AppleFeaturedJobsCarousel = () => {
  const { t } = useLanguage();
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const carouselRef = useRef(null);
  const particleCanvasRef = useRef(null);
  const morphingShapesRef = useRef([]);
  const jobCardRefs = useRef([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [savedJobs, setSavedJobs] = useState(new Set());

  const featuredJobs = [
    {
      id: 1,
      title: 'Senior React Developer',
      company: 'TechCorp Inc.',
      location: 'Remote',
      type: 'Full-time',
      salary: '$80,000 - $120,000',
      rating: 4.8,
      description: 'We are looking for an experienced React developer to join our dynamic team...',
      skills: ['React', 'TypeScript', 'Node.js', 'GraphQL'],
      urgent: true,
      featured: true,
    },
    {
      id: 2,
      title: 'UI/UX Designer',
      company: 'Design Studio',
      location: 'New York, NY',
      type: 'Contract',
      salary: '$60 - $80/hour',
      rating: 4.9,
      description: 'Create beautiful and intuitive user interfaces for our mobile applications...',
      skills: ['Figma', 'Adobe XD', 'Prototyping', 'User Research'],
      urgent: false,
      featured: true,
    },
    {
      id: 3,
      title: 'Full Stack Engineer',
      company: 'StartupXYZ',
      location: 'San Francisco, CA',
      type: 'Full-time',
      salary: '$90,000 - $140,000',
      rating: 4.7,
      description: 'Join our fast-growing startup and help build the next generation platform...',
      skills: ['Python', 'Django', 'React', 'PostgreSQL'],
      urgent: true,
      featured: true,
    },
    {
      id: 4,
      title: 'DevOps Engineer',
      company: 'CloudTech',
      location: 'Remote',
      type: 'Full-time',
      salary: '$85,000 - $130,000',
      rating: 4.6,
      description: 'Help us scale our infrastructure and improve deployment processes...',
      skills: ['AWS', 'Docker', 'Kubernetes', 'Terraform'],
      urgent: false,
      featured: true,
    },
    {
      id: 5,
      title: 'Mobile App Developer',
      company: 'MobileFirst',
      location: 'Austin, TX',
      type: 'Contract',
      salary: '$70 - $90/hour',
      rating: 4.8,
      description: 'Develop cutting-edge mobile applications for iOS and Android platforms...',
      skills: ['React Native', 'Swift', 'Kotlin', 'Firebase'],
      urgent: true,
      featured: true,
    },
  ];

  const itemsPerView = 3;
  const maxIndex = Math.max(0, featuredJobs.length - itemsPerView);

  // Advanced Particle Stream for Jobs
  const createAdvancedJobParticles = () => {
    if (!particleCanvasRef.current) return;

    const container = particleCanvasRef.current;
    const particles = [];

    // Create 80 streaming job particles
    for (let i = 0; i < 80; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-1 h-1 bg-gradient-to-r from-green-400 to-blue-400 rounded-full opacity-40';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.top = Math.random() * 100 + '%';
      container.appendChild(particle);
      particles.push(particle);

      // Create curved motion paths for job flow
      const pathData = `M0,${Math.random() * 100} Q${Math.random() * 100},${Math.random() * 100} ${100},${Math.random() * 100}`;
      
      gsap.to(particle, {
        motionPath: {
          path: pathData,
          autoRotate: true
        },
        duration: Math.random() * 10 + 6,
        repeat: -1,
        ease: carouselEases.carousel,
        delay: Math.random() * 3
      });

      // Job opportunity pulse
      gsap.to(particle, {
        scale: "random(0.8, 2.5)",
        opacity: "random(0.2, 0.8)",
        duration: "random(2, 5)",
        repeat: -1,
        yoyo: true,
        ease: carouselEases.wave
      });
    }
  };

  // Morphing Job Opportunity Backgrounds
  const createMorphingJobShapes = () => {
    morphingShapesRef.current.forEach((shape, index) => {
      if (!shape) return;

      const colors = [
        'from-green-400/15 to-blue-400/15',
        'from-blue-400/15 to-indigo-400/15',
        'from-indigo-400/15 to-purple-400/15',
        'from-purple-400/15 to-pink-400/15',
        'from-pink-400/15 to-red-400/15'
      ];

      shape.className = `absolute bg-gradient-to-br ${colors[index % colors.length]} rounded-full`;

      const morphTimeline = gsap.timeline({ repeat: -1, yoyo: true });
      
      morphTimeline
        .to(shape, {
          borderRadius: "70% 30% 40% 60% / 30% 60% 40% 70%",
          scale: 1.4,
          rotation: 270,
          x: "random(-40, 40)",
          y: "random(-30, 30)",
          duration: 6,
          ease: carouselEases.liquid
        })
        .to(shape, {
          borderRadius: "30% 70% 60% 40% / 60% 40% 70% 30%",
          scale: 0.8,
          rotation: -135,
          x: "random(-30, 30)",
          y: "random(-40, 40)",
          duration: 4,
          ease: carouselEases.elastic
        })
        .to(shape, {
          borderRadius: "60% 40% 30% 70% / 40% 70% 30% 60%",
          scale: 1.2,
          rotation: 180,
          x: "random(-35, 35)",
          y: "random(-35, 35)",
          duration: 5,
          ease: carouselEases.wave
        });

      morphTimeline.delay(index * 1.2);
    });
  };

  // Advanced Title Animation with Job Theme
  const createAdvancedTitleAnimation = () => {
    if (!titleRef.current) return;

    const titleElement = titleRef.current.querySelector('h2');
    const subtitleElement = titleRef.current.querySelector('p');

    if (titleElement) {
      const titleSplit = new SplitText(titleElement, { type: "chars,words" });

      gsap.fromTo(titleSplit.chars, {
        opacity: 0,
        y: 120,
        rotationX: -90,
        transformOrigin: "center bottom"
      }, {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration: 2.0,
        stagger: 0.04,
        ease: carouselEases.bounce,
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      });

      // Add job opportunity glow effect
      gsap.to(titleElement, {
        textShadow: "0 0 30px rgba(34, 197, 94, 0.6)",
        duration: 3,
        repeat: -1,
        yoyo: true,
        ease: carouselEases.wave
      });
    }

    if (subtitleElement) {
      const subtitleSplit = new SplitText(subtitleElement, { type: "words" });

      gsap.fromTo(subtitleSplit.words, {
        opacity: 0,
        y: 60,
        scale: 0.7
      }, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1.4,
        stagger: 0.12,
        ease: carouselEases.elastic,
        delay: 1.0,
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      });
    }
  };

  // Advanced Magnetic Job Card Hover
  const createMagneticJobHover = (jobElement, index) => {
    if (!jobElement) return;

    const header = jobElement.querySelector('.job-header');
    const content = jobElement.querySelector('.job-content');
    const bookmark = jobElement.querySelector('.job-bookmark');

    let isHovering = false;

    jobElement.addEventListener('mouseenter', () => {
      isHovering = true;

      // Magnetic job hover timeline
      const hoverTL = gsap.timeline();

      hoverTL
        .to(jobElement, {
          scale: 1.06,
          y: -20,
          rotationY: 6,
          boxShadow: "0 40px 80px rgba(0,0,0,0.2)",
          duration: 0.8,
          ease: carouselEases.magnetic
        })
        .to(header, {
          scale: 1.05,
          duration: 0.6,
          ease: carouselEases.elastic
        }, 0)
        .to(bookmark, {
          scale: 1.3,
          rotation: 15,
          duration: 0.7,
          ease: carouselEases.bounce
        }, 0.1);

      // Create job opportunity particles
      createJobOpportunityParticles(jobElement, index);
    });

    jobElement.addEventListener('mouseleave', () => {
      isHovering = false;

      gsap.to(jobElement, {
        scale: 1,
        y: 0,
        rotationY: 0,
        boxShadow: "0 10px 25px rgba(0,0,0,0.1)",
        duration: 1.0,
        ease: carouselEases.elastic
      });

      gsap.to(header, {
        scale: 1,
        duration: 0.6,
        ease: carouselEases.bounce
      });

      gsap.to(bookmark, {
        scale: 1,
        rotation: 0,
        duration: 0.5,
        ease: carouselEases.wave
      });
    });

    // Real-time mouse tracking
    jobElement.addEventListener('mousemove', (e) => {
      if (!isHovering) return;

      const rect = jobElement.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      const mouseX = e.clientX - centerX;
      const mouseY = e.clientY - centerY;

      gsap.to(jobElement, {
        x: mouseX * 0.06,
        y: mouseY * 0.06,
        duration: 0.3,
        ease: "power2.out"
      });

      gsap.to(header, {
        x: mouseX * 0.1,
        y: mouseY * 0.1,
        duration: 0.2,
        ease: "power2.out"
      });
    });
  };

  // Create Job Opportunity Particles
  const createJobOpportunityParticles = (element, index) => {
    const colors = ['#22C55E', '#3B82F6', '#6366F1', '#8B5CF6', '#EC4899'];
    const color = colors[index % colors.length];

    for (let i = 0; i < 12; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute pointer-events-none w-3 h-3 rounded-full';
      particle.style.background = color;
      particle.style.opacity = '0';
      element.appendChild(particle);

      const angle = (i / 12) * Math.PI * 2;
      const distance = 70 + Math.random() * 40;

      gsap.set(particle, {
        x: 0,
        y: 0,
        scale: 0
      });

      gsap.to(particle, {
        x: Math.cos(angle) * distance,
        y: Math.sin(angle) * distance,
        scale: 1.8,
        opacity: 0.9,
        duration: 1.2,
        ease: carouselEases.elastic
      });

      gsap.to(particle, {
        opacity: 0,
        scale: 0,
        duration: 1.0,
        delay: 1.2,
        ease: "power2.in",
        onComplete: () => {
          if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
          }
        }
      });
    }
  };

  // Advanced Carousel Animation
  const createAdvancedCarouselAnimation = () => {
    if (!carouselRef.current) return;

    const jobElements = Array.from(carouselRef.current.querySelectorAll('.job-card'));

    jobElements.forEach((job, index) => {
      // Store reference for hover effects
      jobCardRefs.current[index] = job;

      // Create magnetic hover effect
      createMagneticJobHover(job, index);

      // Carousel entrance animation
      gsap.fromTo(job, {
        opacity: 0,
        y: 100,
        scale: 0.8,
        rotationY: 30
      }, {
        opacity: 1,
        y: 0,
        scale: 1,
        rotationY: 0,
        duration: 1.8,
        delay: index * 0.2,
        ease: carouselEases.elastic,
        scrollTrigger: {
          trigger: carouselRef.current,
          start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      });

      // Animate job content
      const jobContent = job.querySelectorAll('.job-content > *');
      gsap.fromTo(jobContent, {
        opacity: 0,
        x: -40
      }, {
        opacity: 1,
        x: 0,
        duration: 1.0,
        stagger: 0.1,
        delay: index * 0.2 + 0.6,
        ease: carouselEases.wave,
        scrollTrigger: {
          trigger: carouselRef.current,
          start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      });
    });
  };

  // Enhanced Carousel Navigation
  const nextSlide = () => {
    if (currentIndex < maxIndex) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);
      
      // Animate carousel transition
      gsap.to(carouselRef.current.querySelector('.carousel-inner'), {
        x: -newIndex * (100 / itemsPerView) + '%',
        duration: 0.8,
        ease: carouselEases.carousel
      });
    }
  };

  const prevSlide = () => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
      
      // Animate carousel transition
      gsap.to(carouselRef.current.querySelector('.carousel-inner'), {
        x: -newIndex * (100 / itemsPerView) + '%',
        duration: 0.8,
        ease: carouselEases.carousel
      });
    }
  };

  const toggleSaveJob = (jobId) => {
    const newSavedJobs = new Set(savedJobs);
    if (newSavedJobs.has(jobId)) {
      newSavedJobs.delete(jobId);
    } else {
      newSavedJobs.add(jobId);
    }
    setSavedJobs(newSavedJobs);
  };

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Initialize all advanced animations
      createAdvancedJobParticles();
      createMorphingJobShapes();
      createAdvancedTitleAnimation();
      createAdvancedCarouselAnimation();
    }, sectionRef);

    return () => ctx.revert();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {/* Enhanced CSS */}
      <style>{`
        @keyframes liquid-job {
          0%, 100% {
            border-radius: 70% 30% 40% 60% / 30% 60% 40% 70%;
          }
          25% {
            border-radius: 30% 70% 60% 40% / 60% 40% 70% 30%;
          }
          50% {
            border-radius: 60% 40% 30% 70% / 40% 70% 30% 60%;
          }
          75% {
            border-radius: 40% 60% 70% 30% / 70% 30% 60% 40%;
          }
        }
        
        .liquid-job {
          animation: liquid-job 15s ease-in-out infinite;
        }
        
        .job-card {
          transform-style: preserve-3d;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .job-streaming-particles {
          will-change: transform;
        }
        
        .carousel-inner {
          will-change: transform;
        }
      `}</style>

      <section
        ref={sectionRef}
        className='relative py-20 bg-gray-50 dark:bg-gray-800 transition-colors duration-300 overflow-hidden'
      >
        {/* Advanced Multi-layer Background */}
        <div className='absolute inset-0'>
          <div 
            ref={el => morphingShapesRef.current[0] = el}
            className='absolute top-20 left-16 w-52 h-52 liquid-job'
          />
          <div 
            ref={el => morphingShapesRef.current[1] = el}
            className='absolute top-40 right-20 w-44 h-44 liquid-job'
          />
          <div 
            ref={el => morphingShapesRef.current[2] = el}
            className='absolute bottom-24 left-1/3 w-60 h-60 liquid-job'
          />
          <div 
            ref={el => morphingShapesRef.current[3] = el}
            className='absolute bottom-20 right-16 w-48 h-48 liquid-job'
          />
          <div 
            ref={el => morphingShapesRef.current[4] = el}
            className='absolute top-1/2 left-1/2 w-36 h-36 liquid-job'
          />
        </div>

        {/* Advanced Job Particle Stream Canvas */}
        <div 
          ref={particleCanvasRef}
          className='absolute inset-0 pointer-events-none overflow-hidden job-streaming-particles'
        />

        <div className='relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          {/* Enhanced Section Header */}
          <div ref={titleRef} className='text-center mb-12'>
            <h2 className='text-4xl sm:text-5xl lg:text-6xl font-black text-gray-900 dark:text-gray-100 mb-8 transition-colors duration-300'>
              {t('featuredJobs')}
            </h2>
            <p className='text-xl sm:text-2xl lg:text-3xl text-gray-600 dark:text-gray-400 max-w-4xl mx-auto leading-relaxed transition-colors duration-300'>
              {t('discoverTopOpportunities')}
            </p>
          </div>

          {/* Enhanced Carousel Container */}
          <div className='relative'>
            {/* Enhanced Navigation Buttons */}
            <button
              onClick={prevSlide}
              disabled={currentIndex === 0}
              className='absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-6 z-20 w-16 h-16 bg-white dark:bg-gray-700 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-600 flex items-center justify-center transition-all duration-300 hover:shadow-3xl disabled:opacity-40 disabled:cursor-not-allowed hover:scale-110'
            >
              <ArrowLeftIcon className='h-6 w-6 text-gray-600 dark:text-gray-300' />
            </button>

            <button
              onClick={nextSlide}
              disabled={currentIndex === maxIndex}
              className='absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-6 z-20 w-16 h-16 bg-white dark:bg-gray-700 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-600 flex items-center justify-center transition-all duration-300 hover:shadow-3xl disabled:opacity-40 disabled:cursor-not-allowed hover:scale-110'
            >
              <ArrowRightIcon className='h-6 w-6 text-gray-600 dark:text-gray-300' />
            </button>

            {/* Enhanced Carousel */}
            <div
              ref={carouselRef}
              className='overflow-hidden rounded-3xl'
            >
              <div
                className='carousel-inner flex transition-transform duration-500 ease-in-out'
                style={{
                  transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)`,
                }}
              >
                {featuredJobs.map((job) => (
                  <div
                    key={job.id}
                    className='w-full md:w-1/2 lg:w-1/3 flex-shrink-0 px-4'
                  >
                    <div className='job-card bg-white dark:bg-gray-900 rounded-3xl p-8 border border-gray-100 dark:border-gray-700 transition-all duration-500 hover:shadow-2xl hover:border-gray-200 dark:hover:border-gray-600 group transform-gpu'>
                      {/* Enhanced Header */}
                      <div className='job-header flex items-start justify-between mb-6'>
                        <div className='flex-1'>
                          {job.urgent && (
                            <span className='inline-block px-3 py-1 text-sm font-bold text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 rounded-full mb-3 animate-pulse'>
                              {t('urgent')}
                            </span>
                          )}
                          <h3 className='text-2xl font-bold text-gray-900 dark:text-gray-100 mb-3 transition-colors duration-300'>
                            {job.title}
                          </h3>
                          <p className='text-lg font-semibold text-gray-600 dark:text-gray-400 transition-colors duration-300'>
                            {job.company}
                          </p>
                        </div>
                        <button
                          onClick={() => toggleSaveJob(job.id)}
                          className='job-bookmark p-3 rounded-2xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200'
                        >
                          {savedJobs.has(job.id) ? (
                            <BookmarkSolidIcon className='h-6 w-6 text-blue-600 dark:text-blue-400' />
                          ) : (
                            <BookmarkIcon className='h-6 w-6 text-gray-400 dark:text-gray-500' />
                          )}
                        </button>
                      </div>

                      {/* Enhanced Job Details */}
                      <div className='job-content space-y-4 mb-6'>
                        <div className='grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400'>
                          <div className='flex items-center'>
                            <MapPinIcon className='h-5 w-5 mr-2' />
                            <span className='font-medium'>{job.location}</span>
                          </div>
                          <div className='flex items-center'>
                            <ClockIcon className='h-5 w-5 mr-2' />
                            <span className='font-medium'>{job.type}</span>
                          </div>
                          <div className='flex items-center'>
                            <CurrencyDollarIcon className='h-5 w-5 mr-2' />
                            <span className='font-medium'>{job.salary}</span>
                          </div>
                          <div className='flex items-center'>
                            <StarIcon className='h-5 w-5 mr-2 text-yellow-500' />
                            <span className='font-medium'>{job.rating}</span>
                          </div>
                        </div>

                        {/* Enhanced Description */}
                        <p className='text-gray-600 dark:text-gray-400 leading-relaxed transition-colors duration-300'>
                          {job.description}
                        </p>

                        {/* Enhanced Skills */}
                        <div className='flex flex-wrap gap-2'>
                          {job.skills.slice(0, 3).map((skill) => (
                            <span
                              key={skill}
                              className='px-3 py-1 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 rounded-full'
                            >
                              {skill}
                            </span>
                          ))}
                          {job.skills.length > 3 && (
                            <span className='px-3 py-1 text-sm font-medium text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-full'>
                              +{job.skills.length - 3}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Enhanced Apply Button */}
                      <button className='w-full px-6 py-4 text-lg font-bold text-white bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform group-hover:scale-105'>
                        {t('applyNow')}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Enhanced Dots Indicator */}
            <div className='flex justify-center mt-10 space-x-3'>
              {Array.from({ length: maxIndex + 1 }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-4 h-4 rounded-full transition-all duration-300 ${
                    index === currentIndex
                      ? 'bg-blue-600 dark:bg-blue-400 scale-125'
                      : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default AppleFeaturedJobsCarousel;
