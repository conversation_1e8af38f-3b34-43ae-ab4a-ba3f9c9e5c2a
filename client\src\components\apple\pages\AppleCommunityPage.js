import { useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import {
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  HeartIcon,
  ShareIcon,
  BookmarkIcon,
  ClockIcon,
  EyeIcon,
  PlusIcon,
  FireIcon,
  ArrowTrendingUpIcon,
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../../contexts/LanguageContext';

const AppleCommunityPage = () => {
  const { t } = useLanguage();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedSort, setSelectedSort] = useState('recent');

  const headerRef = useRef(null);

  // Apple-style animations
  useEffect(() => {
    if (headerRef.current) {
      gsap.fromTo(
        headerRef.current,
        { y: 30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: 'power2.out' }
      );
    }
  }, []);

  // Mock community posts data
  const posts = [
    {
      id: 1,
      title: 'Tips for Landing Your First Freelance Client',
      content:
        'After 6 months of freelancing, here are the strategies that helped me land my first high-paying client...',
      author: {
        name: '<PERSON>',
        avatar: null,
        title: 'Full-Stack Developer',
        verified: true,
      },
      category: 'Tips & Advice',
      likes: 127,
      comments: 23,
      views: 1240,
      timeAgo: '2 hours ago',
      trending: true,
      tags: ['freelancing', 'clients', 'tips'],
    },
    {
      id: 2,
      title: 'How I Increased My Hourly Rate by 150% in One Year',
      content:
        'A detailed breakdown of the strategies I used to significantly increase my freelance rates while maintaining client satisfaction...',
      author: {
        name: 'Michael Chen',
        avatar: null,
        title: 'UI/UX Designer',
        verified: true,
      },
      category: 'Success Stories',
      likes: 89,
      comments: 31,
      views: 890,
      timeAgo: '5 hours ago',
      trending: false,
      tags: ['rates', 'success', 'strategy'],
    },
    {
      id: 3,
      title: 'Best Tools for Remote Collaboration in 2024',
      content:
        'A comprehensive list of tools that have transformed how I work with clients remotely. From project management to communication...',
      author: {
        name: 'Emily Rodriguez',
        avatar: null,
        title: 'Project Manager',
        verified: false,
      },
      category: 'Tools & Resources',
      likes: 156,
      comments: 45,
      views: 2100,
      timeAgo: '1 day ago',
      trending: true,
      tags: ['tools', 'remote', 'productivity'],
    },
    {
      id: 4,
      title: 'Dealing with Difficult Clients: A Survival Guide',
      content:
        "Every freelancer encounters challenging clients. Here's how to handle difficult situations professionally while protecting your business...",
      author: {
        name: 'David Kim',
        avatar: null,
        title: 'Marketing Consultant',
        verified: true,
      },
      category: 'Client Relations',
      likes: 203,
      comments: 67,
      views: 3200,
      timeAgo: '2 days ago',
      trending: false,
      tags: ['clients', 'communication', 'business'],
    },
    {
      id: 5,
      title: 'Building a Personal Brand as a Freelancer',
      content:
        "Your personal brand is your most valuable asset. Here's how to build and maintain a strong professional presence online...",
      author: {
        name: 'Lisa Thompson',
        avatar: null,
        title: 'Content Creator',
        verified: true,
      },
      category: 'Branding',
      likes: 94,
      comments: 18,
      views: 750,
      timeAgo: '3 days ago',
      trending: false,
      tags: ['branding', 'marketing', 'personal'],
    },
    {
      id: 6,
      title: 'The Future of Freelancing: AI and Automation',
      content:
        'How artificial intelligence and automation are changing the freelance landscape, and how to adapt your skills for the future...',
      author: {
        name: 'Alex Johnson',
        avatar: null,
        title: 'Tech Consultant',
        verified: true,
      },
      category: 'Industry Insights',
      likes: 178,
      comments: 52,
      views: 1890,
      timeAgo: '4 days ago',
      trending: true,
      tags: ['AI', 'future', 'technology'],
    },
  ];

  const categories = [
    { value: 'all', label: t('allPosts') },
    { value: 'tips', label: t('tipsAdvice') },
    { value: 'success', label: t('successStoriesCategory') },
    { value: 'tools', label: t('toolsResources') },
    { value: 'clients', label: t('clientRelations') },
    { value: 'branding', label: t('brandingCategory') },
    { value: 'insights', label: t('industryInsights') },
  ];

  const sortOptions = [
    { value: 'recent', label: t('mostRecent') },
    { value: 'popular', label: t('mostPopular') },
    { value: 'trending', label: t('trendingFilter') },
    { value: 'discussed', label: t('mostDiscussed') },
  ];

  const formatNumber = num => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  const formatTimeAgo = (timeString) => {
    if (timeString.includes('hours')) {
      return timeString.replace('hours', t('hoursAgo')).replace('ago', t('timeAgo'));
    }
    if (timeString.includes('days')) {
      return timeString.replace('days', t('daysAgo')).replace('ago', t('timeAgo'));
    }
    return timeString;
  };

  return (
    <div className='min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300'>
      <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8'>
        {/* Header */}
        <div ref={headerRef} className='text-center mb-12'>
          <div className='inline-flex items-center space-x-3 mb-6'>
            <div className='w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center shadow-sm'>
              <UserGroupIcon className='h-6 w-6 text-white' />
            </div>
          </div>

          <h1 className='text-4xl md:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-4 transition-colors duration-300'>
            {t('communityHubTitle')}
          </h1>
          <p className='text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed transition-colors duration-300'>
            {t('connectLearnGrow')}
          </p>
        </div>

        {/* Stats */}
        <div className='grid grid-cols-2 md:grid-cols-4 gap-6 mb-8'>
          <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 text-center shadow-sm border border-gray-100 dark:border-gray-700 transition-colors duration-300'>
            <div className='text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2 transition-colors duration-300'>
              25,847
            </div>
            <div className='text-sm text-gray-600 dark:text-gray-400 transition-colors duration-300'>
              {t('membersCount')}
            </div>
          </div>
          <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 text-center shadow-sm border border-gray-100 dark:border-gray-700 transition-colors duration-300'>
            <div className='text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2 transition-colors duration-300'>
              1,234
            </div>
            <div className='text-sm text-gray-600 dark:text-gray-400 transition-colors duration-300'>
              {t('postsCount')}
            </div>
          </div>
          <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 text-center shadow-sm border border-gray-100 dark:border-gray-700 transition-colors duration-300'>
            <div className='text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2 transition-colors duration-300'>
              5,678
            </div>
            <div className='text-sm text-gray-600 dark:text-gray-400 transition-colors duration-300'>
              {t('commentsCount')}
            </div>
          </div>
          <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 text-center shadow-sm border border-gray-100 dark:border-gray-700 transition-colors duration-300'>
            <div className='text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2 transition-colors duration-300'>
              892
            </div>
            <div className='text-sm text-gray-600 dark:text-gray-400 transition-colors duration-300'>
              {t('onlineNow')}
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className='bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-8 transition-colors duration-300'>
          <div className='flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0'>
            <div className='flex items-center space-x-4'>
              <h3 className='text-lg font-semibold text-gray-900 dark:text-gray-100 transition-colors duration-300'>
                {t('newPost')}
              </h3>
              <button className='inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl'>
                <PlusIcon className='h-4 w-4' />
                <span>{t('newPost')}</span>
              </button>
            </div>

            <div className='flex items-center space-x-4'>
              <div className='flex items-center space-x-2'>
                <span className='text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors duration-300'>
                  {t('sortBy')}:
                </span>
                <select
                  value={selectedSort}
                  onChange={e => setSelectedSort(e.target.value)}
                  className='px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-300'
                >
                  {sortOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className='flex items-center space-x-2'>
                <span className='text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors duration-300'>
                  {t('category')}:
                </span>
                <select
                  value={selectedCategory}
                  onChange={e => setSelectedCategory(e.target.value)}
                  className='px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-300'
                >
                  {categories.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Posts Grid */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
          {posts.map(post => (
            <div
              key={post.id}
              className='bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-100 dark:border-gray-700 hover:border-gray-200 dark:hover:border-gray-600 transition-all duration-300 hover:shadow-lg'
            >
              {/* Header */}
              <div className='flex items-start justify-between mb-4'>
                <div className='flex items-center space-x-3'>
                  <div className='w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center'>
                    <span className='text-sm font-semibold text-gray-600 dark:text-gray-400'>
                      {post.author.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <div className='flex items-center space-x-2'>
                      <h3 className='font-semibold text-gray-900 dark:text-gray-100 transition-colors duration-300'>
                        {post.author.name}
                      </h3>
                      {post.author.verified && (
                        <div className='w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center'>
                          <span className='text-white text-xs'>✓</span>
                        </div>
                      )}
                    </div>
                    <p className='text-sm text-gray-600 dark:text-gray-400 transition-colors duration-300'>
                      {post.author.title}
                    </p>
                  </div>
                </div>
                <div className='flex items-center space-x-2'>
                  {post.trending && (
                    <div className='flex items-center space-x-1 px-2 py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 rounded-full text-xs font-medium transition-colors duration-300'>
                      <FireIcon className='h-3 w-3' />
                      <span>{t('trending')}</span>
                    </div>
                  )}
                  <button className='p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200'>
                    <BookmarkIcon className='h-4 w-4' />
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className='mb-4'>
                <h4 className='text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 transition-colors duration-300'>
                  {post.title}
                </h4>
                <p className='text-gray-600 dark:text-gray-400 leading-relaxed transition-colors duration-300'>
                  {post.content}
                </p>
              </div>

              {/* Tags */}
              <div className='flex flex-wrap gap-2 mb-4'>
                {post.tags.map(tag => (
                  <span
                    key={tag}
                    className='px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-full transition-colors duration-300'
                  >
                    #{tag}
                  </span>
                ))}
              </div>

              {/* Footer */}
              <div className='flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-600 transition-colors duration-300'>
                <div className='flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 transition-colors duration-300'>
                  <div className='flex items-center space-x-1'>
                    <HeartIcon className='h-4 w-4' />
                    <span>{formatNumber(post.likes)}</span>
                  </div>
                  <div className='flex items-center space-x-1'>
                    <ChatBubbleLeftRightIcon className='h-4 w-4' />
                    <span>{formatNumber(post.comments)}</span>
                  </div>
                  <div className='flex items-center space-x-1'>
                    <EyeIcon className='h-4 w-4' />
                    <span>{formatNumber(post.views)}</span>
                  </div>
                </div>
                <div className='flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 transition-colors duration-300'>
                  <ClockIcon className='h-4 w-4' />
                  <span>{formatTimeAgo(post.timeAgo)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Load More */}
        <div className='text-center mt-12'>
          <button className='bg-blue-600 text-white px-8 py-3 rounded-xl font-medium hover:bg-blue-700 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl'>
            {t('loadMorePosts')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AppleCommunityPage;
