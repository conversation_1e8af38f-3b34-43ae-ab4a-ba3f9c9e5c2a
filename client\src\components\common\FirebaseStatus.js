import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { ExclamationTriangleIcon, InformationCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';

const FirebaseStatus = () => {
  const { firebaseReady } = useAuth();
  const { t } = useLanguage();
  const [showDetails, setShowDetails] = useState(false);
  const [errorLogs, setErrorLogs] = useState([]);

  // Capture console errors
  React.useEffect(() => {
    const originalError = console.error;
    console.error = (...args) => {
      originalError.apply(console, args);
      
      // Capture auth-related errors
      if (args[0]?.includes?.('🔍') || args[0]?.includes?.('Auth Error')) {
        setErrorLogs(prev => [...prev, {
          timestamp: new Date().toISOString(),
          message: args.join(' '),
          details: args
        }]);
      }
    };

    return () => {
      console.error = originalError;
    };
  }, []);

  if (firebaseReady) {
    return null; // Don't show anything if Firebase is ready
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 shadow-lg max-w-md">
        <div className="flex items-start space-x-3">
          <ExclamationTriangleIcon className="h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
          <div className="flex-1">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
              Firebase Configuration Issue
            </h3>
            <p className="text-sm text-red-700 dark:text-red-300 mt-1">
              Authentication features are limited. Please check console for setup instructions.
            </p>
            
            {errorLogs.length > 0 && (
              <div className="mt-3">
                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline"
                >
                  {showDetails ? 'Hide' : 'Show'} Error Details ({errorLogs.length})
                </button>
                
                {showDetails && (
                  <div className="mt-2 p-2 bg-red-100 dark:bg-red-900/30 rounded text-xs">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">Recent Errors:</span>
                      <button
                        onClick={() => setErrorLogs([])}
                        className="text-red-600 hover:text-red-800"
                      >
                        Clear
                      </button>
                    </div>
                    {errorLogs.slice(-5).map((log, index) => (
                      <div key={index} className="mb-2 p-2 bg-white dark:bg-gray-800 rounded border">
                        <div className="text-gray-500 text-xs mb-1">
                          {new Date(log.timestamp).toLocaleTimeString()}
                        </div>
                        <div className="text-red-700 dark:text-red-300 break-all">
                          {log.message}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
          <button
            onClick={() => setShowDetails(false)}
            className="text-red-400 hover:text-red-600"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default FirebaseStatus;
