#!/usr/bin/env node

/**
 * Quick Service Status Checker for VWork Render Deployment
 */

const axios = require('axios');

const SERVICES = {
  'Auth Service': 'https://vwork-auth-service.onrender.com/health',
  'User Service': 'https://vwork-user-service.onrender.com/health',
  'Project Service': 'https://vwork-project-service.onrender.com/health',
  'Job Service': 'https://vwork-job-service.onrender.com/health',
  'Chat Service': 'https://vwork-chat-service.onrender.com/health',
  'Search Service': 'https://vwork-search-service.onrender.com/health',
  'API Gateway': 'https://vwork-gateway.onrender.com/health',
  'Client': 'https://vwork-client.onrender.com'
};

const checkService = async (name, url) => {
  try {
    const response = await axios.get(url, { 
      timeout: 10000,
      validateStatus: () => true 
    });
    
    const status = response.status === 200 ? '✅' : '❌';
    const statusText = response.status === 200 ? 'OK' : `Error ${response.status}`;
    
    console.log(`${status} ${name.padEnd(15)} | ${statusText.padEnd(12)} | ${url}`);
    
    return response.status === 200;
  } catch (error) {
    const errorMsg = error.code === 'ECONNABORTED' ? 'Timeout' : 'Connection Error';
    console.log(`⏰ ${name.padEnd(15)} | ${errorMsg.padEnd(12)} | ${url}`);
    return false;
  }
};

const main = async () => {
  console.log('\n🔍 VWork Services Status Check');
  console.log('='.repeat(80));
  console.log('Status'.padEnd(3) + 'Service'.padEnd(15) + ' | Status'.padEnd(14) + ' | URL');
  console.log('-'.repeat(80));
  
  let healthyCount = 0;
  const totalServices = Object.keys(SERVICES).length;
  
  for (const [name, url] of Object.entries(SERVICES)) {
    const isHealthy = await checkService(name, url);
    if (isHealthy) healthyCount++;
  }
  
  console.log('-'.repeat(80));
  console.log(`📊 Summary: ${healthyCount}/${totalServices} services healthy`);
  
  if (healthyCount === totalServices) {
    console.log('🎉 All services are operational!');
    console.log(`\n🌐 Your VWork platform is live at: https://vwork-client.onrender.com`);
  } else {
    console.log('⚠️  Some services need attention. Check Render dashboard for details.');
    console.log('\n💡 Tips:');
    console.log('• Services may take 1-2 minutes to wake up from sleep');
    console.log('• Check Render logs for detailed error information');
    console.log('• Verify environment variables are set correctly');
  }
  
  console.log('\n');
};

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Status check failed:', error.message);
    process.exit(1);
  });
}

module.exports = { checkService, SERVICES };
