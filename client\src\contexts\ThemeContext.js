import { createContext, useContext, useEffect, useState, useCallback } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Load theme preference from localStorage
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme-mode');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
      setIsDarkMode(true);
    }
  }, []);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;
    const body = document.body;

    // Clean up existing theme classes
    root.classList.remove('dark', 'light');
    body.classList.remove('apple-theme-dark', 'apple-theme-light');

    if (isDarkMode) {
      // Apply dark mode
      root.classList.add('dark');
      body.classList.add('apple-theme', 'apple-theme-dark');
      root.classList.add('apple-mode-dark');

      // Set CSS variables for dark theme
      root.style.setProperty('--toast-bg', '#1f2937');
      root.style.setProperty('--toast-color', '#f9fafb');
      root.style.setProperty('--toast-border', '#374151');
    } else {
      // Apply light mode
      root.classList.add('light');
      body.classList.add('apple-theme', 'apple-theme-light');
      root.classList.add('apple-mode-light');

      // Set CSS variables for light theme
      root.style.setProperty('--toast-bg', '#ffffff');
      root.style.setProperty('--toast-color', '#1d1d1f');
      root.style.setProperty('--toast-border', '#e5e7eb');
    }
  }, [isDarkMode]);

  const toggleTheme = useCallback(() => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    localStorage.setItem('theme-mode', newMode ? 'dark' : 'light');
  }, [isDarkMode]);

  const setTheme = useCallback((mode) => {
    const isDark = mode === 'dark';
    setIsDarkMode(isDark);
    localStorage.setItem('theme-mode', mode);
  }, []);

  const value = {
    // Theme state
    isDarkMode,
    theme: isDarkMode ? 'dark' : 'light',

    // Theme actions
    toggleTheme,
    setTheme,

    // Apple theme interface (backward compatibility)
    isApple: true,
    appleMode: true,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
};
