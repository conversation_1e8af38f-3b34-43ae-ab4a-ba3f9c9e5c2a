#!/usr/bin/env node

const { exec } = require('child_process');
const path = require('path');
const os = require('os');

// Cross-platform command execution
function createCrossPlatformProcess(command, options = {}) {
  const platform = os.platform();
  const isWindows = platform === 'win32';
  
  // Normalize path separators
  const normalizedCwd = options.cwd ? path.resolve(options.cwd) : process.cwd();
  
  const execOptions = {
    cwd: normalizedCwd,
    env: { 
      ...process.env, 
      PATH: process.env.PATH,
      // Ensure npm can be found on Windows
      ...(isWindows && { PATHEXT: process.env.PATHEXT || '.COM;.EXE;.BAT;.CMD' })
    },
    shell: true,
    // Only add windowsHide on Windows
    ...(isWindows && { windowsHide: true })
  };
  
  return exec(command, execOptions);
}

function runShellCommand(command, cwd) {
  return new Promise((resolve, reject) => {
    console.log(`🔧 Running: ${command} in ${cwd}`);
    
    const childProcess = createCrossPlatformProcess(command, { cwd });
    
    childProcess.stdout.on('data', (data) => {
      process.stdout.write(data);
    });
    
    childProcess.stderr.on('data', (data) => {
      process.stderr.write(data);
    });
    
    childProcess.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command "${command}" failed with code ${code}`));
      }
    });
    
    childProcess.on('error', (error) => {
      reject(error);
    });
  });
}

async function startClient() {
  try {
    console.log('🚀 Starting VWork Client Only...\n');
    
    const clientDir = path.join(__dirname, '..', 'client');
    
    console.log('🎯 Starting React development server...\n');
    
    await runShellCommand('npm start', clientDir);
    
  } catch (error) {
    console.error('❌ Error starting client:', error.message);
    process.exit(1);
  }
}

startClient();
