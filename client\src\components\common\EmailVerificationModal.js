import React, { useState } from 'react';
import { 
  EnvelopeIcon, 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  ArrowPathIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';

const EmailVerificationModal = ({ 
  isOpen, 
  onClose, 
  email, 
  onResendVerification,
  onContinue 
}) => {
  const { t } = useLanguage();
  const [isResending, setIsResending] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);

  const handleResend = async () => {
    setIsResending(true);
    try {
      await onResendVerification();
      setResendSuccess(true);
      setTimeout(() => setResendSuccess(false), 3000);
    } catch (error) {
      console.error('Resend verification error:', error);
    } finally {
      setIsResending(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
        {/* Header */}
        <div className="relative p-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-white/80 hover:text-white transition-colors"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
          
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-white/20 rounded-full">
              <EnvelopeIcon className="w-6 h-6" />
            </div>
            <div>
              <h3 className="text-xl font-semibold">Xác thực Email</h3>
              <p className="text-blue-100 text-sm">Hoàn thành đăng ký tài khoản</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Success Icon */}
          <div className="flex justify-center">
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
              <CheckCircleIcon className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
          </div>

          {/* Main Message */}
          <div className="text-center space-y-2">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              Tài khoản đã được tạo thành công!
            </h4>
            <p className="text-gray-600 dark:text-gray-300">
              Chúng tôi đã gửi email xác thực đến:
            </p>
            <p className="font-medium text-blue-600 dark:text-blue-400 break-all">
              {email}
            </p>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 space-y-2">
            <div className="flex items-start space-x-2">
              <ExclamationTriangleIcon className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800 dark:text-blue-200">
                <p className="font-medium">Lưu ý quan trọng:</p>
                <ul className="mt-1 space-y-1 text-xs">
                  <li>• Kiểm tra cả thư mục spam/junk</li>
                  <li>• Email có thể mất vài phút để đến</li>
                  <li>• Click vào link trong email để xác thực</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Resend Section */}
          <div className="border-t pt-4">
            <div className="text-center space-y-3">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Chưa nhận được email?
              </p>
              
              <button
                onClick={handleResend}
                disabled={isResending}
                className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                {isResending ? (
                  <ArrowPathIcon className="w-4 h-4 animate-spin" />
                ) : (
                  <EnvelopeIcon className="w-4 h-4" />
                )}
                <span>
                  {isResending ? 'Đang gửi...' : 'Gửi lại email'}
                </span>
              </button>

              {resendSuccess && (
                <div className="flex items-center justify-center space-x-2 text-green-600 dark:text-green-400 text-sm">
                  <CheckCircleIcon className="w-4 h-4" />
                  <span>Email đã được gửi lại!</span>
                </div>
              )}
            </div>
          </div>

          {/* Continue Button */}
          <div className="pt-4">
            <button
              onClick={onContinue}
              className="w-full py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg transition-all transform hover:scale-105"
            >
              Tiếp tục
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailVerificationModal; 