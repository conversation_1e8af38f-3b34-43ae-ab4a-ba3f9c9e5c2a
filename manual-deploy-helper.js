#!/usr/bin/env node

// Script hướng dẫn deploy manual lên Render
console.log('🚀 VWork Manual Deploy Guide');
console.log('===============================\n');

const services = [
  {
    name: 'vwork-frontend',
    type: 'Static Site',
    rootDirectory: 'client',
    buildCommand: 'npm install && npm run build',
    publishDirectory: 'build',
    envVars: {
      'NODE_ENV': 'production',
      'REACT_APP_API_URL': 'https://vwork-api-gateway.onrender.com',
      'REACT_APP_FIREBASE_API_KEY': 'AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw',
      'REACT_APP_FIREBASE_AUTH_DOMAIN': 'vwork-786c3.firebaseapp.com',
      'REACT_APP_FIREBASE_PROJECT_ID': 'vwork-786c3',
      'REACT_APP_FIREBASE_STORAGE_BUCKET': 'vwork-786c3.firebasestorage.app'
    }
  },
  {
    name: 'vwork-api-gateway',
    type: 'Web Service',
    rootDirectory: 'services/api-gateway',
    buildCommand: 'npm install',
    startCommand: 'npm start',
    envVars: {
      'NODE_ENV': 'production',
      'PORT': '10000',
      'AUTH_SERVICE_URL': 'https://vwork-auth-service.onrender.com',
      'USER_SERVICE_URL': 'https://vwork-user-service.onrender.com',
      'PROJECT_SERVICE_URL': 'https://vwork-project-service.onrender.com',
      'CHAT_SERVICE_URL': 'https://vwork-chat-service.onrender.com',
      'PAYMENT_SERVICE_URL': 'https://vwork-payment-service.onrender.com',
      'CORS_ORIGIN': 'https://vwork-frontend.onrender.com',
      'JWT_SECRET': 'vwork-super-secret-jwt-key-2024',
      'FIREBASE_PROJECT_ID': 'vwork-786c3'
    }
  },
  {
    name: 'vwork-auth-service',
    type: 'Web Service',
    rootDirectory: 'services/auth-service',
    buildCommand: 'npm install',
    startCommand: 'npm start',
    envVars: {
      'NODE_ENV': 'production',
      'PORT': '10000',
      'JWT_SECRET': 'vwork-super-secret-jwt-key-2024',
      'FIREBASE_PROJECT_ID': 'vwork-786c3'
    }
  },
  {
    name: 'vwork-user-service',
    type: 'Web Service',
    rootDirectory: 'services/user-service',
    buildCommand: 'npm install',
    startCommand: 'npm start',
    envVars: {
      'NODE_ENV': 'production',
      'PORT': '10000',
      'FIREBASE_PROJECT_ID': 'vwork-786c3'
    }
  },
  {
    name: 'vwork-project-service',
    type: 'Web Service',
    rootDirectory: 'services/project-service',
    buildCommand: 'npm install',
    startCommand: 'npm start',
    envVars: {
      'NODE_ENV': 'production',
      'PORT': '10000',
      'FIREBASE_PROJECT_ID': 'vwork-786c3'
    }
  },
  {
    name: 'vwork-chat-service',
    type: 'Web Service',
    rootDirectory: 'services/chat-service',
    buildCommand: 'npm install',
    startCommand: 'npm start',
    envVars: {
      'NODE_ENV': 'production',
      'PORT': '10000',
      'FIREBASE_PROJECT_ID': 'vwork-786c3'
    }
  },
  {
    name: 'vwork-payment-service',
    type: 'Web Service',
    rootDirectory: 'services/payment-service',
    buildCommand: 'npm install',
    startCommand: 'npm start',
    envVars: {
      'NODE_ENV': 'production',
      'PORT': '10000',
      'FIREBASE_PROJECT_ID': 'vwork-786c3'
    }
  }
];

console.log('📋 Danh sách services cần tạo trên Render:\n');

services.forEach((service, index) => {
  console.log(`${index + 1}. ${service.name} (${service.type})`);
  console.log(`   Root Directory: ${service.rootDirectory}`);
  if (service.buildCommand) {
    console.log(`   Build Command: ${service.buildCommand}`);
  }
  if (service.startCommand) {
    console.log(`   Start Command: ${service.startCommand}`);
  }
  if (service.publishDirectory) {
    console.log(`   Publish Directory: ${service.publishDirectory}`);
  }
  
  console.log('\n   Environment Variables:');
  Object.entries(service.envVars).forEach(([key, value]) => {
    console.log(`   ${key}=${value}`);
  });
  console.log('\n' + '─'.repeat(50) + '\n');
});

console.log('🔧 Các bước thực hiện:');
console.log('1. Truy cập https://dashboard.render.com');
console.log('2. Đăng nhập với GitHub account');
console.log('3. Tạo từng service theo thứ tự ở trên');
console.log('4. Copy/paste environment variables cho từng service');
console.log('5. Deploy và monitor logs\n');

console.log('💡 Khuyến nghị:');
console.log('- Bắt đầu với Frontend + API Gateway (2 services)');
console.log('- Test trước khi deploy thêm services khác');
console.log('- Free tier có giới hạn: services sẽ sleep sau 15 phút không hoạt động');
console.log('- Static Site (Frontend) FREE vĩnh viễn\n');

console.log('🔗 URLs sau khi deploy:');
console.log('- Frontend: https://vwork-frontend.onrender.com');
console.log('- API Gateway: https://vwork-api-gateway.onrender.com');
console.log('- Health Checks: https://service-name.onrender.com/health\n');

console.log('✅ Sẵn sàng deploy manual lên Render!');
