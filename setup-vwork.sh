#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 VWork Complete System Setup${NC}"
echo -e "${BLUE}==============================${NC}"
echo

# Check if Node.js is installed
echo -e "${YELLOW}🔍 Checking system requirements...${NC}"
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js is not installed!${NC}"
    echo -e "${YELLOW}📥 Please download and install Node.js from: https://nodejs.org/${NC}"
    echo -e "${YELLOW}📋 Required: Node.js 16+ and npm 8+${NC}"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm is not installed!${NC}"
    echo -e "${YELLOW}📥 Please install npm with Node.js${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js and npm are installed${NC}"
echo

# Step 1: Install root dependencies
echo -e "${YELLOW}📦 Step 1: Installing root dependencies...${NC}"
npm install
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Root npm install failed${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Root dependencies installed${NC}"
echo

# Step 2: Setup client
echo -e "${YELLOW}🎨 Step 2: Setting up client...${NC}"
if [ -d "client" ]; then
    cd client
    echo -e "${YELLOW}📦 Installing client dependencies...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Client npm install failed${NC}"
        cd ..
        exit 1
    fi
    echo -e "${GREEN}✅ Client dependencies installed${NC}"
    
    echo -e "${YELLOW}🔧 Setting up client environment...${NC}"
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            echo -e "${GREEN}✅ Client environment file created${NC}"
        else
            echo -e "${YELLOW}⚠️ No .env.example found, creating basic .env${NC}"
            echo "REACT_APP_API_URL=http://localhost:8080" > .env
            echo "REACT_APP_SOCKET_URL=http://localhost:8080" >> .env
        fi
    else
        echo -e "${YELLOW}ℹ️ Client .env file already exists${NC}"
    fi
    cd ..
else
    echo -e "${YELLOW}⚠️ Client directory not found, skipping client setup${NC}"
fi
echo -e "${GREEN}✅ Client setup completed${NC}"
echo

# Step 3: Setup server
echo -e "${YELLOW}🏰 Step 3: Setting up server...${NC}"
if [ -d "server" ]; then
    cd server
    echo -e "${YELLOW}📦 Installing server dependencies...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Server npm install failed${NC}"
        cd ..
        exit 1
    fi
    echo -e "${GREEN}✅ Server dependencies installed${NC}"
    
    echo -e "${YELLOW}🗄️ Installing databases...${NC}"
    npm run setup:db
    if [ $? -ne 0 ]; then
        echo -e "${YELLOW}⚠️ Database installation failed, continuing with manual setup...${NC}"
    fi
    echo -e "${GREEN}✅ Database installation completed${NC}"
    
    echo -e "${YELLOW}🔧 Setting up server environment...${NC}"
    if [ ! -f ".env" ]; then
        npm run setup
        if [ $? -ne 0 ]; then
            echo -e "${RED}❌ Server environment setup failed${NC}"
            cd ..
            exit 1
        fi
    else
        echo -e "${YELLOW}ℹ️ Server .env file already exists${NC}"
    fi
    echo -e "${GREEN}✅ Server environment configured${NC}"
    
    echo -e "${YELLOW}📊 Setting up database schema...${NC}"
    npm run setup:manual
    if [ $? -ne 0 ]; then
        echo -e "${YELLOW}⚠️ Database schema setup failed, trying manual setup...${NC}"
        node scripts/setupDatabase.js setup
    fi
    echo -e "${GREEN}✅ Database schema created${NC}"
    
    echo -e "${YELLOW}🌱 Seeding database...${NC}"
    npm run db:seed
    if [ $? -ne 0 ]; then
        echo -e "${YELLOW}⚠️ Database seeding failed, trying manual seeding...${NC}"
        node scripts/setupDatabase.js seed
    fi
    echo -e "${GREEN}✅ Database seeded${NC}"
    
    cd ..
else
    echo -e "${RED}❌ Server directory not found!${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Server setup completed${NC}"
echo

# Step 4: Create start scripts
echo -e "${YELLOW}🚀 Step 4: Creating start scripts...${NC}"

# Create start-vwork.sh
cat > start-vwork.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting VWork System..."
echo

echo "📦 Starting server..."
cd server && npm run dev &
SERVER_PID=$!

echo "⏳ Waiting for server to start..."
sleep 10

echo "🎨 Starting client..."
cd client && npm start &
CLIENT_PID=$!

echo
echo "✅ VWork system started!"
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend: http://localhost:8080"
echo

# Wait for user to stop
echo "Press Ctrl+C to stop all services..."
trap "echo 'Stopping services...'; kill $SERVER_PID $CLIENT_PID; exit" INT
wait
EOF

# Create start-server.sh
cat > start-server.sh << 'EOF'
#!/bin/bash
echo "🏰 Starting VWork Server..."
cd server
npm run dev
EOF

# Create start-client.sh
cat > start-client.sh << 'EOF'
#!/bin/bash
echo "🎨 Starting VWork Client..."
cd client
npm start
EOF

# Make scripts executable
chmod +x start-vwork.sh start-server.sh start-client.sh

echo -e "${GREEN}✅ Start scripts created${NC}"
echo

# Step 5: Create environment file
echo -e "${YELLOW}🔧 Step 5: Creating root environment file...${NC}"
if [ ! -f ".env" ]; then
    cat > .env << 'EOF'
# VWork Root Environment
# Generated on $(date)

# Client Settings
REACT_APP_API_URL=http://localhost:8080
REACT_APP_SOCKET_URL=http://localhost:8080

# Server Settings
NODE_ENV=development
PORT=8080
CORS_ORIGIN=http://localhost:3000

# Database Settings
DB_HOST=localhost
DB_PORT=5432
DB_NAME=vwork_db
DB_USER=vwork_user
DB_PASSWORD=vwork_password
MONGODB_URI=mongodb://localhost:27017/vwork_social
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT Settings
JWT_SECRET=vwork_jwt_secret_$(date +%s)
JWT_EXPIRE=7d

# Firebase Settings (Configure later)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=your-service-account-email
FIREBASE_PRIVATE_KEY="your-private-key"

# Email Settings (Configure later)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_EMAIL=<EMAIL>
SMTP_PASSWORD=your-app-password
EOF
    echo -e "${GREEN}✅ Root environment file created${NC}"
else
    echo -e "${YELLOW}ℹ️ Root .env file already exists${NC}"
fi
echo

# Step 6: Test installation
echo -e "${YELLOW}🧪 Step 6: Testing installation...${NC}"
echo -e "${YELLOW}📊 Testing database connections...${NC}"
cd server
node -e "
const { testConnection } = require('./config/database');
const { testMongoConnection } = require('./config/mongodb');

Promise.all([testConnection(), testMongoConnection()])
  .then(([pg, mongo]) => {
    console.log('✅ PostgreSQL:', pg.postgres ? 'Connected' : 'Failed');
    console.log('✅ MongoDB:', mongo ? 'Connected' : 'Failed');
    process.exit(pg.postgres && mongo ? 0 : 1);
  })
  .catch(err => {
    console.log('❌ Connection test failed:', err.message);
    process.exit(1);
  });
"
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}⚠️ Database connection test failed, but continuing...${NC}"
else
    echo -e "${GREEN}✅ Database connections successful${NC}"
fi
cd ..
echo

# Step 7: Create documentation
echo -e "${YELLOW}📚 Step 7: Creating documentation...${NC}"
cat > VWORK_GUIDE.md << 'EOF'
# VWork System Documentation

## 🚀 Quick Start

### Start the entire system:
```bash
./start-vwork.sh
```

### Start server only:
```bash
./start-server.sh
```

### Start client only:
```bash
./start-client.sh
```

## 📱 Access URLs
- Frontend: http://localhost:3000
- Backend: http://localhost:8080
- API Health: http://localhost:8080/health

## 🗄️ Database Status
- PostgreSQL: localhost:5432
- MongoDB: localhost:27017
- Redis: localhost:6379

## 🔧 Troubleshooting
- Reset database: `cd server && npm run db:reset`
- Reinstall: Run this script again
- Check logs: `cd server && npm run dev`
EOF
echo -e "${GREEN}✅ Documentation created${NC}"
echo

# Final success message
echo -e "${GREEN}🎉 VWork System Setup Completed Successfully!${NC}"
echo
echo -e "${YELLOW}📋 Next steps:${NC}"
echo "1. Start the system: ./start-vwork.sh"
echo "2. Or start individually:"
echo "   - Server: ./start-server.sh"
echo "   - Client: ./start-client.sh"
echo
echo -e "${YELLOW}📱 Access URLs:${NC}"
echo "- Frontend: http://localhost:3000"
echo "- Backend: http://localhost:8080"
echo "- API Health: http://localhost:8080/health"
echo
echo -e "${YELLOW}📚 Documentation: VWORK_GUIDE.md${NC}"
echo
echo -e "${YELLOW}🔧 If you encounter issues:${NC}"
echo "- Check the logs in server/client directories"
echo "- Reset database: cd server && npm run db:reset"
echo "- Reinstall: Run this script again"
echo 