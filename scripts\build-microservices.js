#!/usr/bin/env node

/**
 * VWork Microservices Build Script
 * Supports building individual services or the entire platform
 * Usage: node scripts/build-microservices.js [service-name|all] [--production]
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Service configuration
const SERVICES = {
  'api-gateway': {
    path: './services/api-gateway',
    name: 'API Gateway',
    port: 8080,
    dependencies: ['shared']
  },
  'auth-service': {
    path: './services/auth-service',
    name: 'Authentication Service',
    port: 3001,
    dependencies: ['shared']
  },
  'user-service': {
    path: './services/user-service',
    name: 'User Service',
    port: 3002,
    dependencies: ['shared']
  },
  'project-service': {
    path: './services/project-service',
    name: 'Project Service',
    port: 3003,
    dependencies: ['shared']
  },
  'job-service': {
    path: './services/job-service',
    name: 'Job Service',
    port: 3004,
    dependencies: ['shared']
  },
  'chat-service': {
    path: './services/chat-service',
    name: 'Chat Service',
    port: 3005,
    dependencies: ['shared']
  },
  'payment-service': {
    path: './services/payment-service',
    name: 'Payment Service',
    port: 3006,
    dependencies: ['shared']
  },
  'shared': {
    path: './services/shared',
    name: 'Shared Utilities',
    port: null,
    dependencies: []
  },
  'client': {
    path: './client',
    name: 'React Frontend',
    port: 3000,
    dependencies: []
  }
};

class MicroserviceBuilder {
  constructor() {
    this.args = process.argv.slice(2);
    this.target = this.args[0] || 'all';
    this.isProduction = this.args.includes('--production');
    this.verbose = this.args.includes('--verbose');
    this.buildResults = {};
  }

  log(message, color = 'reset') {
    const timestamp = new Date().toISOString().substring(11, 19);
    console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
  }

  error(message) {
    this.log(`❌ ERROR: ${message}`, 'red');
  }

  success(message) {
    this.log(`✅ ${message}`, 'green');
  }

  warn(message) {
    this.log(`⚠️  ${message}`, 'yellow');
  }

  info(message) {
    this.log(`ℹ️  ${message}`, 'blue');
  }

  async execCommand(command, cwd, serviceName) {
    return new Promise((resolve, reject) => {
      if (this.verbose) {
        this.info(`Executing: ${command} in ${cwd}`);
      }

      const child = spawn(command, { 
        shell: true, 
        cwd,
        stdio: this.verbose ? 'inherit' : 'pipe'
      });

      let stdout = '';
      let stderr = '';

      if (!this.verbose) {
        child.stdout?.on('data', (data) => {
          stdout += data.toString();
        });

        child.stderr?.on('data', (data) => {
          stderr += data.toString();
        });
      }

      child.on('close', (code) => {
        if (code === 0) {
          resolve({ stdout, stderr });
        } else {
          reject(new Error(`Command failed with code ${code}: ${stderr || stdout}`));
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  async buildService(serviceKey) {
    const service = SERVICES[serviceKey];
    if (!service) {
      throw new Error(`Unknown service: ${serviceKey}`);
    }

    this.log(`🔨 Building ${service.name}...`, 'cyan');

    // Check if service directory exists
    if (!fs.existsSync(service.path)) {
      throw new Error(`Service directory not found: ${service.path}`);
    }

    const packageJsonPath = path.join(service.path, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      throw new Error(`package.json not found in ${service.path}`);
    }

    try {
      // Install dependencies
      this.info(`Installing dependencies for ${service.name}...`);
      await this.execCommand('npm install', service.path, serviceKey);

      // Run build command
      this.info(`Running build for ${service.name}...`);
      if (this.isProduction) {
        await this.execCommand('npm ci --omit=dev', service.path, serviceKey);
      } else {
        await this.execCommand('npm run build', service.path, serviceKey);
      }

      this.success(`${service.name} built successfully`);
      this.buildResults[serviceKey] = { status: 'success', error: null };

    } catch (error) {
      this.error(`Failed to build ${service.name}: ${error.message}`);
      this.buildResults[serviceKey] = { status: 'failed', error: error.message };
      throw error;
    }
  }

  async buildDependencies(serviceKey) {
    const service = SERVICES[serviceKey];
    if (!service || !service.dependencies.length) {
      return;
    }

    this.info(`Building dependencies for ${service.name}: ${service.dependencies.join(', ')}`);
    
    for (const dep of service.dependencies) {
      if (!this.buildResults[dep] || this.buildResults[dep].status !== 'success') {
        await this.buildService(dep);
      }
    }
  }

  async buildAll() {
    this.log('🚀 Building all VWork services...', 'bright');
    
    // Build order: shared first, then services, then client
    const buildOrder = ['shared', 'api-gateway', 'auth-service', 'user-service', 
                       'project-service', 'job-service', 'chat-service', 'payment-service', 'client'];
    
    for (const serviceKey of buildOrder) {
      try {
        await this.buildService(serviceKey);
      } catch (error) {
        this.error(`Build failed for ${serviceKey}, continuing with other services...`);
      }
    }
  }

  async buildSingle(serviceKey) {
    this.log(`🎯 Building single service: ${SERVICES[serviceKey]?.name || serviceKey}`, 'bright');
    
    // Build dependencies first
    await this.buildDependencies(serviceKey);
    
    // Build the target service
    await this.buildService(serviceKey);
  }

  printSummary() {
    this.log('\n📊 Build Summary:', 'bright');
    console.log('='.repeat(50));
    
    let successCount = 0;
    let failedCount = 0;
    
    for (const [service, result] of Object.entries(this.buildResults)) {
      const serviceName = SERVICES[service]?.name || service;
      if (result.status === 'success') {
        console.log(`${colors.green}✅ ${serviceName}${colors.reset}`);
        successCount++;
      } else {
        console.log(`${colors.red}❌ ${serviceName}: ${result.error}${colors.reset}`);
        failedCount++;
      }
    }
    
    console.log('='.repeat(50));
    this.log(`Total: ${successCount + failedCount}, Success: ${successCount}, Failed: ${failedCount}`, 
              failedCount > 0 ? 'yellow' : 'green');
  }

  async run() {
    try {
      this.log('🏗️  VWork Microservices Builder', 'bright');
      this.log(`Target: ${this.target}`, 'blue');
      this.log(`Production mode: ${this.isProduction}`, 'blue');
      this.log(`Verbose: ${this.verbose}`, 'blue');
      console.log();

      if (this.target === 'all') {
        await this.buildAll();
      } else if (SERVICES[this.target]) {
        await this.buildSingle(this.target);
      } else {
        throw new Error(`Unknown target: ${this.target}. Available: ${Object.keys(SERVICES).join(', ')}, all`);
      }

      this.printSummary();
      
      const failedCount = Object.values(this.buildResults).filter(r => r.status === 'failed').length;
      process.exit(failedCount > 0 ? 1 : 0);

    } catch (error) {
      this.error(`Build process failed: ${error.message}`);
      process.exit(1);
    }
  }
}

// Show help if requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
VWork Microservices Builder

Usage: node scripts/build-microservices.js [target] [options]

Targets:
  all                 Build all services (default)
  ${Object.keys(SERVICES).join('\n  ')}

Options:
  --production        Build for production (use npm ci --omit=dev)
  --verbose           Show detailed output
  --help, -h          Show this help

Examples:
  node scripts/build-microservices.js all
  node scripts/build-microservices.js auth-service --production
  node scripts/build-microservices.js client --verbose
`);
  process.exit(0);
}

// Run the builder
const builder = new MicroserviceBuilder();
builder.run();
