/**
 * Responsive Autoscaling Service for VWork Frontend
 * Implements client-side performance optimization and adaptive scaling
 */

import { gsap } from 'gsap';

class ResponsiveAutoscalingService {
  constructor() {
    this.deviceCapabilities = this.detectDeviceCapabilities();
    this.performanceMetrics = {
      frameRate: 60,
      memoryUsage: 0,
      loadTime: 0,
      animationPerformance: 'high'
    };

    this.scalingConfig = {
      animations: {
        high: { duration: 1, ease: 'power3.out', stagger: 0.1 },
        medium: { duration: 0.7, ease: 'power2.out', stagger: 0.05 },
        low: { duration: 0.3, ease: 'power1.out', stagger: 0.02 },
        minimal: { duration: 0.1, ease: 'none', stagger: 0 }
      },
      components: {
        high: { lazyLoad: false, preloadImages: true, fullAnimations: true },
        medium: { lazyLoad: true, preloadImages: false, fullAnimations: true },
        low: { lazyLoad: true, preloadImages: false, fullAnimations: false },
        minimal: { lazyLoad: true, preloadImages: false, fullAnimations: false }
      }
    };

    // Monitoring state
    this.isMonitoring = false;
    this.memoryInterval = null;
    this.networkChangeHandler = null;
    this.resizeHandler = null;
    this.frameId = null;

    this.currentPerformanceLevel = this.determinePerformanceLevel();
    this.startMonitoring();
  }

  // Detect device capabilities
  detectDeviceCapabilities() {
    const userAgent = navigator.userAgent.toLowerCase();
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    const deviceInfo = this.analyzeDevice(userAgent, screenWidth, hasTouch);
    
    const capabilities = {
      isMobile: deviceInfo.type === 'mobile',
      isDesktop: deviceInfo.type === 'desktop',
      hasTouch: hasTouch,
      deviceMemory: navigator.deviceMemory || 4, // GB
      hardwareConcurrency: navigator.hardwareConcurrency || 4,
      connectionType: this.getConnectionType(),
      pixelRatio: window.devicePixelRatio || 1,
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
      // Additional info for debugging
      userAgent: userAgent.substring(0, 100),
      screenSize: `${screenWidth}x${screenHeight}`,
      detectedAs: deviceInfo.type,
      isDesktopBrowser: deviceInfo.isDesktopBrowser,
      detectionMethod: deviceInfo.method
    };

    return capabilities;
  }

  // Simplified device detection - Desktop or Mobile only (no tablet)
  analyzeDevice(userAgent, screenWidth, hasTouch) {
    
    // Real mobile devices detection (highest priority)
    const isRealMobile = /android.*mobile|webos|iphone|ipod|blackberry|iemobile|opera mini|mobile safari/i.test(userAgent);
    
    // Desktop OS detection
    const isWindowsDesktop = /windows nt/i.test(userAgent) && !/windows phone|windows mobile/i.test(userAgent);
    const isMacDesktop = /macintosh|mac os x/i.test(userAgent) && !/mobile/i.test(userAgent);
    const isLinuxDesktop = /linux/i.test(userAgent) && !/android|mobile/i.test(userAgent);
    const isDesktopOS = isWindowsDesktop || isMacDesktop || isLinuxDesktop;
    
    // Desktop browser detection
    const isDesktopBrowser = /edge|edg\/|chrome|firefox|safari/i.test(userAgent) && !/mobile/i.test(userAgent);
    
    // Simplified detection logic: Desktop or Mobile only
    
    // 1. Real mobile devices = Mobile
    if (isRealMobile) {
      return { type: 'mobile', method: 'userAgent-mobile', isDesktopBrowser: false };
    }
    
    // 2. Desktop OS + Desktop Browser = Desktop (even with touch)
    if (isDesktopOS && isDesktopBrowser) {
      return { type: 'desktop', method: 'desktop-os-browser', isDesktopBrowser: true };
    }
    
    // 3. Desktop OS (even without desktop browser) = Desktop
    if (isDesktopOS) {
      return { type: 'desktop', method: 'desktop-os', isDesktopBrowser: isDesktopBrowser };
    }
    
    // 4. Screen size fallback
    if (screenWidth >= 768) {
      return { type: 'desktop', method: 'screen-desktop-fallback', isDesktopBrowser: false };
    } else {
      return { type: 'mobile', method: 'screen-mobile-fallback', isDesktopBrowser: false };
    }
  }

  // Get connection type
  getConnectionType() {
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    if (!connection) return 'unknown';
    
    const effectiveType = connection.effectiveType;
    const downlink = connection.downlink;
    
    if (effectiveType === '4g' && downlink > 10) return 'fast';
    if (effectiveType === '4g' || (effectiveType === '3g' && downlink > 1.5)) return 'medium';
    return 'slow';
  }

  // Determine performance level based on device capabilities
  determinePerformanceLevel() {
    const { deviceMemory, hardwareConcurrency, connectionType, isMobile, reducedMotion } = this.deviceCapabilities;
    
    if (reducedMotion) return 'minimal';
    
    let score = 0;
    
    // Memory score (0-3)
    if (deviceMemory >= 8) score += 3;
    else if (deviceMemory >= 4) score += 2;
    else if (deviceMemory >= 2) score += 1;
    
    // CPU score (0-3)
    if (hardwareConcurrency >= 8) score += 3;
    else if (hardwareConcurrency >= 4) score += 2;
    else if (hardwareConcurrency >= 2) score += 1;
    
    // Connection score (0-2)
    if (connectionType === 'fast') score += 2;
    else if (connectionType === 'medium') score += 1;
    
    // Device type penalty
    if (isMobile) score -= 2;
    
    // Determine level
    if (score >= 7) return 'high';
    if (score >= 4) return 'medium';
    if (score >= 2) return 'low';
    return 'minimal';
  }

  // Start performance monitoring
  startMonitoring() {
    // Prevent multiple monitoring instances
    if (this.isMonitoring) {
      return;
    }
    this.isMonitoring = true;

    // Monitor frame rate
    this.monitorFrameRate();

    // Monitor memory usage with cleanup tracking
    this.memoryInterval = setInterval(() => {
      this.monitorMemoryUsage();
    }, 5000);

    // Monitor network conditions with cleanup tracking
    if (navigator.connection) {
      this.networkChangeHandler = () => {
        this.handleNetworkChange();
      };
      navigator.connection.addEventListener('change', this.networkChangeHandler);
    }

    // Monitor window resize with cleanup tracking
    this.resizeHandler = () => {
      this.handleResize();
    };
    window.addEventListener('resize', this.resizeHandler);
  }

  // Stop performance monitoring and cleanup
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }
    this.isMonitoring = false;

    // Clear memory monitoring interval
    if (this.memoryInterval) {
      clearInterval(this.memoryInterval);
      this.memoryInterval = null;
    }

    // Remove network change listener
    if (navigator.connection && this.networkChangeHandler) {
      navigator.connection.removeEventListener('change', this.networkChangeHandler);
      this.networkChangeHandler = null;
    }

    // Remove resize listener
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler);
      this.resizeHandler = null;
    }

    // Clear frame monitoring
    if (this.frameId) {
      cancelAnimationFrame(this.frameId);
      this.frameId = null;
    }
  }

  // Monitor frame rate
  monitorFrameRate() {
    let frames = 0;
    let lastTime = performance.now();

    const countFrames = () => {
      if (!this.isMonitoring) {
        return; // Stop monitoring if disabled
      }

      frames++;
      const currentTime = performance.now();

      if (currentTime - lastTime >= 1000) {
        this.performanceMetrics.frameRate = frames;
        frames = 0;
        lastTime = currentTime;

        // Adjust performance level if needed
        this.adjustPerformanceLevel();
      }

      this.frameId = requestAnimationFrame(countFrames);
    };

    this.frameId = requestAnimationFrame(countFrames);
  }

  // Monitor memory usage
  monitorMemoryUsage() {
    if (performance.memory) {
      const memoryUsage = (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100;
      this.performanceMetrics.memoryUsage = memoryUsage;
      
      console.log(`📊 Memory Usage: ${memoryUsage.toFixed(2)}%`);
      
      // Trigger cleanup if memory usage is high
      if (memoryUsage > 80) {
        this.triggerMemoryCleanup();
      }
    }
  }

  // Adjust performance level based on metrics
  adjustPerformanceLevel() {
    const { frameRate, memoryUsage } = this.performanceMetrics;
    let newLevel = this.currentPerformanceLevel;
    
    // Downgrade if performance is poor
    if (frameRate < 30 || memoryUsage > 70) {
      if (this.currentPerformanceLevel === 'high') newLevel = 'medium';
      else if (this.currentPerformanceLevel === 'medium') newLevel = 'low';
      else if (this.currentPerformanceLevel === 'low') newLevel = 'minimal';
    }
    
    // Upgrade if performance is good
    else if (frameRate > 55 && memoryUsage < 50) {
      if (this.currentPerformanceLevel === 'minimal') newLevel = 'low';
      else if (this.currentPerformanceLevel === 'low') newLevel = 'medium';
      else if (this.currentPerformanceLevel === 'medium') newLevel = 'high';
    }
    
    if (newLevel !== this.currentPerformanceLevel) {
      console.log(`🔄 Performance level changed: ${this.currentPerformanceLevel} → ${newLevel}`);
      this.currentPerformanceLevel = newLevel;
      this.notifyPerformanceLevelChange(newLevel);
    }
  }

  // Handle network change
  handleNetworkChange() {
    const newConnectionType = this.getConnectionType();
    
    if (newConnectionType !== this.deviceCapabilities.connectionType) {
      console.log(`🌐 Network changed: ${this.deviceCapabilities.connectionType} → ${newConnectionType}`);
      this.deviceCapabilities.connectionType = newConnectionType;
      
      // Recalculate performance level
      const newLevel = this.determinePerformanceLevel();
      if (newLevel !== this.currentPerformanceLevel) {
        this.currentPerformanceLevel = newLevel;
        this.notifyPerformanceLevelChange(newLevel);
      }
    }
  }

  // Handle window resize
  handleResize() {
    const wasDesktop = this.deviceCapabilities.isDesktop;
    const wasMobile = this.deviceCapabilities.isMobile;
    
    // Re-analyze device with new screen dimensions using simplified logic
    const userAgent = navigator.userAgent.toLowerCase();
    const screenWidth = window.innerWidth;
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    const deviceInfo = this.analyzeDevice(userAgent, screenWidth, hasTouch);
    
    // Update device capabilities with new analysis (Desktop or Mobile only)
    this.deviceCapabilities.isMobile = deviceInfo.type === 'mobile';
    this.deviceCapabilities.isDesktop = deviceInfo.type === 'desktop';
    this.deviceCapabilities.screenSize = `${screenWidth}x${window.innerHeight}`;
    this.deviceCapabilities.detectedAs = deviceInfo.type;
    this.deviceCapabilities.detectionMethod = deviceInfo.method;
    

    
    // If device type changed, recalculate performance level
    if (wasDesktop !== this.deviceCapabilities.isDesktop ||
        wasMobile !== this.deviceCapabilities.isMobile) {
      
      const newLevel = this.determinePerformanceLevel();
      if (newLevel !== this.currentPerformanceLevel) {
        this.currentPerformanceLevel = newLevel;
        this.notifyPerformanceLevelChange(newLevel);
      }
    }
  }

  // Trigger memory cleanup
  triggerMemoryCleanup() {
    console.log('🧹 Triggering memory cleanup...');
    
    // Kill unnecessary GSAP timelines
    gsap.globalTimeline.getChildren().forEach(tl => {
      if (tl.progress() === 1) {
        tl.kill();
      }
    });
    
    // Clear image caches if possible
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          if (name.includes('images')) {
            caches.delete(name);
          }
        });
      });
    }
    
    // Notify components to cleanup
    window.dispatchEvent(new CustomEvent('memoryCleanup'));
  }

  // Notify components of performance level change
  notifyPerformanceLevelChange(newLevel) {
    window.dispatchEvent(new CustomEvent('performanceLevelChange', {
      detail: { level: newLevel, config: this.getConfigForLevel(newLevel) }
    }));
  }

  // Get configuration for performance level
  getConfigForLevel(level) {
    return {
      animations: this.scalingConfig.animations[level],
      components: this.scalingConfig.components[level]
    };
  }

  // Get current performance configuration
  getCurrentConfig() {
    return this.getConfigForLevel(this.currentPerformanceLevel);
  }

  // Get optimized GSAP settings
  getOptimizedGSAPSettings() {
    const config = this.getCurrentConfig();
    
    return {
      ...config.animations,
      force3D: this.deviceCapabilities.isDesktop,
      autoAlpha: this.currentPerformanceLevel !== 'minimal',
      transformOrigin: this.currentPerformanceLevel === 'high' ? 'center center' : '50% 50%'
    };
  }

  // Check if component should be lazy loaded
  shouldLazyLoad() {
    return this.getCurrentConfig().components.lazyLoad;
  }

  // Check if images should be preloaded
  shouldPreloadImages() {
    return this.getCurrentConfig().components.preloadImages;
  }

  // Check if full animations should be enabled
  shouldUseFullAnimations() {
    return this.getCurrentConfig().components.fullAnimations;
  }

  // Get device info for debugging
  getDeviceInfo() {
    return {
      capabilities: this.deviceCapabilities,
      performanceLevel: this.currentPerformanceLevel,
      metrics: this.performanceMetrics,
      config: this.getCurrentConfig()
    };
  }

  // Cleanup method for proper shutdown
  cleanup() {
    console.log('🧹 ResponsiveAutoscalingService: Cleaning up...');
    this.stopMonitoring();
  }
}

// Create singleton instance
const responsiveAutoscaling = new ResponsiveAutoscalingService();

export default responsiveAutoscaling;
