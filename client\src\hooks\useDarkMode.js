import { useTheme } from '../contexts/ThemeContext';
import { commonDarkModeClasses, withDarkMode } from '../utils/darkModeClasses';

/**
 * Custom hook for consistent dark mode class management
 */
export const useDarkMode = () => {
  const { isDarkMode } = useTheme();

  const getDarkModeClass = (baseClass, category = 'backgrounds') => {
    return withDarkMode(baseClass);
  };

  const getCommonClass = (classKey) => {
    return commonDarkModeClasses[classKey] || '';
  };

  return {
    isDarkMode,
    getDarkModeClass,
    getCommonClass,
    classes: commonDarkModeClasses,
  };
};

export default useDarkMode; 