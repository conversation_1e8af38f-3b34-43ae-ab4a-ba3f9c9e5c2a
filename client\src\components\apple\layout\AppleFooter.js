import React from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../../../contexts/LanguageContext';
import {
  BriefcaseIcon,
  HeartIcon,
  GlobeAltIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
} from '@heroicons/react/24/outline';

const AppleFooter = () => {
  const { t } = useLanguage();

  const footerSections = [
    {
      title: t('platform'),
      links: [
        { name: t('findWork'), href: '/projects' },
        { name: t('findTalent'), href: '/freelancers' },
        { name: t('postJob'), href: '/jobs' },
        { name: t('contests'), href: '/contests' },
      ],
    },
    {
      title: t('community'),
      links: [
        { name: t('communityHub'), href: '/community' },
        { name: t('successStories'), href: '/success-stories' },
        { name: t('blog'), href: '/blog' },
        { name: t('events'), href: '/events' },
      ],
    },
    {
      title: t('support'),
      links: [
        { name: t('helpCenter'), href: '/support' },
        { name: t('contact'), href: '/contact' },
        { name: t('trustSafety'), href: '/trust-safety' },
        { name: t('apiDocs'), href: '/api-docs' },
      ],
    },
    {
      title: t('company'),
      links: [
        { name: t('aboutUs'), href: '/about' },
        { name: t('careers'), href: '/careers' },
        { name: t('press'), href: '/press' },
        { name: t('investors'), href: '/investors' },
      ],
    },
  ];

  return (
    <footer className='bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 transition-colors duration-300'>
      <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12'>
        {/* Main Footer Content */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-8'>
          {/* Brand Section */}
          <div className='lg:col-span-2'>
            <div className='flex items-center mb-4'>
              <BriefcaseIcon className='h-8 w-8 text-blue-600 dark:text-blue-400 mr-2' />
              <span className='text-2xl font-bold text-gray-900 dark:text-gray-100'>
                VWork
              </span>
            </div>
            <p className='text-gray-600 dark:text-gray-400 mb-6 max-w-md leading-relaxed'>
              {t('footerDescription')}
            </p>
            
            {/* Post Job Button */}
            <Link
              to="/jobs/create"
              className="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md"
            >
              <BriefcaseIcon className="h-4 w-4 mr-2" />
              + Đăng việc
            </Link>
            
            {/* Contact Info */}
            <div className='space-y-3'>
              <div className='flex items-center text-gray-600 dark:text-gray-400'>
                <EnvelopeIcon className='h-5 w-5 mr-3 text-blue-600 dark:text-blue-400' />
                <span><EMAIL></span>
              </div>
              <div className='flex items-center text-gray-600 dark:text-gray-400'>
                <PhoneIcon className='h-5 w-5 mr-3 text-blue-600 dark:text-blue-400' />
                <span>+****************</span>
              </div>
              <div className='flex items-center text-gray-600 dark:text-gray-400'>
                <MapPinIcon className='h-5 w-5 mr-3 text-blue-600 dark:text-blue-400' />
                <span>San Francisco, CA</span>
              </div>
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title}>
              <h3 className='text-sm font-semibold text-gray-900 dark:text-gray-100 uppercase tracking-wider mb-4'>
                {section.title}
              </h3>
              <ul className='space-y-3'>
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      to={link.href}
                      className='text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 text-sm'
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className='border-t border-gray-200 dark:border-gray-700 pt-8'>
          <div className='flex flex-col md:flex-row justify-between items-center'>
            {/* Copyright */}
            <div className='flex items-center text-gray-600 dark:text-gray-400 mb-4 md:mb-0'>
              <span className='text-sm'>
                © 2024 VWork. {t('allRightsReserved')}
              </span>
              <HeartIcon className='h-4 w-4 text-red-500 mx-2' />
              <span className='text-sm'>{t('madeWithLove')}</span>
            </div>

            {/* Language & Legal Links */}
            <div className='flex items-center space-x-6'>
              <div className='flex items-center space-x-4 text-sm'>
                <Link
                  to='/privacy'
                  className='text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200'
                >
                  {t('privacy')}
                </Link>
                <Link
                  to='/terms'
                  className='text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200'
                >
                  {t('terms')}
                </Link>
                <Link
                  to='/cookies'
                  className='text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200'
                >
                  {t('cookies')}
                </Link>
              </div>
              
              <div className='flex items-center text-gray-600 dark:text-gray-400'>
                <GlobeAltIcon className='h-4 w-4 mr-1' />
                <span className='text-sm'>{t('global')}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className='mt-8 pt-8 border-t border-gray-200 dark:border-gray-700'>
          <div className='flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-8'>
            <div className='flex items-center text-gray-600 dark:text-gray-400'>
              <div className='w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2'>
                <div className='w-2 h-2 bg-white rounded-full'></div>
              </div>
              <span className='text-sm font-medium'>{t('securePayments')}</span>
            </div>
            
            <div className='flex items-center text-gray-600 dark:text-gray-400'>
              <div className='w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-2'>
                <div className='w-2 h-2 bg-white rounded-full'></div>
              </div>
              <span className='text-sm font-medium'>{t('verifiedProfiles')}</span>
            </div>
            
            <div className='flex items-center text-gray-600 dark:text-gray-400'>
              <div className='w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center mr-2'>
                <div className='w-2 h-2 bg-white rounded-full'></div>
              </div>
              <span className='text-sm font-medium'>{t('support247')}</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default AppleFooter;
