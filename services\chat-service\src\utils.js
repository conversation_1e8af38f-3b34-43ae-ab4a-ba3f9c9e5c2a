/**
 * Chat Service Utilities
 * Standalone utilities for chat service (no shared dependencies)
 */

// Response utilities
class ApiResponse {
  static success(data = null, message = 'Success', pagination = null) {
    const response = {
      success: true,
      data,
      message
    };

    if (pagination) {
      response.pagination = pagination;
    }

    return response;
  }

  static error(message = 'An error occurred', code = 'INTERNAL_ERROR', statusCode = 500) {
    return {
      success: false,
      error: message,
      message,
      code,
      statusCode
    };
  }

  static validationError(errors, message = 'Validation failed') {
    return {
      success: false,
      error: message,
      message,
      code: 'VALIDATION_ERROR',
      statusCode: 422,
      errors
    };
  }

  static unauthorized(message = 'Unauthorized access') {
    return {
      success: false,
      error: message,
      message,
      code: 'UNAUTHORIZED',
      statusCode: 401
    };
  }

  static forbidden(message = 'Access forbidden') {
    return {
      success: false,
      error: message,
      message,
      code: 'FORBIDDEN',
      statusCode: 403
    };
  }

  static notFound(message = 'Resource not found') {
    return {
      success: false,
      error: message,
      message,
      code: 'NOT_FOUND',
      statusCode: 404
    };
  }

  static badRequest(message = 'Bad request') {
    return {
      success: false,
      error: message,
      message,
      code: 'BAD_REQUEST',
      statusCode: 400
    };
  }
}

// Response middleware
const responseMiddleware = (req, res, next) => {
  res.apiSuccess = (data, message, pagination) => {
    const response = ApiResponse.success(data, message, pagination);
    res.status(200).json(response);
  };

  res.apiError = (message, code, statusCode = 500) => {
    const response = ApiResponse.error(message, code, statusCode);
    res.status(statusCode).json(response);
  };

  res.apiValidationError = (errors, message) => {
    const response = ApiResponse.validationError(errors, message);
    res.status(422).json(response);
  };

  res.apiUnauthorized = (message) => {
    const response = ApiResponse.unauthorized(message);
    res.status(401).json(response);
  };

  res.apiForbidden = (message) => {
    const response = ApiResponse.forbidden(message);
    res.status(403).json(response);
  };

  res.apiNotFound = (message) => {
    const response = ApiResponse.notFound(message);
    res.status(404).json(response);
  };

  res.apiBadRequest = (message) => {
    const response = ApiResponse.badRequest(message);
    res.status(400).json(response);
  };

  next();
};

// Auth middleware
const verifyFirebaseToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.apiUnauthorized('No token provided');
    }

    // For now, just check if token exists
    // In production, this should call Auth Service
    if (token && token !== 'undefined' && token !== 'null') {
      // Mock user data for development
      req.user = {
        uid: 'mock-user-id',
        email: '<EMAIL>',
        name: 'Mock User',
        userType: 'freelancer'
      };
      next();
    } else {
      res.apiUnauthorized('Invalid token');
    }

  } catch (error) {
    console.error('❌ Token verification failed:', error.message);
    res.apiUnauthorized('Invalid token');
  }
};

const optionalAuth = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token || token === 'undefined' || token === 'null') {
      return next(); // Continue without user data
    }

    // Mock user data for development
    req.user = {
      uid: 'mock-user-id',
      email: '<EMAIL>',
      name: 'Mock User',
      userType: 'freelancer'
    };

    next();

  } catch (error) {
    console.error('❌ Optional auth failed:', error.message);
    next(); // Continue without user data
  }
};

module.exports = {
  ApiResponse,
  responseMiddleware,
  verifyFirebaseToken,
  optionalAuth
}; 