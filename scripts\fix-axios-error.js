#!/usr/bin/env node

/**
 * Fix Axios Error Script
 * Installs axios in all services that need it
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Services that need axios
const services = [
  'services/shared',
  'services/user-service',
  'services/project-service',
  'services/job-service',
  'services/chat-service',
  'services/api-gateway'
];

// Fix axios error for a service
const fixAxiosError = (servicePath) => {
  log(`🔧 Fixing axios error in ${servicePath}...`, 'cyan');
  
  try {
    const fullPath = path.join(process.cwd(), servicePath);
    
    // Check if directory exists
    if (!fs.existsSync(fullPath)) {
      log(`❌ Directory not found: ${servicePath}`, 'red');
      return false;
    }
    
    // Change to service directory
    process.chdir(fullPath);
    
    // Check if package.json exists
    const packageJsonPath = path.join(fullPath, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      log(`❌ package.json not found in ${servicePath}`, 'red');
      return false;
    }
    
    // Install axios
    execSync('npm install axios@^1.6.0', {
      stdio: 'inherit'
    });
    
    log(`✅ Axios installed successfully in ${servicePath}`, 'green');
    return true;
    
  } catch (error) {
    log(`❌ Failed to install axios in ${servicePath}: ${error.message}`, 'red');
    return false;
  }
};

// Fix axios error for all services
const fixAllAxiosErrors = async () => {
  log('🔧 Fixing Axios Errors', 'bright');
  log('=====================', 'bright');
  
  const results = [];
  
  for (const servicePath of services) {
    const success = fixAxiosError(servicePath);
    results.push({ servicePath, success });
    
    // Wait a bit between installations
    if (success) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  // Summary
  log('\n📊 Fix Summary:', 'cyan');
  log('==============', 'cyan');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  log(`✅ Successfully fixed: ${successful.length}`, 'green');
  successful.forEach(({ servicePath }) => {
    log(`   • ${servicePath}`, 'green');
  });
  
  if (failed.length > 0) {
    log(`❌ Failed fixes: ${failed.length}`, 'red');
    failed.forEach(({ servicePath }) => {
      log(`   • ${servicePath}`, 'red');
    });
  }
  
  log('\n💡 Next Steps:', 'cyan');
  log('==============', 'cyan');
  log('1. Test services locally', 'yellow');
  log('2. Deploy to Render', 'yellow');
  log('3. Check if axios error is resolved', 'yellow');
};

// Main function
const main = async () => {
  try {
    await fixAllAxiosErrors();
  } catch (error) {
    log(`❌ Fix failed: ${error.message}`, 'red');
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { fixAllAxiosErrors, services }; 