#!/usr/bin/env node

/**
 * Health check script for all VWork services
 */

const axios = require('axios');

const services = [
  { name: 'API Gateway', url: 'http://localhost:8080/health' },
  { name: 'Auth Service', url: 'http://localhost:3001/health' },
  { name: 'User Service', url: 'http://localhost:3002/health' },
  { name: 'Project Service', url: 'http://localhost:3003/health' },
  { name: 'Job Service', url: 'http://localhost:3004/health' },
  { name: 'Chat Service', url: 'http://localhost:3005/health' },
  { name: 'Search Service', url: 'http://localhost:3009/health' }
];

const log = (message, color = 'reset') => {
  const colors = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    cyan: '\x1b[36m'
  };
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
};

const checkService = async (service) => {
  try {
    const response = await axios.get(service.url, { 
      timeout: 5000,
      validateStatus: (status) => status < 500 
    });
    
    if (response.status === 200) {
      return {
        name: service.name,
        status: 'healthy',
        responseTime: response.headers['x-response-time'] || 'N/A',
        data: response.data
      };
    } else {
      return {
        name: service.name,
        status: 'unhealthy',
        statusCode: response.status,
        message: response.statusText
      };
    }
  } catch (error) {
    return {
      name: service.name,
      status: 'error',
      error: error.code || error.message,
      message: error.message
    };
  }
};

const main = async () => {
  log('🏥 VWork Platform Health Check', 'cyan');
  log('==============================', 'cyan');

  const results = await Promise.all(services.map(checkService));
  
  log('\n📊 Service Health Status:', 'cyan');
  log('=========================', 'cyan');

  let healthyCount = 0;
  let unhealthyCount = 0;
  let errorCount = 0;

  results.forEach(result => {
    switch (result.status) {
      case 'healthy':
        log(`✅ ${result.name}: Healthy ${result.responseTime ? `(${result.responseTime})` : ''}`, 'green');
        healthyCount++;
        break;
      case 'unhealthy':
        log(`⚠️ ${result.name}: Unhealthy (${result.statusCode} - ${result.message})`, 'yellow');
        unhealthyCount++;
        break;
      case 'error':
        log(`❌ ${result.name}: Error (${result.error})`, 'red');
        errorCount++;
        break;
    }
  });

  log('\n📈 Summary:', 'cyan');
  log(`   Healthy: ${healthyCount}/${services.length}`, 'green');
  log(`   Unhealthy: ${unhealthyCount}/${services.length}`, 'yellow');
  log(`   Errors: ${errorCount}/${services.length}`, 'red');

  const overallHealth = healthyCount / services.length;
  
  if (overallHealth === 1) {
    log('\n🎉 All services are healthy!', 'green');
  } else if (overallHealth >= 0.8) {
    log('\n⚠️ Most services are healthy, but some issues detected', 'yellow');
  } else {
    log('\n❌ Multiple service issues detected - immediate attention required', 'red');
  }

  // Exit with appropriate code for CI/CD
  if (overallHealth < 0.5) {
    process.exit(1);
  } else if (overallHealth < 1) {
    process.exit(2); // Warning
  } else {
    process.exit(0); // Success
  }
};

const continuousCheck = async (interval = 30000) => {
  log(`🔄 Starting continuous health monitoring (${interval/1000}s intervals)...`, 'cyan');
  
  while (true) {
    await main();
    await new Promise(resolve => setTimeout(resolve, interval));
  }
};

if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--continuous') || args.includes('-c')) {
    const intervalArg = args.find(arg => arg.startsWith('--interval='));
    const interval = intervalArg ? parseInt(intervalArg.split('=')[1]) * 1000 : 30000;
    
    continuousCheck(interval).catch(error => {
      log(`❌ Health check failed: ${error.message}`, 'red');
      process.exit(1);
    });
  } else {
    main().catch(error => {
      log(`❌ Health check failed: ${error.message}`, 'red');
      process.exit(1);
    });
  }
}

module.exports = { checkService, services };
