import React from 'react';
import { useDarkMode } from '../../hooks/useDarkMode';

/**
 * Wrapper component that automatically applies dark mode classes
 */
const DarkModeWrapper = ({ 
  children, 
  className = '', 
  baseClass = '',
  category = 'backgrounds',
  ...props 
}) => {
  const { getDarkModeClass, getCommonClass } = useDarkMode();

  const getClassName = () => {
    if (baseClass) {
      return getDarkModeClass(baseClass, category);
    }
    return className;
  };

  return (
    <div className={getClassName()} {...props}>
      {children}
    </div>
  );
};

/**
 * Pre-configured dark mode components
 */
export const DarkModeCard = ({ children, className = '', ...props }) => (
  <DarkModeWrapper 
    baseClass="card-base" 
    category="cards" 
    className={className}
    {...props}
  >
    {children}
  </DarkModeWrapper>
);

export const DarkModeButton = ({ children, className = '', variant = 'primary', ...props }) => {
  const { classes } = useDarkMode();
  const buttonClass = variant === 'primary' ? classes.buttons['btn-primary'] : classes.buttons['btn-secondary'];
  
  return (
    <button className={`${buttonClass} ${className}`} {...props}>
      {children}
    </button>
  );
};

export const DarkModeInput = ({ className = '', ...props }) => {
  const { classes } = useDarkMode();
  
  return (
    <input 
      className={`${classes.forms['input-base']} ${classes.forms['input-focus']} ${className}`}
      {...props}
    />
  );
};

export default DarkModeWrapper; 