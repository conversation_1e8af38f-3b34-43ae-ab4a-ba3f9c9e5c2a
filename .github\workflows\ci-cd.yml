# GitHub Actions CI/CD Pipeline for VWork Platform
name: VWork CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18.x'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Lint and Test
  test:
    name: 🧪 Test & Lint
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install shared dependencies
      run: |
        cd services/shared
        npm ci

    - name: Install client dependencies
      run: |
        cd client
        npm ci

    - name: Lint client code
      run: |
        cd client
        npm run lint || echo "Linting completed with warnings"

    - name: Test client
      run: |
        cd client
        npm test -- --coverage --watchAll=false

    - name: Install service dependencies
      run: |
        services=(auth-service user-service project-service job-service chat-service search-service api-gateway)
        for service in "${services[@]}"; do
          if [ -d "services/$service" ]; then
            echo "Installing dependencies for $service..."
            cd "services/$service"
            npm ci || echo "Warning: Failed to install dependencies for $service"
            cd ../..
          fi
        done

    - name: Test services
      run: |
        services=(auth-service user-service project-service job-service chat-service search-service api-gateway)
        for service in "${services[@]}"; do
          if [ -d "services/$service" ] && [ -f "services/$service/package.json" ]; then
            cd "services/$service"
            if npm run | grep -q "test"; then
              npm test || echo "Warning: Tests failed for $service"
            fi
            cd ../..
          fi
        done

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        directory: ./client/coverage
        fail_ci_if_error: false

  # Security Scan
  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

    - name: NPM Audit
      run: |
        cd client && npm audit --audit-level=high || true
        cd ../services/shared && npm audit --audit-level=high || true

  # Build
  build:
    name: 🏗️ Build Application
    runs-on: ubuntu-latest
    needs: [test, security]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Make build script executable
      run: chmod +x scripts/build-production.sh

    - name: Build production
      run: ./scripts/build-production.sh

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: vwork-build
        path: build/
        retention-days: 30

  # Docker Build
  docker:
    name: 🐳 Build Docker Image
    runs-on: ubuntu-latest
    needs: [build]
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: vwork-build
        path: build/

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./build
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - name: Deploy to staging
      run: |
        echo "🚀 Deploying to staging environment..."
        # Add your staging deployment commands here
        # This could be:
        # - Deploy to staging server via SSH
        # - Update Kubernetes deployment
        # - Deploy to cloud provider (AWS, GCP, Azure)
        # - Deploy to Render, Heroku, etc.

  # Deploy to Production
  deploy-production:
    name: 🎯 Deploy to Production
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Deploy to production
      run: |
        echo "🎯 Deploying to production environment..."
        # Add your production deployment commands here

  # Health Check
  health-check:
    name: 🏥 Health Check
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')

    steps:
    - name: Wait for deployment
      run: sleep 60

    - name: Check application health
      run: |
        # Add health check URLs based on your deployment
        echo "Checking application health..."
        # curl -f https://your-staging-url.com/health || exit 1
        # curl -f https://your-production-url.com/health || exit 1
