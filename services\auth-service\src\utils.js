/**
 * Auth Service Utilities
 * Standalone utilities for auth service (no shared dependencies)
 */

const Joi = require('joi');

// Response utilities
class ApiResponse {
  static success(data = null, message = 'Success', pagination = null) {
    const response = {
      success: true,
      data,
      message
    };

    if (pagination) {
      response.pagination = pagination;
    }

    return response;
  }

  static error(message = 'An error occurred', code = 'INTERNAL_ERROR', statusCode = 500) {
    return {
      success: false,
      error: message,
      message,
      code,
      statusCode
    };
  }

  static validationError(errors, message = 'Validation failed') {
    return {
      success: false,
      error: message,
      message,
      code: 'VALIDATION_ERROR',
      statusCode: 422,
      errors
    };
  }

  static unauthorized(message = 'Unauthorized access') {
    return {
      success: false,
      error: message,
      message,
      code: 'UNAUTHORIZED',
      statusCode: 401
    };
  }

  static forbidden(message = 'Access forbidden') {
    return {
      success: false,
      error: message,
      message,
      code: 'FORBIDDEN',
      statusCode: 403
    };
  }

  static notFound(message = 'Resource not found') {
    return {
      success: false,
      error: message,
      message,
      code: 'NOT_FOUND',
      statusCode: 404
    };
  }

  static badRequest(message = 'Bad request') {
    return {
      success: false,
      error: message,
      message,
      code: 'BAD_REQUEST',
      statusCode: 400
    };
  }
}

// Response middleware
const responseMiddleware = (req, res, next) => {
  res.apiSuccess = (data, message, pagination) => {
    const response = ApiResponse.success(data, message, pagination);
    res.status(200).json(response);
  };

  res.apiError = (message, code, statusCode = 500) => {
    const response = ApiResponse.error(message, code, statusCode);
    res.status(statusCode).json(response);
  };

  res.apiValidationError = (errors, message) => {
    const response = ApiResponse.validationError(errors, message);
    res.status(422).json(response);
  };

  res.apiUnauthorized = (message) => {
    const response = ApiResponse.unauthorized(message);
    res.status(401).json(response);
  };

  res.apiForbidden = (message) => {
    const response = ApiResponse.forbidden(message);
    res.status(403).json(response);
  };

  res.apiNotFound = (message) => {
    const response = ApiResponse.notFound(message);
    res.status(404).json(response);
  };

  res.apiBadRequest = (message) => {
    const response = ApiResponse.badRequest(message);
    res.status(400).json(response);
  };

  next();
};

// Validation utilities
const validateBody = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      return res.apiValidationError(errors, 'Validation failed');
    }

    req.body = value;
    next();
  };
};

// Auth service validation schemas
const schemas = {
  userRegistration: Joi.object({
    firebaseToken: Joi.string().required(),
    userType: Joi.string().valid('freelancer', 'client').required(),
    name: Joi.string().min(2).max(100).required(),
    guildSpecialization: Joi.string().max(100).allow('').optional()
  }),

  userLogin: Joi.object({
    firebaseToken: Joi.string().required()
  })
};

module.exports = {
  ApiResponse,
  responseMiddleware,
  validateBody,
  schemas
}; 