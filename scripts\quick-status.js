#!/usr/bin/env node

/**
 * Quick status check for VWork services
 */

const net = require('net');

const services = [
  { name: 'API Gateway', port: 8080 },
  { name: 'Auth Service', port: 3001 },
  { name: 'User Service', port: 3002 },
  { name: 'Project Service', port: 3003 },
  { name: 'Job Service', port: 3004 },
  { name: 'Chat Service', port: 3005 },
  { name: 'Search Service', port: 3009 }
];

const checkPort = (port) => {
  return new Promise((resolve) => {
    const socket = new net.Socket();
    
    socket.setTimeout(1000);
    
    socket.on('connect', () => {
      socket.destroy();
      resolve(true);
    });
    
    socket.on('timeout', () => {
      socket.destroy();
      resolve(false);
    });
    
    socket.on('error', () => {
      resolve(false);
    });
    
    socket.connect(port, 'localhost');
  });
};

const main = async () => {
  console.log('🔍 VWork Services Status Check');
  console.log('==============================');
  
  let runningCount = 0;
  
  for (const service of services) {
    const isRunning = await checkPort(service.port);
    const status = isRunning ? '✅ RUNNING' : '❌ STOPPED';
    const color = isRunning ? '\x1b[32m' : '\x1b[31m';
    
    console.log(`${color}${status}\x1b[0m ${service.name} (port ${service.port})`);
    
    if (isRunning) runningCount++;
  }
  
  console.log('\n📊 Summary:');
  console.log(`Running: ${runningCount}/${services.length} services`);
  
  if (runningCount === services.length) {
    console.log('🎉 All services are running!');
  } else if (runningCount > 0) {
    console.log('⚠️ Some services are not running');
  } else {
    console.log('❌ No services are running');
    console.log('\n💡 Try running: npm start');
  }
};

main().catch(console.error);
