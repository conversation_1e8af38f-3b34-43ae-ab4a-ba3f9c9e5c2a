#!/usr/bin/env node

/**
 * Install Dependencies Script
 * Installs dependencies for all VWork services
 */

const { execSync } = require('child_process');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Service configurations
const services = [
  {
    name: 'Shared',
    path: 'services/shared'
  },
  {
    name: 'Auth Service',
    path: 'services/auth-service'
  },
  {
    name: 'User Service',
    path: 'services/user-service'
  },
  {
    name: 'Project Service',
    path: 'services/project-service'
  },
  {
    name: 'Job Service',
    path: 'services/job-service'
  },
  {
    name: 'Chat Service',
    path: 'services/chat-service'
  },
  {
    name: 'API Gateway',
    path: 'services/api-gateway'
  }
];

// Install dependencies for a service
const installServiceDependencies = (service) => {
  log(`📦 Installing dependencies for ${service.name}...`, 'cyan');
  
  try {
    const servicePath = path.join(process.cwd(), service.path);
    process.chdir(servicePath);
    
    // Check if package.json exists
    const packageJsonPath = path.join(servicePath, 'package.json');
    if (!require('fs').existsSync(packageJsonPath)) {
      log(`❌ package.json not found for ${service.name}`, 'red');
      return false;
    }
    
    // Install dependencies
    execSync('npm install', {
      stdio: 'inherit'
    });
    
    log(`✅ ${service.name} dependencies installed successfully`, 'green');
    return true;
    
  } catch (error) {
    log(`❌ Failed to install dependencies for ${service.name}: ${error.message}`, 'red');
    return false;
  }
};

// Install all dependencies
const installAllDependencies = async () => {
  log('🎯 VWork Dependencies Installation', 'bright');
  log('================================', 'bright');
  
  const results = [];
  
  for (const service of services) {
    const success = installServiceDependencies(service);
    results.push({ service, success });
    
    // Wait a bit between installations
    if (success) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  // Summary
  log('\n📊 Installation Summary:', 'cyan');
  log('======================', 'cyan');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  log(`✅ Successfully installed: ${successful.length}`, 'green');
  successful.forEach(({ service }) => {
    log(`   • ${service.name}`, 'green');
  });
  
  if (failed.length > 0) {
    log(`❌ Failed installations: ${failed.length}`, 'red');
    failed.forEach(({ service }) => {
      log(`   • ${service.name}`, 'red');
    });
  }
  
  log('\n💡 Next Steps:', 'cyan');
  log('==============', 'cyan');
  log('1. Test services locally', 'yellow');
  log('2. Deploy to Render', 'yellow');
  log('3. Configure environment variables', 'yellow');
};

// Main function
const main = async () => {
  try {
    await installAllDependencies();
  } catch (error) {
    log(`❌ Installation failed: ${error.message}`, 'red');
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { installAllDependencies, services }; 