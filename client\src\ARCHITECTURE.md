# Frontend Architecture Guide

## 📁 Cấu trúc Th<PERSON> mục

```
src/
├── components/          # Reusable components
│   ├── apple/          # Apple design system components
│   ├── common/         # Shared components
│   ├── auth/           # Authentication components
│   └── layout/         # Layout components
├── pages/              # Page components
├── contexts/           # React contexts
├── hooks/              # Custom hooks
├── utils/              # Utility functions
├── services/           # API and business logic
└── styles/             # CSS files
```

## 🎨 Theme System

### Dark Mode Implementation
- **ThemeContext.js**: Centralized theme state management
- **darkModeClasses.js**: Utility classes for dark mode
- **useDarkMode.js**: Custom hook for consistent dark mode usage

### Usage Examples
```javascript
// ✅ Recommended approach
import { useDarkMode } from '../hooks/useDarkMode';
import { DarkModeCard, DarkModeButton } from '../components/common/DarkModeWrapper';

const MyComponent = () => {
  const { isDarkMode, getCommonClass } = useDarkMode();
  
  return (
    <DarkModeCard>
      <h1 className={getCommonClass('pageHeader')}>
        Title
      </h1>
      <DarkModeButton variant="primary">
        Click me
      </DarkModeButton>
    </DarkModeCard>
  );
};
```

## 🎬 Animation System

### Animation Controller
- **animations.js**: Centralized animation controller
- **gsapConfig.js**: GSAP configuration
- **useAnimation.js**: Custom hook for animations

### Usage Examples
```javascript
// ✅ Recommended approach
import { useAnimation } from '../hooks/useAnimation';

const MyComponent = () => {
  const { elementRef, animateIn, animateCard } = useAnimation();
  
  useEffect(() => {
    animateIn({ duration: 0.8, stagger: 0.1 });
    animateCard();
  }, []);
  
  return <div ref={elementRef}>Content</div>;
};
```

## 🚫 Anti-patterns to Avoid

### ❌ Don't do this:
```javascript
// Hardcoded dark mode classes
className="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"

// Direct GSAP usage
import { gsap } from 'gsap';
const tl = gsap.timeline();
```

### ✅ Do this instead:
```javascript
// Use utility classes
className={getCommonClass('pageContainer')}

// Use animation controller
const { animateIn } = useAnimation();
animateIn();
```

## 📋 Best Practices

1. **Always use utility classes** from `darkModeClasses.js`
2. **Use custom hooks** for animations and dark mode
3. **Import GSAP** only from `gsapConfig.js`
4. **Use DarkModeWrapper components** for consistent styling
5. **Keep components small** and focused on single responsibility

## 🔧 Migration Guide

### Converting Existing Components

1. **Replace hardcoded dark mode classes**:
```javascript
// Before
className="bg-white dark:bg-gray-900"

// After
className={getCommonClass('pageContainer')}
```

2. **Replace direct GSAP usage**:
```javascript
// Before
import { gsap } from 'gsap';
const tl = gsap.timeline();

// After
import { useAnimation } from '../hooks/useAnimation';
const { animateIn } = useAnimation();
```

3. **Use DarkModeWrapper components**:
```javascript
// Before
<div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">

// After
<DarkModeCard>
```

## 📊 Performance Considerations

1. **Animation cleanup**: Always cleanup animations in useEffect
2. **Reduced motion**: Respect user preferences
3. **Lazy loading**: Use React.lazy for large components
4. **Memoization**: Use React.memo for expensive components 