/**
 * Auth Service Validation Schemas
 */

const schemas = {
  register: {
    email: { type: 'email', required: true },
    password: { type: 'string', required: true, minLength: 6 },
    name: { type: 'string', required: true, minLength: 2, maxLength: 100 },
    userType: { type: 'string', required: true, enum: ['freelancer', 'client'] }
  },

  login: {
    email: { type: 'email', required: true },
    password: { type: 'string', required: true }
  },

  resetPassword: {
    email: { type: 'email', required: true }
  },

  changePassword: {
    currentPassword: { type: 'string', required: true },
    newPassword: { type: 'string', required: true, minLength: 6 }
  }
};

module.exports = { schemas };
