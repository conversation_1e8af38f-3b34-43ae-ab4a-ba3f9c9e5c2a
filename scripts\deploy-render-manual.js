#!/usr/bin/env node

/**
 * Manual Render Deployment Script
 * Deploys VWork services to Render manually
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Service configurations
const services = [
  {
    name: 'Auth Service',
    path: 'services/auth-service',
    renderName: 'vwork-auth-service',
    port: 3001
  },
  {
    name: 'User Service',
    path: 'services/user-service',
    renderName: 'vwork-user-service',
    port: 3002
  },
  {
    name: 'Project Service',
    path: 'services/project-service',
    renderName: 'vwork-project-service',
    port: 3003
  },
  {
    name: 'Job Service',
    path: 'services/job-service',
    renderName: 'vwork-job-service',
    port: 3004
  },
  {
    name: 'Chat Service',
    path: 'services/chat-service',
    renderName: 'vwork-chat-service',
    port: 3005
  },
  {
    name: 'API Gateway',
    path: 'services/api-gateway',
    renderName: 'vwork-gateway',
    port: 10000
  }
];

// Check if render CLI is installed
const checkRenderCLI = () => {
  try {
    execSync('render --version', { stdio: 'pipe' });
    return true;
  } catch (error) {
    return false;
  }
};

// Install render CLI if not available
const installRenderCLI = () => {
  log('📦 Installing Render CLI...', 'cyan');
  try {
    execSync('npm install -g @render/cli', { stdio: 'inherit' });
    log('✅ Render CLI installed successfully', 'green');
  } catch (error) {
    log('❌ Failed to install Render CLI', 'red');
    log('Please install manually: npm install -g @render/cli', 'yellow');
    process.exit(1);
  }
};

// Login to Render
const loginToRender = () => {
  log('🔐 Logging into Render...', 'cyan');
  try {
    execSync('render login', { stdio: 'inherit' });
    log('✅ Logged into Render successfully', 'green');
  } catch (error) {
    log('❌ Failed to login to Render', 'red');
    log('Please login manually: render login', 'yellow');
    process.exit(1);
  }
};

// Deploy a single service
const deployService = (service) => {
  log(`🚀 Deploying ${service.name}...`, 'cyan');
  
  try {
    // Change to service directory
    const servicePath = path.join(process.cwd(), service.path);
    process.chdir(servicePath);
    
    // Check if render.yaml exists
    const renderYamlPath = path.join(servicePath, 'render.yaml');
    if (!fs.existsSync(renderYamlPath)) {
      log(`❌ render.yaml not found for ${service.name}`, 'red');
      return false;
    }
    
    // Deploy using render CLI
    execSync(`render deploy --service ${service.renderName}`, {
      stdio: 'inherit'
    });
    
    log(`✅ ${service.name} deployed successfully`, 'green');
    return true;
    
  } catch (error) {
    log(`❌ Failed to deploy ${service.name}: ${error.message}`, 'red');
    return false;
  }
};

// Deploy all services
const deployAllServices = async () => {
  log('🎯 VWork Manual Render Deployment', 'bright');
  log('================================', 'bright');
  
  // Check and install Render CLI
  if (!checkRenderCLI()) {
    log('⚠️ Render CLI not found', 'yellow');
    installRenderCLI();
  }
  
  // Login to Render
  loginToRender();
  
  // Deploy services
  const results = [];
  
  for (const service of services) {
    const success = deployService(service);
    results.push({ service, success });
    
    // Wait between deployments
    if (success) {
      log(`⏳ Waiting 30 seconds before next deployment...`, 'yellow');
      await new Promise(resolve => setTimeout(resolve, 30000));
    }
  }
  
  // Summary
  log('\n📊 Deployment Summary:', 'cyan');
  log('====================', 'cyan');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  log(`✅ Successfully deployed: ${successful.length}`, 'green');
  successful.forEach(({ service }) => {
    log(`   • ${service.name}: https://${service.renderName}.onrender.com`, 'green');
  });
  
  if (failed.length > 0) {
    log(`❌ Failed deployments: ${failed.length}`, 'red');
    failed.forEach(({ service }) => {
      log(`   • ${service.name}`, 'red');
    });
  }
  
  log('\n🌐 Service URLs:', 'cyan');
  log('===============', 'cyan');
  services.forEach(service => {
    log(`   • ${service.name}: https://${service.renderName}.onrender.com`, 'blue');
  });
  
  log('\n🏥 Health Checks:', 'cyan');
  log('================', 'cyan');
  services.forEach(service => {
    log(`   • ${service.name}: https://${service.renderName}.onrender.com/health`, 'blue');
  });
  
  log('\n💡 Next Steps:', 'cyan');
  log('==============', 'cyan');
  log('1. Set environment variables in Render dashboard', 'yellow');
  log('2. Configure Firebase credentials for Auth Service', 'yellow');
  log('3. Test health endpoints', 'yellow');
  log('4. Deploy client application', 'yellow');
};

// Main function
const main = async () => {
  try {
    await deployAllServices();
  } catch (error) {
    log(`❌ Deployment failed: ${error.message}`, 'red');
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { deployAllServices, services }; 