#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to prepare VWork services for Render deployment
 */

const fs = require('fs');
const path = require('path');

const log = (message, color = 'reset') => {
  const colors = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    cyan: '\x1b[36m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const services = [
  'auth-service',
  'user-service', 
  'project-service',
  'job-service',
  'chat-service',
  'search-service',
  'api-gateway'
];

// Health check endpoint template
const healthCheckCode = `
// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    service: '{{SERVICE_NAME}}',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});`;

// Production middleware template
const productionMiddleware = `
// Production error handling
if (process.env.NODE_ENV === 'production') {
  app.use((error, req, res, next) => {
    console.error('Production Error:', error.message);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  });
}`;

const createRenderYaml = (serviceName) => {
  const renderConfig = `services:
  - type: web
    runtime: node
    name: vwork-${serviceName}
    region: oregon
    branch: main
    rootDir: services/${serviceName}
    buildCommand: npm ci
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: FIREBASE_PROJECT_ID
        sync: false
      - key: FIREBASE_SERVICE_ACCOUNT_KEY
        sync: false
      - key: CORS_ORIGIN
        value: https://vwork-gateway.onrender.com,https://vwork-client.onrender.com
    plan: free
`;

  return renderConfig;
};

const updatePackageJson = (servicePath, serviceName) => {
  const packagePath = path.join(servicePath, 'package.json');
  
  if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // Update scripts for production
    packageJson.scripts = {
      ...packageJson.scripts,
      start: 'node src/index.js',
      build: 'npm ci --production',
      dev: 'nodemon src/index.js'
    };
    
    // Add engines requirement
    packageJson.engines = {
      node: '>=18.0.0'
    };
    
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
    log(`✅ Updated package.json for ${serviceName}`, 'green');
  }
};

const addHealthCheck = (servicePath, serviceName) => {
  const indexPath = path.join(servicePath, 'src', 'index.js');
  
  if (fs.existsSync(indexPath)) {
    let content = fs.readFileSync(indexPath, 'utf8');
    
    // Check if health check already exists
    if (!content.includes('/health')) {
      const healthCheck = healthCheckCode.replace('{{SERVICE_NAME}}', serviceName);
      
      // Find the right place to insert (before app.listen)
      const listenMatch = content.match(/(app\.listen\([^}]+\}\);?\s*$)/m);
      if (listenMatch) {
        content = content.replace(
          listenMatch[1],
          healthCheck + '\n\n' + listenMatch[1]
        );
        
        fs.writeFileSync(indexPath, content);
        log(`✅ Added health check to ${serviceName}`, 'green');
      }
    }
  }
};

const main = async () => {
  log('🚀 Preparing VWork services for Render deployment...', 'cyan');
  log('=====================================================', 'cyan');
  
  for (const serviceName of services) {
    const servicePath = path.join(__dirname, '..', 'services', serviceName);
    
    if (fs.existsSync(servicePath)) {
      log(`\n🔧 Preparing ${serviceName}...`, 'cyan');
      
      // Update package.json
      updatePackageJson(servicePath, serviceName);
      
      // Add health check
      addHealthCheck(servicePath, serviceName);
      
      // Create/update render.yaml if it doesn't exist
      const renderYamlPath = path.join(servicePath, 'render.yaml');
      if (!fs.existsSync(renderYamlPath)) {
        fs.writeFileSync(renderYamlPath, createRenderYaml(serviceName));
        log(`✅ Created render.yaml for ${serviceName}`, 'green');
      }
      
    } else {
      log(`⚠️ Service directory not found: ${serviceName}`, 'yellow');
    }
  }
  
  // Create main render.yaml for the entire project
  const mainRenderConfig = `services:
  # API Gateway (Main entry point)
  - type: web
    runtime: node
    name: vwork-gateway
    region: oregon
    branch: main
    rootDir: services/api-gateway
    buildCommand: npm ci
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: AUTH_SERVICE_URL
        value: https://vwork-auth-service.onrender.com
      - key: USER_SERVICE_URL
        value: https://vwork-user-service.onrender.com
      - key: PROJECT_SERVICE_URL
        value: https://vwork-project-service.onrender.com
      - key: JOB_SERVICE_URL
        value: https://vwork-job-service.onrender.com
      - key: CHAT_SERVICE_URL
        value: https://vwork-chat-service.onrender.com
      - key: SEARCH_SERVICE_URL
        value: https://vwork-search-service.onrender.com
      - key: CORS_ORIGIN
        value: https://vwork-client.onrender.com
    plan: free

  # React Client
  - type: web
    runtime: static
    name: vwork-client
    region: oregon
    branch: main
    rootDir: client
    buildCommand: npm ci && npm run build
    publishDir: build
    envVars:
      - key: NODE_ENV
        value: production
      - key: GENERATE_SOURCEMAP
        value: false
      - key: REACT_APP_API_BASE_URL
        value: https://vwork-gateway.onrender.com/api/v1
      - key: REACT_APP_FIREBASE_API_KEY
        sync: false
      - key: REACT_APP_FIREBASE_AUTH_DOMAIN
        sync: false
      - key: REACT_APP_FIREBASE_PROJECT_ID
        sync: false
      - key: REACT_APP_FIREBASE_STORAGE_BUCKET
        sync: false
      - key: REACT_APP_FIREBASE_MESSAGING_SENDER_ID
        sync: false
      - key: REACT_APP_FIREBASE_APP_ID
        sync: false
    plan: free
`;

  fs.writeFileSync(path.join(__dirname, '..', 'render.yaml'), mainRenderConfig);
  log(`✅ Created main render.yaml`, 'green');
  
  log('\n🎉 All services prepared for Render deployment!', 'green');
  log('================================================', 'green');
  log('\n📋 Next steps:', 'cyan');
  log('1. Push changes to GitHub:', 'cyan');
  log('   git add .', 'cyan');
  log('   git commit -m "Prepare for Render deployment"', 'cyan');
  log('   git push origin main', 'cyan');
  log('\n2. Deploy services in this order:', 'cyan');
  services.forEach((service, index) => {
    log(`   ${index + 1}. ${service}`, 'cyan');
  });
  log('\n3. Update environment variables in Render dashboard', 'cyan');
  log('4. Test health checks and connectivity', 'cyan');
};

if (require.main === module) {
  main().catch(error => {
    log(`❌ Preparation failed: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { main };
