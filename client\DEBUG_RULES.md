# Debug Rules & Best Practices

## 🚫 Debug Code Restrictions

### Production Rules
- **No `console.log()`** - Use proper logging service
- **No `console.debug()`** - Use proper logging service  
- **No `console.info()`** - Use proper logging service
- **No `alert()`** - Use proper error handling
- **No `debugger` statements** - Remove before production

### Development Rules
- `console.log()` is allowed but discouraged
- `console.warn()` and `console.error()` are allowed
- Debug UI elements should be removed before production

## 📝 Proper Logging

### Instead of console.log(), use:
```javascript
// ✅ Good - Use proper logging service
import { logger } from '../services/logger';

logger.info('User action completed', { userId, action });
logger.error('API call failed', { error, endpoint });
logger.warn('Deprecated feature used', { feature, user });

// ❌ Bad - Don't use console.log in production
console.log('Debug info:', data);
console.debug('Debug data');
```

## 🧹 Cleanup Checklist

Before deploying to production:

1. **Remove debug UI elements**
   ```javascript
   // ❌ Remove this
   {process.env.NODE_ENV === 'development' && (
     <DebugPanel />
   )}
   ```

2. **Remove debug state**
   ```javascript
   // ❌ Remove this
   const [debugMode, setDebugMode] = useState(false);
   ```

3. **Remove debug console statements**
   ```javascript
   // ❌ Remove this
   console.log('🔍 Debug info:', data);
   ```

4. **Remove debug comments**
   ```javascript
   // ❌ Remove this
   // DEBUG: This is temporary code
   ```

## 🔧 Linting Commands

```bash
# Development linting (allows console.log)
npm run lint

# Production linting (strict, no console.log)
npm run lint:production

# Fix auto-fixable issues
npm run lint:fix

# Full quality check
npm run quality:check

# Production quality check
npm run quality:production
```

## 🎯 Best Practices

1. **Use environment variables for debug features**
   ```javascript
   const isDebugMode = process.env.REACT_APP_DEBUG === 'true';
   ```

2. **Create proper logging service**
   ```javascript
   // services/logger.js
   export const logger = {
     info: (message, data) => {
       if (process.env.NODE_ENV === 'development') {
         console.log(message, data);
       }
       // Send to logging service in production
     },
     error: (message, error) => {
       console.error(message, error);
       // Send to error tracking service
     }
   };
   ```

3. **Use React DevTools for debugging**
   - Install React Developer Tools extension
   - Use React DevTools Profiler
   - Use React DevTools Components tab

4. **Use proper error boundaries**
   ```javascript
   class ErrorBoundary extends React.Component {
     componentDidCatch(error, errorInfo) {
       // Log to error tracking service
       logger.error('React Error Boundary caught error', { error, errorInfo });
     }
   }
   ```

## 🚨 Common Issues

### Issue: Debug UI still showing in production
**Solution:** Check for `process.env.NODE_ENV === 'development'` conditions

### Issue: Console statements in production
**Solution:** Run `npm run lint:production` to catch them

### Issue: Debug state variables
**Solution:** Remove unused debug state variables

## 📋 Pre-deployment Checklist

- [ ] Run `npm run lint:production`
- [ ] Remove all debug UI elements
- [ ] Remove debug state variables
- [ ] Remove console.log statements
- [ ] Remove debug comments
- [ ] Test in production build locally
- [ ] Verify no debug features are accessible 