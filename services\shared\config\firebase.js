/**
 * Centralized Firebase configuration for all services
 */
const admin = require('firebase-admin');

class FirebaseManager {
  static instance = null;

  static getInstance() {
    if (!FirebaseManager.instance) {
      FirebaseManager.instance = new FirebaseManager();
    }
    return FirebaseManager.instance;
  }

  constructor() {
    this.initializeFirebase();
  }

  initializeFirebase() {
    if (!admin.apps.length) {
      try {
        // In production, use service account key from environment
        if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
          const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
          admin.initializeApp({
            credential: admin.credential.cert(serviceAccount),
            projectId: process.env.FIREBASE_PROJECT_ID || 'vwork-786c3'
          });
        } else {
          // In development or if service account key is not available
          admin.initializeApp({
            projectId: process.env.FIREBASE_PROJECT_ID || 'vwork-786c3'
          });
        }
        console.log('🔥 Firebase Admin initialized successfully');
      } catch (error) {
        console.error('❌ Firebase Admin initialization failed:', error.message);
        throw error;
      }
    }
  }

  // Middleware to verify Firebase tokens
  verifyFirebaseToken() {
    return async (req, res, next) => {
      try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return res.apiUnauthorized('No token provided');
        }

        const token = authHeader.split('Bearer ')[1];
        
        if (!token) {
          return res.apiUnauthorized('Invalid token format');
        }

        const decodedToken = await admin.auth().verifyIdToken(token);
        req.user = decodedToken;
        
        console.log(`🔐 Token verified for user: ${decodedToken.uid}`);
        next();
      } catch (error) {
        console.error('❌ Token verification failed:', error.message);
        return res.apiUnauthorized('Invalid or expired token');
      }
    };
  }

  getAuth() {
    return admin.auth();
  }

  getFirestore() {
    return admin.firestore();
  }
}

module.exports = FirebaseManager;
