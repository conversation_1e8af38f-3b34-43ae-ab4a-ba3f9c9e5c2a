import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { checkUserStatus, USER_STATUS } from '../utils/userStatus';

/**
 * Hook to handle user authentication flow and redirects
 * @param {Object} options - Configuration options
 * @returns {Object} - User status and actions
 */
export const useAuthFlow = (options = {}) => {
  const { 
    skipRedirect = false, 
    allowedStatuses = [], 
    redirectDelay = 1000 
  } = options;

  const { user, loading } = useAuth();
  const navigate = useNavigate();
  const [userStatus, setUserStatus] = useState(null);
  const [shouldRedirect, setShouldRedirect] = useState(false);

  useEffect(() => {
    if (loading) return;

    const status = checkUserStatus(user);
    setUserStatus(status);

    // Check if current status is allowed
    if (allowedStatuses.length > 0 && !allowedStatuses.includes(status.status)) {
      setShouldRedirect(true);
      return;
    }

    // Auto-redirect logic
    if (!skipRedirect && status.redirectTo) {
      const shouldAutoRedirect = 
        status.status === USER_STATUS.EMAIL_NOT_VERIFIED ||
        status.status === USER_STATUS.NEW_USER ||
        status.status === USER_STATUS.PROFILE_INCOMPLETE;

      if (shouldAutoRedirect) {
        setShouldRedirect(true);
        setTimeout(() => {
          navigate(status.redirectTo);
        }, redirectDelay);
      }
    }
  }, [user, loading, navigate, skipRedirect, allowedStatuses, redirectDelay]);

  const manualRedirect = () => {
    if (userStatus?.redirectTo) {
      navigate(userStatus.redirectTo);
    }
  };

  return {
    userStatus,
    shouldRedirect,
    manualRedirect,
    loading
  };
};

/**
 * Hook specifically for login flow
 */
export const useLoginFlow = () => {
  return useAuthFlow({
    skipRedirect: false,
    redirectDelay: 2000 // Give user time to see success message
  });
};

/**
 * Hook to protect routes that require specific user status
 */
export const useProtectedRoute = (requiredStatus) => {
  return useAuthFlow({
    allowedStatuses: Array.isArray(requiredStatus) ? requiredStatus : [requiredStatus],
    skipRedirect: false,
    redirectDelay: 0
  });
};

/**
 * Hook for dashboard that requires active user
 */
export const useDashboardAccess = () => {
  return useProtectedRoute(USER_STATUS.ACTIVE);
};

/**
 * Hook for profile setup pages
 */
export const useProfileSetupAccess = () => {
  return useProtectedRoute([USER_STATUS.NEW_USER, USER_STATUS.PROFILE_INCOMPLETE]);
};
