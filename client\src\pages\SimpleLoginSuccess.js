import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { checkUserStatus } from '../utils/userStatus';
import { useNavigate } from 'react-router-dom';
import { ApplePageWrapper } from '../components/apple';

const SimpleLoginSuccess = () => {
  const { user, loading } = useAuth();
  const navigate = useNavigate();
  const [userStatus, setUserStatus] = useState(null);
  const [countdown, setCountdown] = useState(3);

  useEffect(() => {
    if (!loading && user) {
      const status = checkUserStatus(user);
      setUserStatus(status);
      console.log('🔍 User status check result:', status);

      // Start countdown for redirect
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            console.log('🚀 Redirecting to:', status.redirectTo);
            navigate(status.redirectTo);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [user, loading, navigate]);

  if (loading) {
    return (
      <ApplePageWrapper>
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-medieval-red-500 mx-auto mb-4"></div>
            <p className="font-cinzel text-medieval-brown-600">Loading...</p>
          </div>
        </div>
      </ApplePageWrapper>
    );
  }

  if (!user) {
    return (
      <ApplePageWrapper>
        <div className="text-center">
          <h1 className="font-cinzel-decorative text-2xl font-bold text-medieval-red-600 mb-4">
            🔐 Login Required
          </h1>
          <p className="text-medieval-brown-600 mb-4">Please login to continue</p>
          <button 
            onClick={() => navigate('/login')}
            className="px-6 py-3 bg-medieval-red-500 text-white rounded-lg hover:bg-medieval-red-600 font-cinzel"
          >
            Go to Login
          </button>
        </div>
      </ApplePageWrapper>
    );
  }

  return (
    <ApplePageWrapper>
      <div className="max-w-lg mx-auto text-center">
        <div className="bg-white rounded-lg border border-medieval-brown-200 p-8 shadow-lg">
          <div className="text-green-500 text-6xl mb-4">✅</div>
          <h1 className="font-cinzel-decorative text-2xl font-bold text-medieval-brown-800 mb-4">
            Login Successful!
          </h1>
          
          {userStatus && (
            <div className="mb-6">
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="text-left space-y-2 text-sm">
                  <div><strong>Status:</strong> {userStatus.status}</div>
                  <div><strong>Message:</strong> {userStatus.message}</div>
                  <div><strong>Next Action:</strong> {userStatus.nextAction}</div>
                  <div><strong>Redirect To:</strong> <code className="bg-gray-200 px-1 rounded">{userStatus.redirectTo}</code></div>
                </div>
              </div>
              
              <p className="font-cinzel text-medieval-brown-600 mb-4">
                Redirecting in <span className="font-bold text-medieval-red-600">{countdown}</span> seconds...
              </p>
              
              <button
                onClick={() => navigate(userStatus.redirectTo)}
                className="px-6 py-3 bg-medieval-red-500 text-white rounded-lg hover:bg-medieval-red-600 font-cinzel"
              >
                Go Now
              </button>
            </div>
          )}

          <div className="border-t pt-4">
            <p className="text-xs text-gray-500">
              Welcome back, {user.displayName || user.email}
            </p>
          </div>
        </div>
      </div>
    </ApplePageWrapper>
  );
};

export default SimpleLoginSuccess;
