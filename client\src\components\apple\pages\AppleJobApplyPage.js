import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useAuth } from '../../../contexts/AuthContext';
import { apiService } from '../../../services/api';
import {
  ArrowLeftIcon,
  CurrencyDollarIcon,
  ClockIcon,
  DocumentTextIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

const AppleJobApplyPage = () => {
  const { id } = useParams();
  const { t } = useLanguage();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [job, setJob] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    proposal: '',
    bidAmount: '',
    estimatedDuration: '',
  });

  useEffect(() => {
    const fetchJob = async () => {
      try {
        setLoading(true);
        const response = await apiService.jobs.getById(id);
        if (response.success) {
          setJob(response.data.job);
        } else {
          navigate('/jobs');
        }
      } catch (error) {
        console.error('Error fetching job:', error);
        navigate('/jobs');
      } finally {
        setLoading(false);
      }
    };

    fetchJob();
  }, [id, navigate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.proposal.trim()) {
      alert('Vui lòng nhập proposal');
      return;
    }

    try {
      setSubmitting(true);
      const response = await apiService.jobs.apply(id, formData);
      
      if (response.success) {
        alert('Ứng tuyển thành công!');
        navigate(`/jobs/${id}`);
      } else {
        alert('Có lỗi xảy ra: ' + response.message);
      }
    } catch (error) {
      console.error('Error applying for job:', error);
      alert('Có lỗi xảy ra khi ứng tuyển');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Đang tải thông tin công việc...</p>
        </div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Không tìm thấy công việc
          </h3>
          <Link
            to="/jobs"
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
          >
            Quay lại danh sách công việc
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center space-x-4">
            <Link
              to={`/jobs/${id}`}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Ứng tuyển công việc
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {job.title} • {job.company}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Application Form */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                Thông tin ứng tuyển
              </h2>

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Proposal */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Proposal của bạn *
                  </label>
                  <textarea
                    name="proposal"
                    value={formData.proposal}
                    onChange={handleInputChange}
                    rows={8}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Mô tả chi tiết về cách bạn sẽ thực hiện công việc này, kinh nghiệm liên quan, và lý do tại sao bạn phù hợp..."
                    required
                  />
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Tối thiểu 50 ký tự, tối đa 2000 ký tự
                  </p>
                </div>

                {/* Bid Amount */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Mức giá đề xuất (VNĐ)
                  </label>
                  <div className="relative">
                    <CurrencyDollarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="number"
                      name="bidAmount"
                      value={formData.bidAmount}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Nhập mức giá bạn đề xuất"
                      min={job.budget.min}
                      max={job.budget.max}
                    />
                  </div>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Ngân sách: {job.budget.min.toLocaleString('vi-VN')} - {job.budget.max.toLocaleString('vi-VN')} VNĐ
                  </p>
                </div>

                {/* Estimated Duration */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Thời gian dự kiến
                  </label>
                  <div className="relative">
                    <ClockIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      name="estimatedDuration"
                      value={formData.estimatedDuration}
                      onChange={handleInputChange}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Ví dụ: 2-3 tuần, 1 tháng..."
                    />
                  </div>
                </div>

                {/* Submit Button */}
                <div className="flex items-center justify-between pt-6">
                  <Link
                    to={`/jobs/${id}`}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    Hủy
                  </Link>
                  <button
                    type="submit"
                    disabled={submitting || !formData.proposal.trim()}
                    className="px-8 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
                  >
                    {submitting ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Đang gửi...</span>
                      </>
                    ) : (
                      <>
                        <CheckCircleIcon className="h-5 w-5" />
                        <span>Gửi ứng tuyển</span>
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Job Summary */}
          <div className="space-y-6">
            {/* Job Info */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Thông tin công việc
              </h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    {job.title}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {job.company} • {job.location}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Ngân sách</p>
                    <p className="font-semibold text-green-600 dark:text-green-400">
                      {job.budget.min.toLocaleString('vi-VN')} - {job.budget.max.toLocaleString('vi-VN')} VNĐ
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Kinh nghiệm</p>
                    <p className="font-semibold text-gray-900 dark:text-white">
                      {job.experience}
                    </p>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Kỹ năng yêu cầu</p>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {job.skills.map((skill, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Hạn nộp</p>
                  <p className="font-semibold text-gray-900 dark:text-white">
                    {new Date(job.deadline).toLocaleDateString('vi-VN')}
                  </p>
                </div>
              </div>
            </div>

            {/* Tips */}
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-2xl p-6 border border-blue-200 dark:border-blue-800">
              <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">
                Mẹo viết proposal hiệu quả
              </h3>
              
              <ul className="space-y-3 text-sm text-blue-800 dark:text-blue-200">
                <li className="flex items-start space-x-2">
                  <DocumentTextIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Giới thiệu bản thân và kinh nghiệm liên quan</span>
                </li>
                <li className="flex items-start space-x-2">
                  <DocumentTextIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Giải thích cách bạn sẽ thực hiện dự án</span>
                </li>
                <li className="flex items-start space-x-2">
                  <DocumentTextIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Đề xuất timeline và milestones cụ thể</span>
                </li>
                <li className="flex items-start space-x-2">
                  <DocumentTextIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Chia sẻ portfolio hoặc dự án tương tự</span>
                </li>
                <li className="flex items-start space-x-2">
                  <DocumentTextIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Đặt câu hỏi để hiểu rõ hơn về yêu cầu</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppleJobApplyPage; 