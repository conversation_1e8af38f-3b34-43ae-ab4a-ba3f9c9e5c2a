const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

// Import local utilities
const { 
  responseMiddleware, 
  validateBody, 
  validateQuery, 
  verifyFirebaseToken, 
  optionalAuth, 
  createPagination, 
  schemas 
} = require('./utils');

const app = express();
const PORT = process.env.PORT || 3003;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3006', 'http://localhost:8080'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token']
}));
app.use(morgan('combined'));
app.use(express.json());
app.use(responseMiddleware);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Project Service',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Mock data
const mockProjects = [
  {
    id: 'project-1',
    title: 'E-commerce Website Development',
    description: 'Build a modern e-commerce website with React and Node.js. The project includes user authentication, product catalog, shopping cart, payment integration, and admin dashboard.',
    category: 'Web Development',
    budget: {
      type: 'fixed',
      amount: 5000,
      currency: 'USD'
    },
    skills: ['React', 'Node.js', 'MongoDB', 'Stripe API'],
    deadline: '2024-03-15T23:59:59.000Z',
    attachments: [
      'https://example.com/wireframes.pdf',
      'https://example.com/requirements.docx'
    ],
    clientId: 'client-1',
    clientName: 'TechCorp Vietnam',
    postedAt: '2024-01-10T09:00:00.000Z',
    status: 'open',
    bidsCount: 15,
    viewsCount: 120
  },
  {
    id: 'project-2',
    title: 'Mobile App UI/UX Design',
    description: 'Design a complete UI/UX for a fitness tracking mobile app. Includes user research, wireframes, prototypes, and final designs for both iOS and Android.',
    category: 'Design',
    budget: {
      type: 'hourly',
      amount: 40,
      currency: 'USD'
    },
    skills: ['Figma', 'UI Design', 'UX Research', 'Prototyping'],
    deadline: '2024-02-28T23:59:59.000Z',
    attachments: [
      'https://example.com/app-concept.pdf'
    ],
    clientId: 'client-2',
    clientName: 'FitLife Startup',
    postedAt: '2024-01-12T14:30:00.000Z',
    status: 'open',
    bidsCount: 8,
    viewsCount: 85
  }
];

// Project routes
app.get('/projects', validateQuery(schemas.pagination), async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      budgetMin,
      budgetMax,
      skills,
      status = 'open'
    } = req.query;

    console.log('📁 Get projects:', { page, limit, category, budgetMin, budgetMax, skills, status });

    // TODO: Apply filters and get from database
    let filteredProjects = [...mockProjects];

    if (category) {
      filteredProjects = filteredProjects.filter(project =>
        project.category.toLowerCase().includes(category.toLowerCase())
      );
    }

    if (status) {
      filteredProjects = filteredProjects.filter(project => project.status === status);
    }

    if (skills) {
      const skillsArray = Array.isArray(skills) ? skills : [skills];
      filteredProjects = filteredProjects.filter(project =>
        skillsArray.some(skill =>
          project.skills.some(projectSkill =>
            projectSkill.toLowerCase().includes(skill.toLowerCase())
          )
        )
      );
    }

    if (budgetMin || budgetMax) {
      filteredProjects = filteredProjects.filter(project => {
        if (project.budget.type === 'fixed') {
          const amount = project.budget.amount;
          if (budgetMin && amount < parseInt(budgetMin)) return false;
          if (budgetMax && amount > parseInt(budgetMax)) return false;
        }
        return true;
      });
    }

    const total = filteredProjects.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedProjects = filteredProjects.slice(startIndex, endIndex);

    const pagination = createPagination(page, limit, total);

    res.apiSuccess(paginatedProjects, 'Projects retrieved successfully', pagination);

  } catch (error) {
    console.error('❌ Get projects failed:', error);
    res.apiError('Failed to get projects', 'GET_PROJECTS_ERROR', 500);
  }
});

app.get('/projects/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`📁 Get project details: ${id}`);

    // TODO: Get project from database and increment view count
    const project = mockProjects.find(p => p.id === id);

    if (!project) {
      return res.apiNotFound('Project not found');
    }

    // Increment view count
    project.viewsCount += 1;

    res.apiSuccess(project, 'Project details retrieved');

  } catch (error) {
    console.error('❌ Get project failed:', error);
    res.apiError('Failed to get project details', 'GET_PROJECT_ERROR', 500);
  }
});

app.post('/projects', verifyFirebaseToken, validateBody(schemas.projectCreate), async (req, res) => {
  try {
    const projectData = req.body;

    console.log('📁 Create project:', projectData.title);

    // TODO: Save project to database
    const newProject = {
      id: `project-${Date.now()}`,
      ...projectData,
      clientId: req.user.uid,
      clientName: req.user.name || 'Unknown Client',
      postedAt: new Date().toISOString(),
      status: 'open',
      bidsCount: 0,
      viewsCount: 0
    };

    console.log('✅ Project created:', newProject.id);

    res.apiSuccess(newProject, 'Project created successfully');

  } catch (error) {
    console.error('❌ Create project failed:', error);
    res.apiError('Failed to create project', 'CREATE_PROJECT_ERROR', 500);
  }
});

app.put('/projects/:id', verifyFirebaseToken, async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    console.log(`📁 Update project: ${id}`);

    // TODO: Check ownership and update in database
    const updatedProject = {
      id,
      ...updateData,
      updatedAt: new Date().toISOString()
    };

    res.apiSuccess(updatedProject, 'Project updated successfully');

  } catch (error) {
    console.error('❌ Update project failed:', error);
    res.apiError('Failed to update project', 'UPDATE_PROJECT_ERROR', 500);
  }
});

app.delete('/projects/:id', verifyFirebaseToken, async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`📁 Delete project: ${id}`);

    // TODO: Check ownership and delete from database

    res.apiSuccess(null, 'Project deleted successfully');

  } catch (error) {
    console.error('❌ Delete project failed:', error);
    res.apiError('Failed to delete project', 'DELETE_PROJECT_ERROR', 500);
  }
});

app.post('/projects/:id/bids', verifyFirebaseToken, async (req, res) => {
  try {
    const { id } = req.params;
    const bidData = req.body;

    console.log(`📁 Submit bid for project: ${id}`);

    // TODO: Save bid to database
    const bid = {
      id: `bid-${Date.now()}`,
      projectId: id,
      freelancerId: req.user.uid,
      freelancerName: req.user.name || 'Unknown Freelancer',
      ...bidData,
      submittedAt: new Date().toISOString(),
      status: 'pending'
    };

    console.log('✅ Bid submitted:', bid.id);

    res.apiSuccess(bid, 'Bid submitted successfully');

  } catch (error) {
    console.error('❌ Submit bid failed:', error);
    res.apiError('Failed to submit bid', 'SUBMIT_BID_ERROR', 500);
  }
});

app.get('/projects/:id/bids', verifyFirebaseToken, async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`📁 Get project bids: ${id}`);

    // TODO: Check ownership and get bids from database
    const mockBids = [
      {
        id: 'bid-1',
        projectId: id,
        freelancerId: 'freelancer-1',
        freelancerName: 'Alice Johnson',
        amount: 4500,
        currency: 'USD',
        timeline: '6 weeks',
        proposal: 'I have extensive experience in React and Node.js...',
        submittedAt: '2024-01-11T10:00:00.000Z',
        status: 'pending'
      }
    ];

    res.apiSuccess(mockBids, 'Project bids retrieved');

  } catch (error) {
    console.error('❌ Get bids failed:', error);
    res.apiError('Failed to get bids', 'GET_BIDS_ERROR', 500);
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('🚨 Project Service Error:', error);
  res.apiError('Internal server error', 'INTERNAL_ERROR', 500);
});

app.listen(PORT, () => {
  console.log(`📁 Project Service running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
