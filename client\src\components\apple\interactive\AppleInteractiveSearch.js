import React, { useRef, useEffect, useState } from 'react';
import { gsap } from 'gsap';
import { useLanguage } from '../../../contexts/LanguageContext';
import {
  MagnifyingGlassIcon,
  MapPinIcon,
  AdjustmentsHorizontalIcon,
  XMarkIcon,
  ClockIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';
import { animationController } from '../../../utils/animations';

const AppleInteractiveSearch = () => {
  const { t } = useLanguage();
  const sectionRef = useRef(null);
  const searchRef = useRef(null);
  const suggestionsRef = useRef(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [locationQuery, setLocationQuery] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [isLocationFocused, setIsLocationFocused] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);

  const popularSearches = [
    t('reactDeveloper'),
    t('uiUxDesigner'),
    t('contentWriter'),
    t('digitalMarketing'),
    t('mobileAppDeveloper'),
    t('dataScientist'),
  ];

  const popularLocations = [
    'Remote',
    'New York, NY',
    'San Francisco, CA',
    'Los Angeles, CA',
    'Chicago, IL',
    'Austin, TX',
  ];

  const filterOptions = [
    { id: 'hourly', label: t('hourlyRate'), icon: CurrencyDollarIcon },
    { id: 'experience', label: t('experienceLevel'), icon: ClockIcon },
    { id: 'availability', label: t('availability'), icon: ClockIcon },
  ];

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Search container animation
      if (searchRef.current) {
        animationController.fadeIn(searchRef.current, {
          scrollTrigger: {
            trigger: searchRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse',
          },
        });
      }

      // Suggestions animation
      if (suggestionsRef.current && suggestionsRef.current.children) {
        const suggestionElements = Array.from(suggestionsRef.current.children);
        
        animationController.scaleIn(suggestionElements, {
          delay: 0.3,
          stagger: 0.05,
          scrollTrigger: {
            trigger: suggestionsRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse',
          },
        });
      }
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const handleFilterToggle = (filterId) => {
    setSelectedFilters(prev => 
      prev.includes(filterId) 
        ? prev.filter(id => id !== filterId)
        : [...prev, filterId]
    );
  };

  const handleSearch = () => {
    console.log('Searching for:', { searchQuery, locationQuery, selectedFilters });
    // Implement search logic here
  };

  return (
    <section
      ref={sectionRef}
      className='py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 transition-colors duration-300'
    >
      <div className='mx-auto max-w-4xl px-4 sm:px-6 lg:px-8'>
        {/* Search Container */}
        <div
          ref={searchRef}
          className='bg-white dark:bg-gray-800 rounded-3xl p-8 shadow-xl border border-gray-100 dark:border-gray-700 transition-colors duration-300'
        >
          {/* Header */}
          <div className='text-center mb-8'>
            <h2 className='text-3xl sm:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4 transition-colors duration-300'>
              {t('findPerfectMatch')}
            </h2>
            <p className='text-lg text-gray-600 dark:text-gray-400 transition-colors duration-300'>
              {t('searchThousandsOfTalented')}
            </p>
          </div>

          {/* Search Form */}
          <div className='space-y-6'>
            {/* Main Search Fields */}
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              {/* Search Input */}
              <div className='relative'>
                <div className='absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none'>
                  <MagnifyingGlassIcon className='h-5 w-5 text-gray-400 dark:text-gray-500' />
                </div>
                <input
                  type='text'
                  placeholder={t('searchSkillsOrJobs')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => setIsSearchFocused(true)}
                  onBlur={() => setIsSearchFocused(false)}
                  className='block w-full pl-12 pr-4 py-4 border border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 transition-all duration-200'
                />
              </div>

              {/* Location Input */}
              <div className='relative'>
                <div className='absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none'>
                  <MapPinIcon className='h-5 w-5 text-gray-400 dark:text-gray-500' />
                </div>
                <input
                  type='text'
                  placeholder={t('location')}
                  value={locationQuery}
                  onChange={(e) => setLocationQuery(e.target.value)}
                  onFocus={() => setIsLocationFocused(true)}
                  onBlur={() => setIsLocationFocused(false)}
                  className='block w-full pl-12 pr-4 py-4 border border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 transition-all duration-200'
                />
              </div>
            </div>

            {/* Filters */}
            <div className='flex flex-wrap gap-3'>
              {filterOptions.map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => handleFilterToggle(filter.id)}
                  className={`inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                    selectedFilters.includes(filter.id)
                      ? 'bg-blue-600 text-white shadow-md'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  <filter.icon className='h-4 w-4 mr-2' />
                  {filter.label}
                  {selectedFilters.includes(filter.id) && (
                    <XMarkIcon className='h-4 w-4 ml-2' />
                  )}
                </button>
              ))}
              <button className='inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200'>
                <AdjustmentsHorizontalIcon className='h-4 w-4 mr-2' />
                {t('moreFilters')}
              </button>
            </div>

            {/* Search Button */}
            <button
              onClick={handleSearch}
              className='w-full px-8 py-4 text-lg font-semibold text-white bg-blue-600 hover:bg-blue-700 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
            >
              {t('searchNow')}
            </button>
          </div>
        </div>

        {/* Popular Searches */}
        <div ref={suggestionsRef} className='mt-12'>
          <div className='text-center mb-8'>
            <h3 className='text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 transition-colors duration-300'>
              {t('popularSearches')}
            </h3>
          </div>
          
          <div className='flex flex-wrap justify-center gap-3'>
            {popularSearches.map((search, index) => (
              <button
                key={index}
                onClick={() => setSearchQuery(search)}
                className='px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 transition-all duration-200 hover:shadow-md'
              >
                {search}
              </button>
            ))}
          </div>

          <div className='text-center mt-8'>
            <h3 className='text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 transition-colors duration-300'>
              {t('popularLocations')}
            </h3>
          </div>
          
          <div className='flex flex-wrap justify-center gap-3'>
            {popularLocations.map((location, index) => (
              <button
                key={index}
                onClick={() => setLocationQuery(location)}
                className='px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 transition-all duration-200 hover:shadow-md'
              >
                {location}
              </button>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AppleInteractiveSearch;
