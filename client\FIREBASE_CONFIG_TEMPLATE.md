# Firebase Configuration Template

## Create .env file

Tạo file `.env` trong thư mục `client/` với nội dung sau:

```env
# Firebase Configuration (thay bằng config thật từ Firebase Console)
REACT_APP_FIREBASE_API_KEY=your_api_key_here
REACT_APP_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your_project_id_here
REACT_APP_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id_here
REACT_APP_FIREBASE_APP_ID=your_app_id_here

# Development settings
GENERATE_SOURCEMAP=false
SKIP_PREFLIGHT_CHECK=true
```

## Cách lấy Firebase Config

1. Vào [Firebase Console](https://console.firebase.google.com/)
2. Tạo project mới hoặc chọn project có sẵn
3. Vào Project Settings (icon bánh răng)
4. Cuộn xuống phần "Your apps"
5. Copy config values và paste vào file .env

## Chạy without Firebase (Development Mode)

Nếu chưa có Firebase config, ứng dụng vẫn chạy được với mock data và hiển thị warnings trong console. 