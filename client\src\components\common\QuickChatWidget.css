/* Floating Widget Container - Modern Design */
.floating-widget-container {
  position: fixed;
  bottom: 32px;
  right: 32px;
  z-index: 1000;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Chat Toggle Button */
.chat-toggle-btn {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: #007aff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.25);
  transition: all 0.2s ease;
  transform: scale(1);
}

.chat-toggle-btn:hover {
  transform: scale(1.02);
  box-shadow: 0 6px 20px rgba(0, 122, 255, 0.35);
  background: #0056cc;
}

.chat-toggle-btn.open {
  background: #6b7280;
  box-shadow: 0 4px 16px rgba(107, 114, 128, 0.25);
}

.chat-toggle-btn.open:hover {
  background: #4b5563;
  box-shadow: 0 6px 20px rgba(107, 114, 128, 0.35);
}

/* Chat Window */
.chat-window {
  position: absolute;
  bottom: 76px;
  right: 0;
  width: 380px;
  height: 520px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08), 0 8px 25px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(229, 231, 235, 0.8);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.chat-window.minimized {
  height: 64px;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(16px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Chat Header */
.chat-header {
  background: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
  color: #1f2937;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Header Tabs */
.header-tabs {
  display: flex;
  gap: 4px;
  background: rgba(229, 231, 235, 0.3);
  border-radius: 8px;
  padding: 2px;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 6px;
  border: none;
  background: transparent;
  color: #6b7280;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.5);
  color: #374151;
}

.tab-btn.active {
  background: white;
  color: #007aff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bot-avatar {
  width: 40px;
  height: 40px;
  background: #007aff;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
}

.header-info h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.header-info .status {
  font-size: 12px;
  color: #34c759;
  margin-top: 2px;
  display: block;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.minimize-btn {
  width: 32px;
  height: 32px;
  background: rgba(107, 114, 128, 0.1);
  border: none;
  border-radius: 8px;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.minimize-btn:hover {
  background: rgba(107, 114, 128, 0.15);
  color: #374151;
}

/* Chat Messages */
.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  scroll-behavior: smooth;
  background: rgba(249, 250, 251, 0.5);
}

.chat-messages::-webkit-scrollbar {
  width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.4);
  border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.6);
}

/* Message Styles */
.message {
  display: flex;
  max-width: 85%;
  animation: messageSlide 0.3s ease-out;
}

@keyframes messageSlide {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bot-message {
  align-self: flex-start;
}

.user-message {
  align-self: flex-end;
}

.message-content {
  background: white;
  padding: 12px 16px;
  border-radius: 16px;
  position: relative;
  word-wrap: break-word;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(229, 231, 235, 0.6);
}

.bot-message .message-content {
  background: white;
  border-bottom-left-radius: 6px;
  border: 1px solid rgba(229, 231, 235, 0.6);
}

.user-message .message-content {
  background: #007aff;
  color: white;
  border-bottom-right-radius: 6px;
  border: 1px solid rgba(0, 122, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.15);
}

.message-content p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: inherit;
}

.bot-message .message-content p {
  color: #374151;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  margin-top: 6px;
  display: block;
  font-weight: 500;
}

.user-message .message-time {
  opacity: 0.8;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 0;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: #9ca3af;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Chat Input */
.chat-input {
  padding: 16px 20px;
  border-top: 1px solid rgba(229, 231, 235, 0.8);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  background: white;
  border-radius: 12px;
  padding: 8px;
  border: 1px solid rgba(229, 231, 235, 0.8);
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.input-container:focus-within {
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.input-container textarea {
  flex: 1;
  border: none;
  outline: none;
  resize: none;
  font-size: 14px;
  font-family: inherit;
  line-height: 1.5;
  max-height: 100px;
  padding: 8px;
  background: transparent;
  color: #374151;
}

.input-container textarea::placeholder {
  color: #9ca3af;
}

.send-btn {
  width: 36px;
  height: 36px;
  background: #007aff;
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 122, 255, 0.2);
}

.send-btn:hover:not(:disabled) {
  transform: scale(1.02);
  background: #0056cc;
  box-shadow: 0 4px 8px rgba(0, 122, 255, 0.3);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  background: #9ca3af;
  box-shadow: none;
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .quick-chat-widget {
    bottom: 20px;
    right: 20px;
  }

  .chat-window {
    width: calc(100vw - 40px);
    right: -20px;
    height: 480px;
    border-radius: 16px;
  }

  .chat-toggle-btn {
    width: 52px;
    height: 52px;
    border-radius: 14px;
  }

  .chat-header {
    padding: 14px 16px;
  }

  .header-info h4 {
    font-size: 15px;
  }

  .header-info .status {
    font-size: 11px;
  }

  .chat-messages {
    padding: 16px;
  }

  .chat-input {
    padding: 14px 16px;
  }
}

@media (max-width: 360px) {
  .chat-window {
    height: 420px;
  }

  .chat-messages {
    padding: 12px;
  }
}

/* Quick Actions */
.quick-actions {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.actions-header {
  margin-bottom: 20px;
}

.actions-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.actions-header p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.actions-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.action-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid rgba(229, 231, 235, 0.8);
  background: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.action-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.action-blue .action-icon {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.action-green .action-icon {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.action-purple .action-icon {
  background: rgba(168, 85, 247, 0.1);
  color: #a855f7;
}

.action-orange .action-icon {
  background: rgba(249, 115, 22, 0.1);
  color: #f97316;
}

.action-content {
  flex: 1;
}

.action-content h4 {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 2px 0;
}

.action-content p {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* Dark Mode for Quick Actions */
@media (prefers-color-scheme: dark) {
  .header-tabs {
    background: rgba(75, 85, 99, 0.3);
  }

  .tab-btn {
    color: #9ca3af;
  }

  .tab-btn:hover {
    background: rgba(55, 65, 81, 0.5);
    color: #f3f4f6;
  }

  .tab-btn.active {
    background: rgba(55, 65, 81, 0.8);
    color: #60a5fa;
  }

  .actions-header h3 {
    color: #f9fafb;
  }

  .actions-header p {
    color: #9ca3af;
  }

  .action-card {
    background: rgba(55, 65, 81, 0.8);
    border-color: rgba(75, 85, 99, 0.6);
  }

  .action-card:hover {
    border-color: rgba(96, 165, 250, 0.4);
    background: rgba(55, 65, 81, 0.9);
  }

  .action-content h4 {
    color: #f9fafb;
  }

  .action-content p {
    color: #9ca3af;
  }
}

/* ===== MODERN FLOATING CIRCLES DESIGN ===== */

/* Main Floating Button */
.main-floating-btn {
  width: 72px;
  height: 72px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 8px 32px rgba(102, 126, 234, 0.4),
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.main-floating-btn .btn-content {
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.main-floating-btn .btn-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.main-floating-btn:hover {
  transform: translateY(-4px) scale(1.1);
  box-shadow:
    0 16px 48px rgba(102, 126, 234, 0.5),
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.main-floating-btn:hover .btn-glow {
  opacity: 1;
}

.main-floating-btn:active {
  transform: translateY(-2px) scale(1.05);
  transition: all 0.1s ease;
}

.main-floating-btn.expanded {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  box-shadow:
    0 8px 32px rgba(255, 107, 107, 0.4),
    0 4px 16px rgba(0, 0, 0, 0.1);
}

.main-floating-btn.expanded:hover {
  box-shadow:
    0 16px 48px rgba(255, 107, 107, 0.5),
    0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Floating Circles Container - Vertical stack above main button with ultra-wide spacing */
.floating-circles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 1001;
  width: 80px;
  height: 700px; /* Increased height for ultra-wide spacing */
}

/* Individual Floating Circle - Enhanced Beauty */
.floating-circle {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex !important;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  text-decoration: none;
  color: white;
  pointer-events: auto;
  left: calc(50% + var(--x, 0px));
  top: calc(50% + var(--y, 0px));
  transform: translate(-50%, -50%);
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 2px 0 rgba(255, 255, 255, 0.3),
    inset 0 -2px 0 rgba(0, 0, 0, 0.1);
  z-index: 1002;
  opacity: 1;
  visibility: visible;
  overflow: hidden;
  position: relative;
}

.floating-circle:hover {
  transform: translate(-50%, -50%) scale(1.2) translateY(-4px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.25),
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.4),
    inset 0 -2px 0 rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.floating-circle:active {
  transform: translate(var(--x), var(--y)) scale(1.1) translateY(-2px);
  transition: all 0.1s ease;
}

/* Enhanced Circle Colors with Glass Morphism */
.circle-blue {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.9) 0%,
    rgba(29, 78, 216, 0.9) 100%);
  position: relative;
}

.circle-blue::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 1;
}

.circle-green {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.9) 0%,
    rgba(4, 120, 87, 0.9) 100%);
  position: relative;
}

.circle-green::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 1;
}

.circle-purple {
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.9) 0%,
    rgba(109, 40, 217, 0.9) 100%);
  position: relative;
}

.circle-purple::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 1;
}

.circle-orange {
  background: linear-gradient(135deg,
    rgba(245, 158, 11, 0.9) 0%,
    rgba(217, 119, 6, 0.9) 100%);
  position: relative;
}

.circle-orange::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 1;
}

.circle-pink {
  background: linear-gradient(135deg,
    rgba(236, 72, 153, 0.9) 0%,
    rgba(190, 24, 93, 0.9) 100%);
  position: relative;
}

.circle-pink::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 1;
}

/* Circle Tooltips */
.circle-tooltip {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.floating-circle:hover .circle-tooltip {
  opacity: 1;
}

/* Enhanced Circle icon styling */
.floating-circle svg {
  width: 26px;
  height: 26px;
  color: white;
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.3));
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.floating-circle:hover svg {
  transform: scale(1.1);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4));
}

/* Modern Chat Window */
.modern-chat-window {
  position: absolute;
  bottom: 100px;
  right: 0;
  width: 400px;
  height: 550px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border-radius: 24px;
  box-shadow:
    0 24px 64px rgba(0, 0, 0, 0.12),
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modern-chat-window.minimized {
  height: 60px;
}

/* Enhanced Chat Header */
.modern-chat-window .chat-header {
  background: rgba(247, 250, 252, 0.8);
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.modern-chat-window .header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modern-chat-window .bot-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.modern-chat-window .header-info h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.modern-chat-window .header-info .status {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

.modern-chat-window .header-actions {
  display: flex;
  gap: 8px;
}

.modern-chat-window .minimize-btn,
.modern-chat-window .close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.modern-chat-window .minimize-btn:hover,
.modern-chat-window .close-btn:hover {
  background: rgba(107, 114, 128, 0.2);
  color: #374151;
}

/* Responsive Design for Floating Circles */
@media (max-width: 768px) {
  .floating-widget-container {
    bottom: 24px;
    right: 24px;
  }

  .main-floating-btn {
    width: 64px;
    height: 64px;
  }

  .floating-circle {
    width: 48px;
    height: 48px;
  }

  .modern-chat-window {
    width: calc(100vw - 48px);
    right: -24px;
    height: 480px;
    border-radius: 20px;
  }
}

@media (max-width: 480px) {
  .main-floating-btn {
    width: 56px;
    height: 56px;
  }

  .floating-circle {
    width: 44px;
    height: 44px;
  }

  .modern-chat-window {
    height: 420px;
    border-radius: 16px;
  }
}

/* Dark Mode Support for Floating Circles */
@media (prefers-color-scheme: dark) {
  .modern-chat-window {
    background: rgba(31, 41, 55, 0.95);
    border: 1px solid rgba(75, 85, 99, 0.8);
  }

  .modern-chat-window .chat-header {
    background: rgba(17, 24, 39, 0.9);
    border-bottom: 1px solid rgba(75, 85, 99, 0.8);
  }

  .modern-chat-window .header-info h4 {
    color: #f9fafb;
  }

  .modern-chat-window .header-info .status {
    color: #34d399;
  }

  .modern-chat-window .minimize-btn,
  .modern-chat-window .close-btn {
    background: rgba(75, 85, 99, 0.3);
    color: #9ca3af;
  }

  .modern-chat-window .minimize-btn:hover,
  .modern-chat-window .close-btn:hover {
    background: rgba(75, 85, 99, 0.5);
    color: #f9fafb;
  }

  .circle-tooltip {
    background: rgba(17, 24, 39, 0.9);
    border: 1px solid rgba(75, 85, 99, 0.6);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .chat-window {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(75, 85, 99, 0.8);
  }

  .chat-header {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(75, 85, 99, 0.8);
    color: #f9fafb;
  }

  .header-info h4 {
    color: #f9fafb;
  }

  .chat-messages {
    background: rgba(17, 24, 39, 0.3);
  }

  .bot-message .message-content {
    background: rgba(55, 65, 81, 0.9);
    border-color: rgba(75, 85, 99, 0.6);
    color: #f3f4f6;
  }

  .bot-message .message-content p {
    color: #f3f4f6;
  }

  .chat-input {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(75, 85, 99, 0.8);
  }

  .input-container {
    background: rgba(55, 65, 81, 0.8);
    border-color: rgba(75, 85, 99, 0.8);
  }

  .input-container textarea {
    color: #f3f4f6;
  }

  .input-container textarea::placeholder {
    color: #9ca3af;
  }
} 