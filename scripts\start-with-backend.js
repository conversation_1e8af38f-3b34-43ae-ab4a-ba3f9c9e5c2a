#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

const SERVICES = [
  { name: 'auth-service', port: 3001 },
  { name: 'user-service', port: 3002 },
  { name: 'project-service', port: 3003 },
  { name: 'payment-service', port: 3004 },
  { name: 'chat-service', port: 3005 }
];

console.log('🚀 Starting VWork Microservices...');
console.log('================================\n');

// Kill existing processes on service ports
const killPort = require('kill-port');
async function killServicePorts() {
  const ports = SERVICES.map(s => s.port);
  try {
    await killPort(ports);
    console.log('✅ Killed existing processes on service ports');
  } catch (error) {
    console.log('⚠️ No processes to kill on service ports');
  }
}

// Start a service
function startService(serviceName, port) {
  const servicePath = path.join('services', serviceName);
  
  console.log(`🔧 Starting ${serviceName} on port ${port}...`);
  
  const child = spawn('npm', ['start'], {
    cwd: servicePath,
    stdio: 'pipe',
    shell: true
  });

  child.stdout.on('data', (data) => {
    console.log(`[${serviceName}] ${data.toString().trim()}`);
  });

  child.stderr.on('data', (data) => {
    console.log(`[${serviceName}] ERROR: ${data.toString().trim()}`);
  });

  child.on('close', (code) => {
    console.log(`[${serviceName}] Process exited with code ${code}`);
  });

  return child;
}

// Start all services
async function startAllServices() {
  await killServicePorts();
  
  const processes = [];
  
  SERVICES.forEach(service => {
    try {
      const process = startService(service.name, service.port);
      processes.push(process);
    } catch (error) {
      console.log(`❌ Failed to start ${service.name}: ${error.message}`);
    }
  });

  console.log('\n🎉 All services started!');
  console.log('\n📋 Service URLs:');
  SERVICES.forEach(service => {
    console.log(`   ${service.name}: http://localhost:${service.port}`);
  });
  console.log('   API Gateway: http://localhost:8080');
  console.log('   Frontend: http://localhost:3000');
  
  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down services...');
    processes.forEach(p => p.kill('SIGINT'));
    process.exit(0);
  });
}

// Run if called directly
if (require.main === module) {
  startAllServices().catch(console.error);
}

module.exports = startAllServices; 