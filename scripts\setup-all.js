#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const SERVICES = [
  'auth-service',
  'user-service',
  'project-service',
  'payment-service',
  'chat-service',
  'api-gateway'
];

async function setupAll() {
  console.log('🚀 VWork Complete Setup - Microservices Architecture');
  console.log('===================================================\n');

  try {
    // Step 1: Setup environment
    console.log('⚙️ Step 1: Setting up environment...');
    execSync('node scripts/setup-env.js', { stdio: 'inherit' });
    console.log('✅ Environment configured\n');

    // Step 2: Install root dependencies
    console.log('📦 Step 2: Installing root dependencies...');
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ Root dependencies installed\n');

    // Step 3: Setup client
    setupClient();

    // Step 4: Setup microservices
    setupMicroservices();

    // Step 5: Test everything
    await testInstallation();

    console.log('\n🎉 VWork setup completed successfully!');
    console.log('\n📋 Quick start:');
    console.log('1. Start everything: npm start');
    console.log('2. Start client only: npm run start:client');
    console.log('3. Start microservices: npm run start:backend');
    console.log('4. Access frontend: http://localhost:3000');
    console.log('5. Access API Gateway: http://localhost:8080');
    console.log('6. Test API: http://localhost:8080/health');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  }
}

function setupClient() {
  const clientPath = path.join('client');
  if (fs.existsSync(clientPath)) {
    console.log('🖥️ Setting up client...');
    // Create .env if missing
    const envPath = path.join(clientPath, '.env');
    if (!fs.existsSync(envPath)) {
      fs.writeFileSync(envPath, `# Firebase Configuration\nREACT_APP_FIREBASE_API_KEY=\nREACT_APP_FIREBASE_AUTH_DOMAIN=\nREACT_APP_FIREBASE_PROJECT_ID=\nREACT_APP_FIREBASE_STORAGE_BUCKET=\nREACT_APP_FIREBASE_MESSAGING_SENDER_ID=\nREACT_APP_FIREBASE_APP_ID=\nREACT_APP_API_URL=http://localhost:8080\nNODE_ENV=development\n`);
      console.log('📝 Created client/.env template. Please fill in your Firebase config!');
    }
    execSync('cd client && npm install', { stdio: 'inherit' });
    console.log('✅ Client setup complete');
  } else {
    console.log('⚠️ Client directory not found, skipping...');
  }
}

function setupMicroservices() {
  SERVICES.forEach(service => {
    const servicePath = path.join('services', service);
    if (fs.existsSync(servicePath)) {
      console.log(`🔧 Setting up ${service}...`);
      // Create .env if missing
      const envPath = path.join(servicePath, '.env');
      if (!fs.existsSync(envPath)) {
        fs.writeFileSync(envPath, `# ${service} ENV\nPORT=3000\nNODE_ENV=development\n`);
        console.log(`📝 Created ${service}/.env template.`);
      }
      // Create package.json if missing
      const packageJsonPath = path.join(servicePath, 'package.json');
      if (!fs.existsSync(packageJsonPath)) {
        createServicePackageJson(service, packageJsonPath);
      }
      // Install dependencies
      execSync(`cd ${servicePath} && npm install`, { stdio: 'inherit' });
      console.log(`✅ ${service} setup complete`);
    } else {
      console.log(`⚠️ ${service} directory not found, skipping...`);
    }
  });
}

function createServicePackageJson(serviceName, packageJsonPath) {
  const packageJson = {
    name: serviceName,
    version: "1.0.0",
    description: `${serviceName} for VWork platform`,
    main: "src/index.js",
    scripts: {
      start: "node src/index.js",
      dev: "nodemon src/index.js",
      test: "jest"
    },
    dependencies: {
      "express": "^4.18.2",
      "cors": "^2.8.5",
      "dotenv": "^16.3.1",
      "helmet": "^7.1.0",
      "morgan": "^1.10.0"
    },
    devDependencies: {
      "nodemon": "^3.0.2",
      "jest": "^29.7.0"
    }
  };
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
}

async function testInstallation() {
  try {
    // Test if client can build
    console.log('🔍 Testing client build...');
    execSync('cd client && npm run build --silent', { stdio: 'ignore' });
    console.log('✅ Client build successful');

    // Test if API Gateway exists
    const gatewayPath = path.join('services', 'api-gateway');
    if (fs.existsSync(gatewayPath)) {
      console.log('✅ API Gateway found');
    } else {
      console.log('⚠️ API Gateway not found');
    }
  } catch (error) {
    console.log('⚠️ Some tests failed, but setup may still work');
  }
}

// Run if called directly
if (require.main === module) {
  setupAll();
}

module.exports = setupAll; 