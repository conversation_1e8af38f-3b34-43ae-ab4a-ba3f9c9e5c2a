{"env": {"browser": true, "es2021": true, "node": true, "jest": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "react-hooks"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "react/no-unescaped-entities": "off", "no-unused-vars": "error", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "error", "no-alert": "error", "no-debugger": "error", "no-console": "error", "no-restricted-syntax": ["error", {"selector": "CallExpression[callee.object.name='console']", "message": "Console statements are not allowed in production. Use proper logging service."}, {"selector": "CallExpression[callee.name='alert']", "message": "alert() is not allowed in production. Use proper error handling."}, {"selector": "CallExpression[callee.name='debugger']", "message": "debugger statements are not allowed in production."}], "no-restricted-globals": ["error", {"name": "alert", "message": "alert() is not allowed in production. Use proper error handling."}]}, "settings": {"react": {"version": "detect"}}, "ignorePatterns": ["build/**", "node_modules/**", "public/**", "*.config.js"]}