const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const { createProxyMiddleware } = require('http-proxy-middleware');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 8080;

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN ? 
    process.env.CORS_ORIGIN.split(',') : 
    ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token']
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Logging
app.use(morgan('combined'));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    service: 'VWork API Gateway',
    version: '1.0.0'
  });
});

// API v1 routes
app.use('/api/v1', (req, res, next) => {
  console.log(`🌐 API Gateway: ${req.method} ${req.path}`);
  next();
});

// Get service URLs from environment variables
const AUTH_SERVICE_URL = process.env.AUTH_SERVICE_URL || 'http://localhost:3001';
const USER_SERVICE_URL = process.env.USER_SERVICE_URL || 'http://localhost:3002';
const PROJECT_SERVICE_URL = process.env.PROJECT_SERVICE_URL || 'http://localhost:3003';
const JOB_SERVICE_URL = process.env.JOB_SERVICE_URL || 'http://localhost:3004';
const CHAT_SERVICE_URL = process.env.CHAT_SERVICE_URL || 'http://localhost:3005';
const SEARCH_SERVICE_URL = process.env.SEARCH_SERVICE_URL || 'http://localhost:3009';
const PAYMENT_SERVICE_URL = process.env.PAYMENT_SERVICE_URL || 'http://localhost:3006';

// Auth service proxy
app.use('/api/v1/auth', createProxyMiddleware({
  target: AUTH_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/auth': '/auth'
  },
  onError: (err, req, res) => {
    console.log('❌ Auth service error:', err.message);
    res.status(503).json({
      error: 'Auth service unavailable',
      message: 'Authentication service is currently unavailable. Please try again later.'
    });
  }
}));

// User service proxy
app.use('/api/v1/users', createProxyMiddleware({
  target: USER_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/users': '/users'
  },
  onError: (err, req, res) => {
    console.log('❌ User service error:', err.message);
    res.status(503).json({
      error: 'User service unavailable',
      message: 'User service is currently unavailable. Please try again later.'
    });
  }
}));

// Project service proxy
app.use('/api/v1/projects', createProxyMiddleware({
  target: PROJECT_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/projects': '/projects'
  },
  onError: (err, req, res) => {
    console.log('❌ Project service error:', err.message);
    res.status(503).json({
      error: 'Project service unavailable',
      message: 'Project service is currently unavailable. Please try again later.'
    });
  }
}));

// Payment service proxy
app.use('/api/v1/payments', createProxyMiddleware({
  target: PAYMENT_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/payments': '/payments'
  },
  onError: (err, req, res) => {
    console.log('❌ Payment service error:', err.message);
    res.status(503).json({
      error: 'Payment service unavailable',
      message: 'Payment service is currently unavailable. Please try again later.'
    });
  }
}));

// Job service proxy
app.use('/api/v1/jobs', createProxyMiddleware({
  target: JOB_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/jobs': '/jobs'
  },
  onError: (err, req, res) => {
    console.log('❌ Job service error:', err.message);
    res.status(503).json({
      error: 'Job service unavailable',
      message: 'Job service is currently unavailable. Please try again later.'
    });
  }
}));

// Search service proxy
app.use('/api/v1/search', createProxyMiddleware({
  target: SEARCH_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/search': '/search'
  },
  onError: (err, req, res) => {
    console.log('❌ Search service error:', err.message);
    res.status(503).json({
      error: 'Search service unavailable',
      message: 'Search service is currently unavailable. Please try again later.'
    });
  }
}));

// Chat service proxy
app.use('/api/v1/chat', createProxyMiddleware({
  target: CHAT_SERVICE_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/api/v1/chat': '/chat'
  },
  onError: (err, req, res) => {
    console.log('❌ Chat service error:', err.message);
    res.status(503).json({
      error: 'Chat service unavailable',
      message: 'Chat service is currently unavailable. Please try again later.'
    });
  }
}));

// Mock endpoints for development when services are not running
app.post('/api/v1/auth/login', (req, res) => {
  console.log('🔐 Mock login endpoint called');
  res.json({
    success: true,
    message: 'Login successful (mock)',
    user: {
      id: 'mock-user-id',
      email: req.body.email,
      name: 'Mock User',
      role: 'freelancer'
    },
    token: 'mock-jwt-token'
  });
});

app.get('/api/v1/auth/me', (req, res) => {
  console.log('👤 Mock get current user endpoint called');
  res.json({
    success: true,
    user: {
      id: 'mock-user-id',
      email: '<EMAIL>',
      name: 'Mock User',
      role: 'freelancer'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('❌ API Gateway Error:', err);
  res.status(500).json({
    error: 'Internal Server Error',
    message: 'Something went wrong on the server'
  });
});

app.listen(PORT, () => {
  console.log(`🚀 VWork API Gateway running on port ${PORT}`);
  console.log(`📡 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api/v1`);
}); 