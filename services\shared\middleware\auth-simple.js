/**
 * Simple Authentication Middleware
 * Basic auth without external dependencies
 */

/**
 * Simple token verification (for development/testing)
 */
const verifyFirebaseToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.apiUnauthorized('No token provided');
    }

    // For now, just check if token exists
    // In production, this should call Auth Service
    if (token && token !== 'undefined' && token !== 'null') {
      // Mock user data for development
      req.user = {
        uid: 'mock-user-id',
        email: '<EMAIL>',
        name: 'Mock User',
        userType: 'freelancer'
      };
      next();
    } else {
      res.apiUnauthorized('Invalid token');
    }

  } catch (error) {
    console.error('❌ Token verification failed:', error.message);
    res.apiUnauthorized('Invalid token');
  }
};

/**
 * Optional authentication - doesn't fail if no token
 */
const optionalAuth = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token || token === 'undefined' || token === 'null') {
      return next(); // Continue without user data
    }

    // Mock user data for development
    req.user = {
      uid: 'mock-user-id',
      email: '<EMAIL>',
      name: 'Mock User',
      userType: 'freelancer'
    };

    next();

  } catch (error) {
    console.error('❌ Optional auth failed:', error.message);
    next(); // Continue without user data
  }
};

/**
 * Require specific role
 */
const requireRole = (role) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.apiUnauthorized('Authentication required');
    }

    if (req.user.userType !== role) {
      return res.apiForbidden(`Role '${role}' required`);
    }

    next();
  };
};

/**
 * Require ownership of resource
 */
const requireOwnership = (paramName = 'id') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.apiUnauthorized('Authentication required');
    }

    const resourceId = req.params[paramName];
    
    if (req.user.uid !== resourceId) {
      return res.apiForbidden('Access denied - not owner');
    }

    next();
  };
};

module.exports = {
  verifyFirebaseToken,
  optionalAuth,
  requireRole,
  requireOwnership
}; 