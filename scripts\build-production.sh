#!/bin/bash

# VWork Production Build Script
# This script builds the entire VWork platform for production deployment

set -e  # Exit on any error

echo "🚀 Building VWork Platform for Production..."
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    error "Node.js is not installed. Please install Node.js 18+ and try again."
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if ! node -e "process.exit(process.version.replace('v', '').split('.').map(Number).reduce((a, b, i) => a + b * Math.pow(10, 4 - i * 2), 0) >= '$REQUIRED_VERSION'.split('.').map(Number).reduce((a, b, i) => a + b * Math.pow(10, 4 - i * 2), 0) ? 0 : 1)"; then
    error "Node.js version $NODE_VERSION is not supported. Please install Node.js $REQUIRED_VERSION or higher."
fi

log "Node.js version: $NODE_VERSION ✅"

# Set production environment
export NODE_ENV=production
export GENERATE_SOURCEMAP=false

# Function to build a service
build_service() {
    local service_name=$1
    local service_path=$2
    
    log "Building $service_name..."
    
    if [ ! -d "$service_path" ]; then
        warn "$service_name directory not found at $service_path"
        return 1
    fi
    
    cd "$service_path"
    
    # Clean install dependencies
    log "Installing dependencies for $service_name..."
    npm ci --production --silent
    
    # Run build script if exists
    if npm run | grep -q "build"; then
        log "Running build script for $service_name..."
        npm run build
    fi
    
    cd - > /dev/null
    log "$service_name build completed ✅"
}

# Create build directory
log "Creating build directory..."
mkdir -p ./build
mkdir -p ./build/services
mkdir -p ./build/client

# Build shared utilities first
log "Building shared utilities..."
cd ./services/shared
npm ci --production --silent
cd - > /dev/null

# Build all services
SERVICES=(
    "API Gateway:./services/api-gateway"
    "Auth Service:./services/auth-service" 
    "User Service:./services/user-service"
    "Project Service:./services/project-service"
    "Job Service:./services/job-service"
    "Chat Service:./services/chat-service"
    "Search Service:./services/search-service"
)

for service in "${SERVICES[@]}"; do
    IFS=':' read -r name path <<< "$service"
    build_service "$name" "$path"
done

# Build frontend
log "Building React frontend..."
cd ./client

# Remove debug/test files for production
log "Removing debug/test files..."
find src -name "*Debug*" -type f -delete 2>/dev/null || true
find src -name "*Test*" -type f -delete 2>/dev/null || true
find src -name "*.test.js" -type f -delete 2>/dev/null || true
find src -name "*.spec.js" -type f -delete 2>/dev/null || true

# Install dependencies and build
npm ci --production --silent
npm run build

# Copy build to main build directory
cp -r build/* ../build/client/
cd - > /dev/null

log "Frontend build completed ✅"

# Copy service files to build directory
log "Copying service files to build directory..."
for service in "${SERVICES[@]}"; do
    IFS=':' read -r name path <<< "$service"
    service_dir=$(basename "$path")
    
    if [ -d "$path" ]; then
        mkdir -p "./build/services/$service_dir"
        
        # Copy necessary files
        cp -r "$path/src" "./build/services/$service_dir/" 2>/dev/null || true
        cp "$path/package.json" "./build/services/$service_dir/" 2>/dev/null || true
        cp -r "$path/node_modules" "./build/services/$service_dir/" 2>/dev/null || true
    fi
done

# Copy shared utilities
cp -r "./services/shared" "./build/services/"

# Copy configuration files
log "Copying configuration files..."
cp package.json ./build/ 2>/dev/null || true
cp README.md ./build/ 2>/dev/null || true
cp -r scripts ./build/ 2>/dev/null || true

# Create production start script
log "Creating production start script..."
cat > ./build/start-production.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting VWork Platform (Production Mode)"
export NODE_ENV=production
node scripts/unified-start.js
EOF

chmod +x ./build/start-production.sh

# Create Docker support files
log "Creating Docker support files..."

# Main Dockerfile
cat > ./build/Dockerfile << 'EOF'
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY services/shared/package*.json ./services/shared/

# Install dependencies
RUN npm ci --production --silent

# Copy application files
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S vwork -u 1001

# Change ownership
RUN chown -R vwork:nodejs /app
USER vwork

# Expose ports
EXPOSE 8080 3001 3002 3003 3004 3005 3009

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# Start the application
CMD ["node", "scripts/unified-start.js"]
EOF

# Docker Compose for production
cat > ./build/docker-compose.prod.yml << 'EOF'
version: '3.8'

services:
  vwork-app:
    build: .
    ports:
      - "8080:8080"
      - "3001:3001"
      - "3002:3002"
      - "3003:3003"
      - "3004:3004"
      - "3005:3005"
      - "3009:3009"
    environment:
      - NODE_ENV=production
      - PORT=8080
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - vwork-app
    restart: unless-stopped
EOF

# Nginx configuration
mkdir -p ./build/nginx
cat > ./build/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream vwork_backend {
        server vwork-app:8080;
    }

    server {
        listen 80;
        server_name _;

        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name _;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        location / {
            proxy_pass http://vwork_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
EOF

# Generate build info
log "Generating build information..."
cat > ./build/build-info.json << EOF
{
  "buildDate": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "nodeVersion": "$(node -v)",
  "platform": "$(uname -s)",
  "arch": "$(uname -m)",
  "gitCommit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "gitBranch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')",
  "version": "$(grep '"version"' package.json | cut -d'"' -f4)"
}
EOF

# Calculate build size
BUILD_SIZE=$(du -sh ./build | cut -f1)

log "✅ Production build completed successfully!"
echo "=============================================="
log "Build location: ./build"
log "Build size: $BUILD_SIZE"
log "Build info: ./build/build-info.json"
echo ""
log "🚀 To start the production server:"
log "   cd build && ./start-production.sh"
echo ""
log "🐳 To run with Docker:"
log "   cd build && docker-compose -f docker-compose.prod.yml up -d"
echo ""
log "📋 Next steps:"
log "   1. Copy the ./build directory to your production server"
log "   2. Set up your environment variables"
log "   3. Configure SSL certificates in ./build/ssl/"
log "   4. Run the production server"
