#!/usr/bin/env node

const { exec } = require('child_process');
const os = require('os');

// <PERSON>h sách các port cần kill
const PORTS = [3000, 3001, 3002, 8000, 8080, 5000];

// <PERSON>h sách các process names cần kill (cho Windows)
const PROCESS_NAMES = [
  'node.exe',
  'npm.exe', 
  'npx.exe',
  'react-scripts'
];

// Cross-platform command execution
function createCrossPlatformProcess(command, options = {}) {
  const platform = os.platform();
  const isWindows = platform === 'win32';
  
  const execOptions = {
    timeout: options.timeout || 3000,
    env: { 
      ...process.env, 
      PATH: process.env.PATH,
      // Ensure commands can be found on Windows
      ...(isWindows && { PATHEXT: process.env.PATHEXT || '.COM;.EXE;.BAT;.CMD' })
    },
    shell: true,
    windowsHide: true // Hide console window on Windows
  };
  
  return exec(command, execOptions);
}

/**
 * Kill process theo port
 */
function killPort(port) {
  return new Promise((resolve) => {
    const platform = os.platform();
    
    // Set timeout to prevent hanging
    const timeout = setTimeout(() => {
      console.log(`⏰ Timeout for port ${port}`);
      resolve();
    }, 3000);
    
    if (platform === 'win32') {
      // Windows: Sử dụng netstat và taskkill
      exec(`netstat -ano | findstr :${port}`, { timeout: 2000 }, (error, stdout) => {
        clearTimeout(timeout);
        if (!stdout) {
          console.log(`❌ No process found on port ${port}`);
          resolve();
          return;
        }
        
        const lines = stdout.split('\n').filter(line => line.trim());
        const pids = lines.map(line => {
          const parts = line.trim().split(/\s+/);
          return parts[parts.length - 1];
        }).filter(pid => pid && pid !== '0');
        
        if (pids.length > 0) {
          const uniquePids = [...new Set(pids)];
          uniquePids.forEach(pid => {
            exec(`taskkill /F /PID ${pid}`, (killError) => {
              if (!killError) {
                console.log(`✅ Killed process ${pid} on port ${port}`);
              }
            });
          });
        }
        resolve();
      });
    } else {
      // Unix/Linux/macOS: Sử dụng lsof và kill
      exec(`lsof -ti:${port}`, { timeout: 2000 }, (error, stdout) => {
        clearTimeout(timeout);
        if (!stdout) {
          console.log(`❌ No process found on port ${port}`);
          resolve();
          return;
        }
        
        const pids = stdout.trim().split('\n').filter(pid => pid);
        pids.forEach(pid => {
          exec(`kill -9 ${pid}`, (killError) => {
            if (!killError) {
              console.log(`✅ Killed process ${pid} on port ${port}`);
            }
          });
        });
        resolve();
      });
    }
  });
}

/**
 * Kill processes by name (chủ yếu cho Windows)
 */
function killProcessByName(processName) {
  return new Promise((resolve) => {
    const platform = os.platform();
    
    // Set timeout to prevent hanging
    const timeout = setTimeout(() => {
      resolve();
    }, 2000);
    
    if (platform === 'win32') {
      exec(`tasklist /FI "IMAGENAME eq ${processName}" /FO CSV | findstr /V "INFO:"`, { timeout: 1500 }, (error, stdout) => {
        clearTimeout(timeout);
        if (!stdout || stdout.includes('INFO: No tasks')) {
          resolve();
          return;
        }
        
        exec(`taskkill /F /IM ${processName} /T`, (killError, killStdout) => {
          if (!killError) {
            console.log(`✅ Killed all ${processName} processes`);
          }
          resolve();
        });
      });
    } else {
      exec(`pkill -f ${processName}`, { timeout: 1500 }, (error) => {
        clearTimeout(timeout);
        if (!error) {
          console.log(`✅ Killed all ${processName} processes`);
        }
        resolve();
      });
    }
  });
}

/**
 * Main function
 */
async function stopAllProcesses() {
  console.log('🛑 Stopping all development processes...\n');
  
  // Kill processes by port in parallel
  console.log('📡 Killing processes by port:');
  const portPromises = PORTS.map(port => killPort(port));
  await Promise.all(portPromises);
  
  console.log('\n🔄 Killing development processes by name:');
  
  // Kill processes by name
  const processPromises = PROCESS_NAMES.map(processName => killProcessByName(processName));
  await Promise.all(processPromises);
  
  // Small delay to ensure all operations complete
  await new Promise(resolve => setTimeout(resolve, 500));
  
  console.log('\n✅ All processes stopped successfully!');
  console.log('💡 You can now run "npm start" again.\n');
}

// Check for specific arguments
const args = process.argv.slice(2);

if (args.includes('--port') || args.includes('-p')) {
  const portIndex = args.findIndex(arg => arg === '--port' || arg === '-p');
  const port = parseInt(args[portIndex + 1]);
  
  if (port && !isNaN(port)) {
    console.log(`🛑 Stopping process on port ${port}...`);
    killPort(port).then(() => {
      console.log('✅ Done!');
    });
  } else {
    console.log('❌ Invalid port number');
    process.exit(1);
  }
} else {
  // Stop all processes
  stopAllProcesses().catch(console.error);
} 