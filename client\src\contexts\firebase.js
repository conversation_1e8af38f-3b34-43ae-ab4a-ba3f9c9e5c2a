// Simplified Firebase replacement
// This file provides mock Firebase objects for components that still reference Firebase

console.log('🔥 Using simplified authentication (no Firebase client SDK)');

// Mock auth object
const mockAuth = {
  currentUser: null,
  onAuthStateChanged: (callback) => {
    // Return unsubscribe function
    return () => {};
  },
  signInWithEmailAndPassword: () => 
    Promise.reject(new Error('Use backend authentication instead')),
  createUserWithEmailAndPassword: () => 
    Promise.reject(new Error('Use backend authentication instead')),
  signOut: () => 
    Promise.reject(new Error('Use backend authentication instead')),
  updateProfile: () => 
    Promise.reject(new Error('Use backend authentication instead')),
  sendPasswordResetEmail: () => 
    Promise.reject(new Error('Use backend authentication instead')),
  sendEmailVerification: () => 
    Promise.reject(new Error('Use backend authentication instead')),
  applyActionCode: () => 
    Promise.reject(new Error('Use backend authentication instead')),
  reload: () => 
    Promise.reject(new Error('Use backend authentication instead')),
  getIdToken: () => 
    Promise.reject(new Error('Use backend authentication instead')),
};

// Mock Firestore object
const mockDb = {
  collection: () => ({
    doc: () => ({
      get: () => Promise.reject(new Error('Use backend API instead')),
      set: () => Promise.reject(new Error('Use backend API instead')),
      update: () => Promise.reject(new Error('Use backend API instead')),
      delete: () => Promise.reject(new Error('Use backend API instead')),
    }),
    add: () => Promise.reject(new Error('Use backend API instead')),
    where: () => ({
      get: () => Promise.reject(new Error('Use backend API instead')),
    }),
  }),
  doc: () => ({
    get: () => Promise.reject(new Error('Use backend API instead')),
    set: () => Promise.reject(new Error('Use backend API instead')),
    update: () => Promise.reject(new Error('Use backend API instead')),
    delete: () => Promise.reject(new Error('Use backend API instead')),
  }),
};

// Mock Firebase app
const mockApp = {
  name: 'mock-app',
  options: {},
};

// Export mock objects
export const auth = mockAuth;
export const db = mockDb;
export default mockApp;

// Mock Firebase functions for backward compatibility
export const signInWithEmailAndPassword = mockAuth.signInWithEmailAndPassword;
export const createUserWithEmailAndPassword = mockAuth.createUserWithEmailAndPassword;
export const signOut = mockAuth.signOut;
export const onAuthStateChanged = mockAuth.onAuthStateChanged;
export const updateProfile = mockAuth.updateProfile;
export const sendPasswordResetEmail = mockAuth.sendPasswordResetEmail;
export const sendEmailVerification = mockAuth.sendEmailVerification;
export const applyActionCode = mockAuth.applyActionCode;
export const reload = mockAuth.reload;
export const getIdToken = mockAuth.getIdToken;

// Mock Firestore functions
export const doc = mockDb.doc;
export const getDoc = () => Promise.reject(new Error('Use backend API instead'));
export const setDoc = () => Promise.reject(new Error('Use backend API instead'));
export const updateDoc = () => Promise.reject(new Error('Use backend API instead'));
export const deleteDoc = () => Promise.reject(new Error('Use backend API instead'));

// Mock Google Auth Provider
export const GoogleAuthProvider = {
  PROVIDER_ID: 'google.com',
  credential: () => ({}),
};

export const signInWithPopup = () => 
  Promise.reject(new Error('Google login not implemented yet'));

console.log('✅ Mock Firebase objects initialized');
