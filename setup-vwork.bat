@echo off
chcp 65001 >nul
echo.
echo 🚀 VWork Complete System Setup
echo ===============================
echo.

:: Check if Node.js is installed
echo 🔍 Checking system requirements...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed!
    echo 📥 Please download and install Node.js from: https://nodejs.org/
    echo 📋 Required: Node.js 16+ and npm 8+
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed!
    echo 📥 Please install npm with Node.js
    pause
    exit /b 1
)

echo ✅ Node.js and npm are installed
echo.

:: Step 1: Install root dependencies
echo 📦 Step 1: Installing root dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Root npm install failed
    pause
    exit /b 1
)
echo ✅ Root dependencies installed
echo.

:: Step 2: Setup client
echo 🎨 Step 2: Setting up client...
if exist client (
    cd client
    echo 📦 Installing client dependencies...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ Client npm install failed
        cd ..
        pause
        exit /b 1
    )
    echo ✅ Client dependencies installed
    
    echo 🔧 Setting up client environment...
    if not exist .env (
        copy .env.example .env >nul 2>&1
        echo ✅ Client environment file created
    ) else (
        echo ℹ️ Client .env file already exists
    )
    cd ..
) else (
    echo ⚠️ Client directory not found, skipping client setup
)
echo ✅ Client setup completed
echo.

:: Step 3: Setup server
echo 🏰 Step 3: Setting up server...
if exist server (
    cd server
    echo 📦 Installing server dependencies...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ Server npm install failed
        cd ..
        pause
        exit /b 1
    )
    echo ✅ Server dependencies installed
    
    echo 🗄️ Installing databases...
    call npm run setup:db
    if %errorlevel% neq 0 (
        echo ⚠️ Database installation failed, continuing with manual setup...
    )
    echo ✅ Database installation completed
    
    echo 🔧 Setting up server environment...
    if not exist .env (
        call npm run setup
        if %errorlevel% neq 0 (
            echo ❌ Server environment setup failed
            cd ..
            pause
            exit /b 1
        )
    ) else (
        echo ℹ️ Server .env file already exists
    )
    echo ✅ Server environment configured
    
    echo 📊 Setting up database schema...
    call npm run setup:manual
    if %errorlevel% neq 0 (
        echo ⚠️ Database schema setup failed, trying manual setup...
        call node scripts/setupDatabase.js setup
    )
    echo ✅ Database schema created
    
    echo 🌱 Seeding database...
    call npm run db:seed
    if %errorlevel% neq 0 (
        echo ⚠️ Database seeding failed, trying manual seeding...
        call node scripts/setupDatabase.js seed
    )
    echo ✅ Database seeded
    
    cd ..
) else (
    echo ❌ Server directory not found!
    pause
    exit /b 1
)
echo ✅ Server setup completed
echo.

:: Step 4: Create start scripts
echo 🚀 Step 4: Creating start scripts...
echo @echo off > start-vwork.bat
echo chcp 65001 ^>nul >> start-vwork.bat
echo echo 🚀 Starting VWork System... >> start-vwork.bat
echo echo. >> start-vwork.bat
echo echo 📦 Starting server... >> start-vwork.bat
echo start "VWork Server" cmd /k "cd server ^& npm run dev" >> start-vwork.bat
echo timeout /t 5 /nobreak ^>nul >> start-vwork.bat
echo echo 🎨 Starting client... >> start-vwork.bat
echo start "VWork Client" cmd /k "cd client ^& npm start" >> start-vwork.bat
echo echo. >> start-vwork.bat
echo echo ✅ VWork system started! >> start-vwork.bat
echo echo 📱 Frontend: http://localhost:3000 >> start-vwork.bat
echo echo 🔧 Backend: http://localhost:8080 >> start-vwork.bat
echo echo. >> start-vwork.bat
echo pause >> start-vwork.bat

echo @echo off > start-server.bat
echo chcp 65001 ^>nul >> start-server.bat
echo echo 🏰 Starting VWork Server... >> start-server.bat
echo cd server >> start-server.bat
echo npm run dev >> start-server.bat
echo pause >> start-server.bat

echo @echo off > start-client.bat
echo chcp 65001 ^>nul >> start-client.bat
echo echo 🎨 Starting VWork Client... >> start-client.bat
echo cd client >> start-client.bat
echo npm start >> start-client.bat
echo pause >> start-client.bat

echo ✅ Start scripts created
echo.

:: Step 5: Create environment file
echo 🔧 Step 5: Creating root environment file...
if not exist .env (
    echo # VWork Root Environment > .env
    echo # Generated on %date% %time% >> .env
    echo. >> .env
    echo # Client Settings >> .env
    echo REACT_APP_API_URL=http://localhost:8080 >> .env
    echo REACT_APP_SOCKET_URL=http://localhost:8080 >> .env
    echo. >> .env
    echo # Server Settings >> .env
    echo NODE_ENV=development >> .env
    echo PORT=8080 >> .env
    echo CORS_ORIGIN=http://localhost:3000 >> .env
    echo. >> .env
    echo # Database Settings >> .env
    echo DB_HOST=localhost >> .env
    echo DB_PORT=5432 >> .env
    echo DB_NAME=vwork_db >> .env
    echo DB_USER=vwork_user >> .env
    echo DB_PASSWORD=vwork_password >> .env
    echo MONGODB_URI=mongodb://localhost:27017/vwork_social >> .env
    echo REDIS_HOST=localhost >> .env
    echo REDIS_PORT=6379 >> .env
    echo. >> .env
    echo # JWT Settings >> .env
    echo JWT_SECRET=vwork_jwt_secret_%random% >> .env
    echo JWT_EXPIRE=7d >> .env
    echo. >> .env
    echo # Firebase Settings (Configure later) >> .env
    echo FIREBASE_PROJECT_ID=your-project-id >> .env
    echo FIREBASE_CLIENT_EMAIL=your-service-account-email >> .env
    echo FIREBASE_PRIVATE_KEY="your-private-key" >> .env
    echo. >> .env
    echo # Email Settings (Configure later) >> .env
    echo SMTP_HOST=smtp.gmail.com >> .env
    echo SMTP_PORT=587 >> .env
    echo SMTP_EMAIL=<EMAIL> >> .env
    echo SMTP_PASSWORD=your-app-password >> .env
    echo ✅ Root environment file created
) else (
    echo ℹ️ Root .env file already exists
)
echo.

:: Step 6: Test installation
echo 🧪 Step 6: Testing installation...
echo 📊 Testing database connections...
cd server
call node -e "const { testConnection } = require('./config/database'); const { testMongoConnection } = require('./config/mongodb'); Promise.all([testConnection(), testMongoConnection()]).then(([pg, mongo]) => { console.log('✅ PostgreSQL:', pg.postgres ? 'Connected' : 'Failed'); console.log('✅ MongoDB:', mongo ? 'Connected' : 'Failed'); process.exit(pg.postgres && mongo ? 0 : 1); }).catch(err => { console.log('❌ Connection test failed:', err.message); process.exit(1); });"
if %errorlevel% neq 0 (
    echo ⚠️ Database connection test failed, but continuing...
) else (
    echo ✅ Database connections successful
)
cd ..
echo.

:: Step 7: Create documentation
echo 📚 Step 7: Creating documentation...
echo # VWork System Documentation > VWORK_GUIDE.md
echo. >> VWORK_GUIDE.md
echo ## 🚀 Quick Start >> VWORK_GUIDE.md
echo. >> VWORK_GUIDE.md
echo ### Start the entire system: >> VWORK_GUIDE.md
echo ```bash >> VWORK_GUIDE.md
echo start-vwork.bat >> VWORK_GUIDE.md
echo ``` >> VWORK_GUIDE.md
echo. >> VWORK_GUIDE.md
echo ### Start server only: >> VWORK_GUIDE.md
echo ```bash >> VWORK_GUIDE.md
echo start-server.bat >> VWORK_GUIDE.md
echo ``` >> VWORK_GUIDE.md
echo. >> VWORK_GUIDE.md
echo ### Start client only: >> VWORK_GUIDE.md
echo ```bash >> VWORK_GUIDE.md
echo start-client.bat >> VWORK_GUIDE.md
echo ``` >> VWORK_GUIDE.md
echo. >> VWORK_GUIDE.md
echo ## 📱 Access URLs >> VWORK_GUIDE.md
echo - Frontend: http://localhost:3000 >> VWORK_GUIDE.md
echo - Backend: http://localhost:8080 >> VWORK_GUIDE.md
echo - API Health: http://localhost:8080/health >> VWORK_GUIDE.md
echo. >> VWORK_GUIDE.md
echo ## 🗄️ Database Status >> VWORK_GUIDE.md
echo - PostgreSQL: localhost:5432 >> VWORK_GUIDE.md
echo - MongoDB: localhost:27017 >> VWORK_GUIDE.md
echo - Redis: localhost:6379 >> VWORK_GUIDE.md
echo. >> VWORK_GUIDE.md
echo ## 🔧 Troubleshooting >> VWORK_GUIDE.md
echo - Reset database: `cd server && npm run db:reset` >> VWORK_GUIDE.md
echo - Reinstall: Run this script again >> VWORK_GUIDE.md
echo - Check logs: `cd server && npm run dev` >> VWORK_GUIDE.md
echo ✅ Documentation created
echo.

:: Final success message
echo 🎉 VWork System Setup Completed Successfully!
echo.
echo 📋 Next steps:
echo 1. Start the system: start-vwork.bat
echo 2. Or start individually:
echo    - Server: start-server.bat
echo    - Client: start-client.bat
echo.
echo 📱 Access URLs:
echo - Frontend: http://localhost:3000
echo - Backend: http://localhost:8080
echo - API Health: http://localhost:8080/health
echo.
echo 📚 Documentation: VWORK_GUIDE.md
echo.
echo 🔧 If you encounter issues:
echo - Check the logs in server/client directories
echo - Reset database: cd server && npm run db:reset
echo - Reinstall: Run this script again
echo.
pause 