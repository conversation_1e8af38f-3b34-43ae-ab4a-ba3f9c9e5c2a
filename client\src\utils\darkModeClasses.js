/**
 * Dark Mode Class Utilities for VWork
 * Provides consistent dark mode class mappings for all components
 */

export const darkModeClasses = {
  // Background Classes
  backgrounds: {
    'bg-white': 'bg-white dark:bg-gray-900',
    'bg-gray-50': 'bg-gray-50 dark:bg-gray-800',
    'bg-gray-100': 'bg-gray-100 dark:bg-gray-700',
    'bg-gray-200': 'bg-gray-200 dark:bg-gray-600',
    'bg-gray-300': 'bg-gray-300 dark:bg-gray-500',
  },

  // Text Classes
  text: {
    'text-gray-900': 'text-gray-900 dark:text-gray-100',
    'text-gray-800': 'text-gray-800 dark:text-gray-200',
    'text-gray-700': 'text-gray-700 dark:text-gray-300',
    'text-gray-600': 'text-gray-600 dark:text-gray-400',
    'text-gray-500': 'text-gray-500 dark:text-gray-500',
  },

  // Border Classes
  borders: {
    'border-gray-200': 'border-gray-200 dark:border-gray-700',
    'border-gray-300': 'border-gray-300 dark:border-gray-600',
    'border-gray-400': 'border-gray-400 dark:border-gray-500',
  },

  // Card Classes
  cards: {
    'card-base': 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
    'card-hover': 'hover:bg-gray-50 dark:hover:bg-gray-700',
    'card-interactive': 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600',
  },

  // Button Classes
  buttons: {
    'btn-primary': 'bg-blue-600 hover:bg-blue-700 text-white',
    'btn-secondary': 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-600',
    'btn-outline': 'border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700',
  },

  // Form Classes
  forms: {
    'input-base': 'bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400',
    'input-focus': 'focus:ring-blue-500 focus:border-blue-500',
  },

  // Navigation Classes
  navigation: {
    'nav-link': 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100',
    'nav-active': 'text-blue-600 dark:text-blue-400',
  },

  // Modal Classes
  modals: {
    'modal-overlay': 'bg-black bg-opacity-50 dark:bg-opacity-75',
    'modal-content': 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
  },

  // Dropdown Classes
  dropdowns: {
    'dropdown-menu': 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg',
    'dropdown-item': 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700',
  },

  // Table Classes
  tables: {
    'table-header': 'bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100',
    'table-row': 'bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700',
    'table-cell': 'text-gray-900 dark:text-gray-100',
  },

  // Alert Classes
  alerts: {
    'alert-info': 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200',
    'alert-success': 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-200',
    'alert-warning': 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200',
    'alert-error': 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200',
  },

  // Badge Classes
  badges: {
    'badge-primary': 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200',
    'badge-success': 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200',
    'badge-warning': 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200',
    'badge-error': 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200',
  },
};

/**
 * Get dark mode compatible class string
 * @param {string} baseClass - The base class to convert
 * @param {string} category - The category of the class (backgrounds, text, borders, etc.)
 * @returns {string} Dark mode compatible class string
 */
export const getDarkModeClass = (baseClass, category = 'backgrounds') => {
  const categoryClasses = darkModeClasses[category];
  return categoryClasses[baseClass] || baseClass;
};

/**
 * Convert multiple classes to dark mode compatible classes
 * @param {string} classString - Space-separated class string
 * @returns {string} Dark mode compatible class string
 */
export const convertToDarkMode = (classString) => {
  const classes = classString.split(' ');
  const convertedClasses = classes.map(cls => {
    // Check each category for the class
    for (const [category, categoryClasses] of Object.entries(darkModeClasses)) {
      if (categoryClasses[cls]) {
        return categoryClasses[cls];
      }
    }
    return cls;
  });
  
  return convertedClasses.join(' ');
};

/**
 * Common dark mode class combinations
 */
export const commonDarkModeClasses = {
  // Page containers
  pageContainer: 'min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300',
  contentContainer: 'bg-gray-50 dark:bg-gray-800 transition-colors duration-300',
  
  // Headers
  pageHeader: 'text-gray-900 dark:text-gray-100 transition-colors duration-300',
  sectionHeader: 'text-gray-800 dark:text-gray-200 transition-colors duration-300',
  
  // Cards
  basicCard: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm transition-colors duration-300',
  interactiveCard: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-300',
  
  // Forms
  formInput: 'bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-300',
  formLabel: 'text-gray-700 dark:text-gray-300 transition-colors duration-300',
  
  // Buttons
  primaryButton: 'bg-blue-600 hover:bg-blue-700 text-white transition-colors duration-300',
  secondaryButton: 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 transition-colors duration-300',
  
  // Navigation
  navLink: 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-300',
  activeNavLink: 'text-blue-600 dark:text-blue-400 transition-colors duration-300',
  
  // Text
  primaryText: 'text-gray-900 dark:text-gray-100 transition-colors duration-300',
  secondaryText: 'text-gray-600 dark:text-gray-400 transition-colors duration-300',
  mutedText: 'text-gray-500 dark:text-gray-500 transition-colors duration-300',
  
  // Dividers
  divider: 'border-gray-200 dark:border-gray-700 transition-colors duration-300',
  
  // Shadows
  cardShadow: 'shadow-sm hover:shadow-md dark:shadow-gray-900/25 transition-shadow duration-300',
  modalShadow: 'shadow-xl dark:shadow-gray-900/50',
};

/**
 * Apply dark mode classes to a component's className prop
 * @param {string} className - Original className string
 * @returns {string} Dark mode enhanced className string
 */
export const withDarkMode = (className) => {
  if (!className) return '';
  
  // Add transition classes if not present
  const hasTransition = className.includes('transition');
  const transitionClasses = hasTransition ? '' : ' transition-colors duration-300';
  
  return convertToDarkMode(className) + transitionClasses;
};

export default {
  darkModeClasses,
  getDarkModeClass,
  convertToDarkMode,
  commonDarkModeClasses,
  withDarkMode,
};
