/**
 * Error logging utility for authentication errors
 */

// Firebase error codes that are considered user errors (not system errors)
const USER_ERROR_CODES = [
  'auth/invalid-credential',
  'auth/invalid-login-credentials',
  'auth/user-not-found',
  'auth/wrong-password',
  'auth/email-already-in-use',
  'auth/weak-password',
  'auth/invalid-email',
  'auth/popup-closed-by-user',
  'auth/cancelled-popup-request'
];

// Firebase error codes that indicate system/network issues
const SYSTEM_ERROR_CODES = [
  'auth/network-request-failed',
  'auth/internal-error',
  'auth/timeout',
  'auth/too-many-requests',
  'auth/popup-blocked',
  'auth/user-disabled',
  'auth/operation-not-allowed'
];

/**
 * Enhanced error logging for authentication failures
 * @param {Error} error - The Firebase auth error
 * @param {Object} context - Additional context information
 */
export const logAuthError = (error, context = {}) => {
  const errorData = {
    // Basic error info
    code: error.code,
    message: error.message,
    timestamp: new Date().toISOString(),
    
    // Context information
    ...context,
    
    // Browser/environment info
    userAgent: navigator.userAgent,
    language: navigator.language,
    platform: navigator.platform,
    
    // Screen info (for mobile debugging)
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    
    // Firebase specific info
    isUserError: USER_ERROR_CODES.includes(error.code),
    isSystemError: SYSTEM_ERROR_CODES.includes(error.code),
    
    // Network status if available
    onlineStatus: navigator.onLine,
    
    // Performance timing (if available)
    ...(window.performance && {
      performanceNow: window.performance.now(),
      navigationTiming: {
        loadEventEnd: window.performance.timing.loadEventEnd,
        navigationStart: window.performance.timing.navigationStart
      }
    })
  };

  // Log to console with appropriate level
  if (errorData.isSystemError) {
    console.error('🔥 Firebase System Error:', errorData);
  } else if (errorData.isUserError) {
    console.warn('⚠️ Firebase User Error:', errorData);
  } else {
    console.error('❌ Firebase Unknown Error:', errorData);
  }

  // In production, you might want to send this to an error reporting service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to error reporting service
    // errorReportingService.report(errorData);
  }

  return errorData;
};

/**
 * Get user-friendly error suggestions based on error code
 * @param {string} errorCode - Firebase error code
 * @returns {Array} Array of suggestion strings
 */
export const getErrorSuggestions = (errorCode) => {
  switch (errorCode) {
    case 'auth/invalid-credential':
    case 'auth/invalid-login-credentials':
    case 'auth/user-not-found':
    case 'auth/wrong-password':
      return [
        'Kiểm tra lại email và mật khẩu',
        'Đảm bảo không có khoảng trắng thừa',
        'Thử đặt lại mật khẩu nếu quên',
        'Kiểm tra caps lock có đang bật không'
      ];
      
    case 'auth/email-already-in-use':
      return [
        'Email này đã được đăng ký',
        'Thử đăng nhập thay vì đăng ký',
        'Sử dụng chức năng "Quên mật khẩu" nếu cần'
      ];
      
    case 'auth/weak-password':
      return [
        'Sử dụng ít nhất 8 ký tự',
        'Kết hợp chữ hoa, chữ thường và số',
        'Thêm ký tự đặc biệt để tăng độ bảo mật'
      ];
      
    case 'auth/too-many-requests':
      return [
        'Đợi 1-2 phút trước khi thử lại',
        'Firebase đã tạm khóa do quá nhiều lần thử',
        'Thử sử dụng mạng khác nếu cần thiết'
      ];
      
    case 'auth/network-request-failed':
      return [
        'Kiểm tra kết nối internet',
        'Thử chuyển sang wifi/4G khác',
        'Tắt VPN nếu đang sử dụng'
      ];
      
    case 'auth/popup-closed-by-user':
    case 'auth/cancelled-popup-request':
      return [
        'Đăng nhập bị hủy',
        'Thử lại và không đóng popup',
        'Kiểm tra popup có bị chặn không'
      ];
      
    case 'auth/popup-blocked':
      return [
        'Popup bị chặn bởi trình duyệt',
        'Cho phép popup cho trang này',
        'Thử trên trình duyệt khác'
      ];
      
    default:
      return [
        'Thử lại sau ít phút',
        'Kiểm tra kết nối internet',
        'Liên hệ hỗ trợ nếu vấn đề tiếp tục'
      ];
  }
};

/**
 * Check if error is likely due to user input vs system issue
 * @param {string} errorCode - Firebase error code
 * @returns {Object} Classification of error type
 */
export const classifyError = (errorCode) => {
  return {
    isUserError: USER_ERROR_CODES.includes(errorCode),
    isSystemError: SYSTEM_ERROR_CODES.includes(errorCode),
    isRetryable: !USER_ERROR_CODES.includes(errorCode),
    severity: SYSTEM_ERROR_CODES.includes(errorCode) ? 'high' : 'low'
  };
};

export default {
  logAuthError,
  getErrorSuggestions,
  classifyError,
  USER_ERROR_CODES,
  SYSTEM_ERROR_CODES
};
