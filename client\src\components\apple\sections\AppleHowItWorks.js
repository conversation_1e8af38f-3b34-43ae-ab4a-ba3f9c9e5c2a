import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { SplitText } from 'gsap/SplitText';
import { Physics2DPlugin } from 'gsap/Physics2DPlugin';
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin';
import { CustomEase } from 'gsap/CustomEase';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import { useLanguage } from '../../../contexts/LanguageContext';
import {
  MagnifyingGlassIcon,
  ChatBubbleLeftRightIcon,
  CreditCardIcon,
  StarIcon,
  ArrowRightIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, SplitText, Physics2DPlugin, MorphSVGPlugin, CustomEase, MotionPathPlugin);

// Premium easing curves
const premiumEases = {
  elastic: CustomEase.create("elastic", "M0,0 C0.25,0 0.4,1.4 0.7,1 C0.85,0.8 1,1 1,1"),
  bounce: CustomEase.create("bounce", "M0,0 C0.14,0 0.242,0.438 0.272,0.561 0.313,0.728 0.354,0.963 0.362,1 0.37,0.985 0.414,0.928 0.455,0.879 0.504,0.822 0.565,0.729 0.621,0.653 0.681,0.573 0.737,0.5 0.785,0.5 0.856,0.5 0.923,0.717 1,1"),
  liquid: CustomEase.create("liquid", "M0,0 C0.29,0.01 0.49,1.53 0.59,1.23 C0.69,0.93 1,1 1,1"),
  magnetic: CustomEase.create("magnetic", "M0,0 C0.5,0 0.5,1 1,1"),
  wave: CustomEase.create("wave", "M0,0 C0.2,0.8 0.8,0.2 1,1")
};

const AppleHowItWorks = () => {
  const { t } = useLanguage();
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const stepsRef = useRef(null);
  const connectorsRef = useRef(null);
  const particleSystemRef = useRef(null);
  const morphingShapesRef = useRef([]);

  // Advanced Particle System for Steps
  const createAdvancedParticleSystem = () => {
    if (!particleSystemRef.current) return;

    const container = particleSystemRef.current;
    const particles = [];

    // Create 60 floating particles
    for (let i = 0; i < 60; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-1 h-1 bg-blue-400 rounded-full opacity-30';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.top = Math.random() * 100 + '%';
      container.appendChild(particle);
      particles.push(particle);

      // Physics-based movement
      gsap.to(particle, {
        physics2D: {
          velocity: Math.random() * 100 + 50,
          angle: Math.random() * 360,
          gravity: 30,
          friction: 0.99
        },
        duration: 8,
        repeat: -1,
        yoyo: true,
        ease: "none"
      });

      // Floating animation
      gsap.to(particle, {
        y: "random(-50, 50)",
        x: "random(-30, 30)",
        scale: "random(0.5, 1.5)",
        opacity: "random(0.2, 0.8)",
        duration: "random(3, 6)",
        repeat: -1,
        yoyo: true,
        ease: premiumEases.wave
      });
    }
  };

  // Morphing Background Shapes
  const createMorphingShapes = () => {
    morphingShapesRef.current.forEach((shape, index) => {
      if (!shape) return;

      const morphTimeline = gsap.timeline({ repeat: -1, yoyo: true });
      
      morphTimeline
        .to(shape, {
          borderRadius: "60% 40% 30% 70% / 60% 30% 70% 40%",
          scale: 1.1,
          rotation: 45,
          duration: 4,
          ease: premiumEases.liquid
        })
        .to(shape, {
          borderRadius: "40% 60% 70% 30% / 40% 70% 30% 60%",
          scale: 0.9,
          rotation: -45,
          duration: 3,
          ease: premiumEases.elastic
        })
        .to(shape, {
          borderRadius: "70% 30% 40% 60% / 30% 60% 40% 70%",
          scale: 1.05,
          rotation: 90,
          duration: 3.5,
          ease: premiumEases.wave
        });

      // Add delay based on index
      morphTimeline.delay(index * 0.5);
    });
  };

  // Advanced Magnetic Hover Effect for Steps
  const createMagneticStepHover = (stepElement, index) => {
    if (!stepElement) return;

    const icon = stepElement.querySelector('.step-icon');
    const content = stepElement.querySelector('.step-content');
    const background = stepElement.querySelector('.step-background');

    let isHovering = false;

    stepElement.addEventListener('mouseenter', () => {
      isHovering = true;

      // Magnetic hover timeline
      const hoverTL = gsap.timeline();

      hoverTL
        .to(stepElement, {
          scale: 1.05,
          y: -10,
          rotationY: 5,
          rotationX: 5,
          boxShadow: "0 25px 50px rgba(0,0,0,0.15)",
          duration: 0.6,
          ease: premiumEases.magnetic
        })
        .to(icon, {
          scale: 1.3,
          rotation: 360,
          duration: 0.8,
          ease: premiumEases.bounce
        }, 0)
        .to(background, {
          scale: 1.1,
          opacity: 0.8,
          duration: 0.5,
          ease: premiumEases.elastic
        }, 0.1);

      // Create hover particles
      createHoverParticles(stepElement, index);
    });

    stepElement.addEventListener('mouseleave', () => {
      isHovering = false;

      gsap.to(stepElement, {
        scale: 1,
        y: 0,
        rotationY: 0,
        rotationX: 0,
        boxShadow: "0 10px 25px rgba(0,0,0,0.05)",
        duration: 0.8,
        ease: premiumEases.elastic
      });

      gsap.to(icon, {
        scale: 1,
        rotation: 0,
        duration: 0.6,
        ease: premiumEases.bounce
      });

      gsap.to(background, {
        scale: 1,
        opacity: 1,
        duration: 0.4,
        ease: premiumEases.wave
      });
    });

    // Mouse tracking for magnetic effect
    stepElement.addEventListener('mousemove', (e) => {
      if (!isHovering) return;

      const rect = stepElement.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      const mouseX = e.clientX - centerX;
      const mouseY = e.clientY - centerY;

      gsap.to(stepElement, {
        x: mouseX * 0.1,
        y: mouseY * 0.1,
        duration: 0.3,
        ease: "power2.out"
      });

      gsap.to(icon, {
        x: mouseX * 0.15,
        y: mouseY * 0.15,
        duration: 0.2,
        ease: "power2.out"
      });
    });
  };

  // Create Hover Particles
  const createHoverParticles = (element, index) => {
    const colors = ['#3B82F6', '#8B5CF6', '#06B6D4', '#10B981'];
    const color = colors[index % colors.length];

    for (let i = 0; i < 8; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute pointer-events-none w-2 h-2 rounded-full';
      particle.style.background = color;
      particle.style.opacity = '0';
      element.appendChild(particle);

      const angle = (i / 8) * Math.PI * 2;
      const distance = 50 + Math.random() * 30;

      gsap.set(particle, {
        x: 0,
        y: 0,
        scale: 0
      });

      gsap.to(particle, {
        x: Math.cos(angle) * distance,
        y: Math.sin(angle) * distance,
        scale: 1,
        opacity: 0.8,
        duration: 0.6,
        ease: premiumEases.elastic
      });

      gsap.to(particle, {
        opacity: 0,
        scale: 0,
        duration: 0.4,
        delay: 0.6,
        ease: "power2.in",
        onComplete: () => {
          if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
          }
        }
      });
    }
  };

  // Advanced Title Animation with SplitText
  const createAdvancedTitleAnimation = () => {
    if (!titleRef.current) return;

    const titleElement = titleRef.current.querySelector('h2');
    const subtitleElement = titleRef.current.querySelector('p');

    if (titleElement) {
      const titleSplit = new SplitText(titleElement, { type: "chars,words" });

      gsap.fromTo(titleSplit.chars, {
        opacity: 0,
        y: 100,
        rotationX: -90,
        transformOrigin: "center bottom"
      }, {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration: 1.2,
        stagger: 0.05,
        ease: premiumEases.bounce,
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      });
    }

    if (subtitleElement) {
      const subtitleSplit = new SplitText(subtitleElement, { type: "words" });

      gsap.fromTo(subtitleSplit.words, {
        opacity: 0,
        y: 30,
        scale: 0.8
      }, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        stagger: 0.1,
        ease: premiumEases.elastic,
        delay: 0.5,
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      });
    }
  };

  // Advanced Steps Animation
  const createAdvancedStepsAnimation = () => {
    if (!stepsRef.current) return;

    const stepElements = Array.from(stepsRef.current.children);

    stepElements.forEach((step, index) => {
      // Create magnetic hover effect
      createMagneticStepHover(step, index);

      // Initial animation
      gsap.fromTo(step, {
        opacity: 0,
        y: 80,
        scale: 0.8,
        rotationY: 45
      }, {
        opacity: 1,
        y: 0,
        scale: 1,
        rotationY: 0,
        duration: 1.2,
        delay: index * 0.2,
        ease: premiumEases.elastic,
        scrollTrigger: {
          trigger: step,
          start: 'top 90%',
          toggleActions: 'play none none reverse'
        }
      });

      // Animate step content with physics
      const stepIcon = step.querySelector('.step-icon');
      const stepContent = step.querySelector('.step-content');

      if (stepIcon) {
        gsap.fromTo(stepIcon, {
          scale: 0,
          rotation: -180
        }, {
          scale: 1,
          rotation: 0,
          duration: 1.0,
          delay: index * 0.2 + 0.3,
          ease: premiumEases.bounce,
          scrollTrigger: {
            trigger: step,
            start: 'top 90%',
            toggleActions: 'play none none reverse'
          }
        });
      }

      if (stepContent) {
        const contentChildren = Array.from(stepContent.children);
        gsap.fromTo(contentChildren, {
          opacity: 0,
          x: -30
        }, {
          opacity: 1,
          x: 0,
          duration: 0.8,
          stagger: 0.1,
          delay: index * 0.2 + 0.6,
          ease: premiumEases.wave,
          scrollTrigger: {
            trigger: step,
            start: 'top 90%',
            toggleActions: 'play none none reverse'
          }
        });
      }
    });
  };

  // Advanced Connector Animation
  const createAdvancedConnectorAnimation = () => {
    if (!connectorsRef.current) return;

    const connectors = Array.from(connectorsRef.current.children);

    connectors.forEach((connector, index) => {
      const line = connector.querySelector('.connector-line');
      const arrow = connector.querySelector('.connector-arrow');

      if (line) {
        gsap.fromTo(line, {
          scaleX: 0,
          opacity: 0
        }, {
          scaleX: 1,
          opacity: 1,
          duration: 1.0,
          delay: index * 0.3 + 1.0,
          ease: premiumEases.elastic,
          scrollTrigger: {
            trigger: connectorsRef.current,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        });
      }

      if (arrow) {
        gsap.fromTo(arrow, {
          x: -20,
          opacity: 0,
          scale: 0
        }, {
          x: 0,
          opacity: 1,
          scale: 1,
          duration: 0.6,
          delay: index * 0.3 + 1.3,
          ease: premiumEases.bounce,
          scrollTrigger: {
            trigger: connectorsRef.current,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          }
        });
      }
    });
  };

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Initialize all advanced animations
      createAdvancedParticleSystem();
      createMorphingShapes();
      createAdvancedTitleAnimation();
      createAdvancedStepsAnimation();
      createAdvancedConnectorAnimation();
    }, sectionRef);

    return () => ctx.revert();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const steps = [
    {
      id: 1,
      icon: MagnifyingGlassIcon,
      title: t('postYourProject'),
      description: t('postProjectDesc'),
      details: [
        t('describeProjectReq'),
        t('setBudgetTimeline'),
        t('choosePreferredSkills'),
      ],
      color: 'blue',
    },
    {
      id: 2,
      icon: ChatBubbleLeftRightIcon,
      title: t('reviewProposals'),
      description: t('reviewProposalsDesc'),
      details: [
        t('receiveProposalsFromQualified'),
        t('reviewPortfoliosPastWork'),
        t('interviewTopCandidates'),
      ],
      color: 'green',
    },
    {
      id: 3,
      icon: CreditCardIcon,
      title: t('workPaySafely'),
      description: t('workPaySafelyDesc'),
      details: [
        t('useEscrowProtection'),
        t('trackProgressMilestones'),
        t('payOnlyWhenSatisfied'),
      ],
      color: 'purple',
    },
    {
      id: 4,
      icon: StarIcon,
      title: t('leaveReview'),
      description: t('leaveReviewDesc'),
      details: [
        t('rateFreelancerWork'),
        t('provideFeedback'),
        t('buildLongTermRelationships'),
      ],
      color: 'orange',
    },
  ];

  const getColorClasses = (color) => {
    const colorMap = {
      blue: {
        bg: 'bg-blue-50 dark:bg-blue-900/20',
        text: 'text-blue-600 dark:text-blue-400',
        border: 'border-blue-200 dark:border-blue-700',
        hover: 'hover:bg-blue-100 dark:hover:bg-blue-900/30',
      },
      green: {
        bg: 'bg-green-50 dark:bg-green-900/20',
        text: 'text-green-600 dark:text-green-400',
        border: 'border-green-200 dark:border-green-700',
        hover: 'hover:bg-green-100 dark:hover:bg-green-900/30',
      },
      purple: {
        bg: 'bg-purple-50 dark:bg-purple-900/20',
        text: 'text-purple-600 dark:text-purple-400',
        border: 'border-purple-200 dark:border-purple-700',
        hover: 'hover:bg-purple-100 dark:hover:bg-purple-900/30',
      },
      orange: {
        bg: 'bg-orange-50 dark:bg-orange-900/20',
        text: 'text-orange-600 dark:text-orange-400',
        border: 'border-orange-200 dark:border-orange-700',
        hover: 'hover:bg-orange-100 dark:hover:bg-orange-900/30',
      },
    };
    return colorMap[color] || colorMap.blue;
  };

  return (
    <>
      {/* Enhanced CSS */}
      <style>{`
        @keyframes liquid-morph {
          0%, 100% {
            border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
          }
          25% {
            border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
          }
          50% {
            border-radius: 70% 30% 40% 60% / 30% 60% 40% 70%;
          }
          75% {
            border-radius: 40% 70% 60% 30% / 70% 40% 60% 30%;
          }
        }
        
        .liquid-morph {
          animation: liquid-morph 8s ease-in-out infinite;
        }
        
        .magnetic-hover {
          transform-style: preserve-3d;
          perspective: 1000px;
        }
        
        .floating-particles {
          will-change: transform;
        }
        
        .step-card {
          transform-style: preserve-3d;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .step-card:hover {
          transform: perspective(1000px) rotateX(5deg) rotateY(5deg) translateZ(50px);
        }
      `}</style>

      <section
        ref={sectionRef}
        className='relative py-20 bg-gray-50 dark:bg-gray-800 transition-colors duration-300 overflow-hidden'
      >
        {/* Advanced Multi-layer Background */}
        <div className='absolute inset-0'>
          <div 
            ref={el => morphingShapesRef.current[0] = el}
            className='absolute top-10 left-10 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-400/10 liquid-morph'
          />
          <div 
            ref={el => morphingShapesRef.current[1] = el}
            className='absolute top-20 right-20 w-24 h-24 bg-gradient-to-br from-green-400/10 to-cyan-400/10 liquid-morph'
          />
          <div 
            ref={el => morphingShapesRef.current[2] = el}
            className='absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-br from-purple-400/10 to-pink-400/10 liquid-morph'
          />
          <div 
            ref={el => morphingShapesRef.current[3] = el}
            className='absolute bottom-10 right-10 w-28 h-28 bg-gradient-to-br from-orange-400/10 to-red-400/10 liquid-morph'
          />
        </div>

        {/* Advanced Particle Canvas */}
        <div 
          ref={particleSystemRef}
          className='absolute inset-0 pointer-events-none overflow-hidden floating-particles'
        />

        <div className='relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          {/* Enhanced Section Header */}
          <div ref={titleRef} className='text-center mb-16'>
            <h2 className='text-4xl sm:text-5xl lg:text-6xl font-black text-gray-900 dark:text-gray-100 mb-8 transition-colors duration-300'>
              {t('howItWorks')}
            </h2>
            <p className='text-xl sm:text-2xl lg:text-3xl text-gray-600 dark:text-gray-400 max-w-4xl mx-auto leading-relaxed transition-colors duration-300'>
              {t('simpleStepsToSuccess')}
            </p>
          </div>

          {/* Enhanced Steps */}
          <div className='relative'>
            {/* Advanced Connectors */}
            <div ref={connectorsRef} className='hidden lg:block'>
              {steps.slice(0, -1).map((_, index) => (
                <div
                  key={index}
                  className='absolute top-1/2 transform -translate-y-1/2 z-10'
                  style={{
                    left: `${25 + index * 25}%`,
                  }}
                >
                  <div className='connector-line w-24 h-1 bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 rounded-full opacity-60' />
                  <ArrowRightIcon className='connector-arrow absolute -right-2 -top-2 h-6 w-6 text-blue-500 drop-shadow-lg' />
                </div>
              ))}
            </div>

            {/* Enhanced Steps Grid */}
            <div
              ref={stepsRef}
              className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12'
            >
              {steps.map((step, index) => {
                const colors = getColorClasses(step.color);
                
                return (
                  <div
                    key={step.id}
                    className='group relative step-card magnetic-hover'
                  >
                    <div className='step-background absolute inset-0 bg-white dark:bg-gray-900 rounded-3xl border border-gray-100 dark:border-gray-700 shadow-lg transition-all duration-500' />
                    
                    <div className='relative bg-white dark:bg-gray-900 rounded-3xl p-8 border border-gray-100 dark:border-gray-700 transition-all duration-500 hover:shadow-2xl hover:border-gray-200 dark:hover:border-gray-600 transform-gpu'>
                      {/* Enhanced Step Number */}
                      <div className='absolute -top-6 left-8'>
                        <div className={`w-12 h-12 rounded-2xl ${colors.bg} ${colors.border} border-2 flex items-center justify-center shadow-lg backdrop-blur-sm`}>
                          <span className={`text-lg font-black ${colors.text}`}>
                            {step.id}
                          </span>
                        </div>
                      </div>

                      {/* Enhanced Icon */}
                      <div className='mb-8 mt-6'>
                        <div
                          className={`step-icon inline-flex items-center justify-center w-20 h-20 rounded-3xl transition-all duration-500 ${colors.bg} ${colors.hover} shadow-lg group-hover:shadow-xl`}
                        >
                          <step.icon className={`h-10 w-10 ${colors.text}`} />
                        </div>
                      </div>

                      {/* Enhanced Content */}
                      <div className='step-content'>
                        <h3 className='text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 transition-colors duration-300'>
                          {step.title}
                        </h3>
                        <p className='text-gray-600 dark:text-gray-400 mb-8 leading-relaxed text-lg transition-colors duration-300'>
                          {step.description}
                        </p>

                        {/* Enhanced Details */}
                        <ul className='space-y-3'>
                          {step.details.map((detail, detailIndex) => (
                            <li
                              key={detailIndex}
                              className='flex items-start text-gray-600 dark:text-gray-400 transition-colors duration-300'
                            >
                              <CheckCircleIcon className={`h-5 w-5 mt-0.5 mr-3 flex-shrink-0 ${colors.text}`} />
                              <span className='font-medium'>{detail}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default AppleHowItWorks;
