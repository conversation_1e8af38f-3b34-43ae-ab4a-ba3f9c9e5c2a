/**
 * User Service Validation Schemas
 */

const schemas = {
  updateProfile: {
    name: { type: 'string', required: false, minLength: 2, maxLength: 100 },
    email: { type: 'email', required: false },
    phone: { type: 'string', required: false },
    location: { type: 'string', required: false },
    bio: { type: 'string', required: false, maxLength: 500 },
    skills: { type: 'array', required: false },
    hourlyRate: { type: 'number', required: false, min: 0 },
    availability: { type: 'string', required: false, enum: ['available', 'busy', 'unavailable'] }
  },

  createProfile: {
    name: { type: 'string', required: true, minLength: 2, maxLength: 100 },
    email: { type: 'email', required: true },
    userType: { type: 'string', required: true, enum: ['freelancer', 'client'] }
  }
};

module.exports = { schemas };
