const fs = require('fs');
const path = require('path');

// Function to read all .js and .jsx files recursively
function getAllJSFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.includes('node_modules') && !file.includes('.git')) {
      getAllJSFiles(filePath, fileList);
    } else if (file.endsWith('.js') || file.endsWith('.jsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to fix unused React import
function fixUnusedReactImport(content) {
  // Remove "React, " from import statement if React is not used in JSX or other ways
  if (content.includes("import React,") && 
      !content.includes("React.") && 
      !content.includes("React ") &&
      !content.match(/<[A-Z]/)) { // No JSX with uppercase components
    content = content.replace(/import React,\s*{/, 'import {');
    content = content.replace(/import React,\s*\{/, 'import {');
  }
  
  // Remove entire React import if only React is imported and not used
  if (content.match(/^import React from 'react';\s*$/m) &&
      !content.includes("React.") && 
      !content.includes("React ") &&
      !content.match(/<[A-Z]/)) {
    content = content.replace(/import React from 'react';\s*\n/g, '');
  }
  
  return content;
}

// Function to fix console statements (comment them out for now)
function fixConsoleStatements(content) {
  return content.replace(/(\s*)console\.(log|error|warn|info)\(/g, '$1// console.$2(');
}

// Function to fix unused variable warnings by adding underscore prefix
function fixUnusedVariables(content) {
  // Common patterns for unused variables
  const patterns = [
    /(\s+)const\s+(\w+)\s*=\s*useRef\(null\);\s*$/gm, // unused refs
    /(\s+)const\s+(\w+)\s*=\s*useState\([^)]*\);\s*$/gm, // unused state
    /(\s+)const\s+(\w+)\s*=\s*useLanguage\(\);\s*$/gm, // unused useLanguage
  ];
  
  // Note: This is a simplified approach - in real scenarios, you'd need more sophisticated AST parsing
  return content;
}

// Function to fix simple quote consistency
function fixQuotes(content) {
  // Replace double quotes with single quotes in import statements
  content = content.replace(/import\s+([^']*)"([^"]*)"/g, "import $1'$2'");
  return content;
}

// Main processing function
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // Apply fixes
    content = fixUnusedReactImport(content);
    content = fixConsoleStatements(content);
    content = fixQuotes(content);
    
    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
function main() {
  const srcDir = path.join(__dirname, 'src');
  const jsFiles = getAllJSFiles(srcDir);
  
  console.log(`🔍 Found ${jsFiles.length} JavaScript files`);
  console.log('🚀 Starting automatic fixes...\n');
  
  let fixedCount = 0;
  
  jsFiles.forEach(file => {
    if (processFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n✨ Fixed ${fixedCount} files`);
  console.log('🎯 Run "npm run lint" to see remaining issues');
}

// Run the script
main(); 