#!/usr/bin/env node

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Cross-platform command execution
function createCrossPlatformProcess(command, options = {}) {
  const platform = os.platform();
  const isWindows = platform === 'win32';
  
  // Normalize path separators
  const normalizedCwd = options.cwd ? path.resolve(options.cwd) : process.cwd();
  
  const execOptions = {
    cwd: normalizedCwd,
    env: { 
      ...process.env, 
      PATH: process.env.PATH,
      // Ensure npm can be found on Windows
      ...(isWindows && { PATHEXT: process.env.PATHEXT || '.COM;.EXE;.BAT;.CMD' })
    },
    shell: true,
    // Only add windowsHide on Windows
    ...(isWindows && { windowsHide: true })
  };
  
  return exec(command, execOptions);
}

// Services to install dependencies for
const SERVICES = [
  {
    name: 'Root',
    path: '.',
    description: 'Project root dependencies',
    icon: '🏠'
  },
  {
    name: 'Client',
    path: 'client',
    description: 'React frontend dependencies',
    icon: '⚛️'
  },
  {
    name: 'Server',
    path: 'server',
    description: 'Backend API dependencies',
    icon: '🚀'
  }
];

/**
 * Check if directory exists and has package.json
 */
function hasPackageJson(servicePath) {
  // Resolve the path relative to the script's parent directory
  const fullPath = path.resolve(__dirname, '..', servicePath);
  const packageJsonPath = path.join(fullPath, 'package.json');
  return fs.existsSync(packageJsonPath);
}

/**
 * Run npm install for a specific service
 */
function installService(service) {
  return new Promise((resolve, reject) => {
    // Resolve the path relative to the script's parent directory
    const servicePath = path.resolve(__dirname, '..', service.path);
    
    if (!hasPackageJson(service.path)) {
      console.log(`${colors.yellow}⚠️  ${service.name}: No package.json found, skipping...${colors.reset}`);
      resolve();
      return;
    }

    console.log(`${colors.blue}📦 Installing ${service.icon} ${service.name} dependencies...${colors.reset}`);
    console.log(`${colors.cyan}   Path: ${servicePath}${colors.reset}`);
    
    // Use cross-platform npm install command
    const npmCommand = 'npm install --ignore-scripts --no-audit';
    
    const child = createCrossPlatformProcess(npmCommand, {
      cwd: servicePath
    });

    child.stdout.on('data', (data) => {
      process.stdout.write(data);
    });

    child.stderr.on('data', (data) => {
      process.stderr.write(data);
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log(`${colors.green}✅ ${service.name} dependencies installed successfully!${colors.reset}\n`);
        resolve();
      } else {
        console.log(`${colors.red}❌ ${service.name} installation failed with code ${code}${colors.reset}\n`);
        reject(new Error(`${service.name} installation failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      console.log(`${colors.red}❌ ${service.name} installation error: ${error.message}${colors.reset}\n`);
      reject(new Error(`${service.name} installation error: ${error.message}`));
    });
  });
}

/**
 * Install dependencies for all services
 */
async function installAll() {
  console.log(`${colors.bold}${colors.magenta}🚀 Installing dependencies for all services...${colors.reset}\n`);
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const service of SERVICES) {
    try {
      await installService(service);
      successCount++;
    } catch (error) {
      errorCount++;
      console.error(`${colors.red}Failed to install ${service.name}: ${error.message}${colors.reset}`);
    }
  }
  
  console.log(`${colors.bold}📊 Installation Summary:${colors.reset}`);
  console.log(`${colors.green}✅ Success: ${successCount}${colors.reset}`);
  console.log(`${colors.red}❌ Errors: ${errorCount}${colors.reset}\n`);
  
  if (errorCount === 0) {
    console.log(`${colors.green}${colors.bold}🎉 All dependencies installed successfully!${colors.reset}`);
    console.log(`${colors.cyan}💡 You can now run "npm start" to start development.${colors.reset}\n`);
  } else {
    console.log(`${colors.yellow}⚠️  Some installations failed. Please check the errors above.${colors.reset}\n`);
    process.exit(1);
  }
}

/**
 * Install dependencies for specific service
 */
async function installSpecific(serviceName) {
  const service = SERVICES.find(s => s.name.toLowerCase() === serviceName.toLowerCase());
  
  if (!service) {
    console.log(`${colors.red}❌ Service "${serviceName}" not found.${colors.reset}`);
    console.log(`${colors.cyan}Available services: ${SERVICES.map(s => s.name).join(', ')}${colors.reset}\n`);
    process.exit(1);
  }
  
  try {
    await installService(service);
    console.log(`${colors.green}🎉 ${service.name} dependencies installed successfully!${colors.reset}\n`);
  } catch (error) {
    console.error(`${colors.red}❌ Failed to install ${service.name}: ${error.message}${colors.reset}\n`);
    process.exit(1);
  }
}

/**
 * Clean dependencies (node_modules and package-lock.json)
 */
function cleanService(service) {
  return new Promise((resolve) => {
    const servicePath = path.resolve(service.path);
    const nodeModulesPath = path.join(servicePath, 'node_modules');
    const packageLockPath = path.join(servicePath, 'package-lock.json');
    
    console.log(`${colors.yellow}🧹 Cleaning ${service.icon} ${service.name}...${colors.reset}`);
    
    let cleaned = false;
    
    // Remove node_modules
    if (fs.existsSync(nodeModulesPath)) {
      try {
        fs.rmSync(nodeModulesPath, { recursive: true, force: true });
        console.log(`${colors.green}   ✅ Removed node_modules${colors.reset}`);
        cleaned = true;
      } catch (error) {
        console.log(`${colors.red}   ❌ Failed to remove node_modules: ${error.message}${colors.reset}`);
      }
    }
    
    // Remove package-lock.json
    if (fs.existsSync(packageLockPath)) {
      try {
        fs.unlinkSync(packageLockPath);
        console.log(`${colors.green}   ✅ Removed package-lock.json${colors.reset}`);
        cleaned = true;
      } catch (error) {
        console.log(`${colors.red}   ❌ Failed to remove package-lock.json: ${error.message}${colors.reset}`);
      }
    }
    
    if (!cleaned) {
      console.log(`${colors.cyan}   ℹ️  Nothing to clean${colors.reset}`);
    }
    
    console.log('');
    resolve();
  });
}

/**
 * Clean all services
 */
async function cleanAll() {
  console.log(`${colors.bold}${colors.yellow}🧹 Cleaning all services...${colors.reset}\n`);
  
  for (const service of SERVICES) {
    await cleanService(service);
  }
  
  console.log(`${colors.green}✅ All services cleaned successfully!${colors.reset}\n`);
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`${colors.bold}📦 Installation Script${colors.reset}\n`);
    console.log(`${colors.cyan}Usage:${colors.reset}`);
    console.log(`  node scripts/install.js [options] [service]`);
    console.log('');
    console.log(`${colors.cyan}Options:${colors.reset}`);
    console.log(`  --clean, -c     Clean dependencies before installing`);
    console.log(`  --help, -h      Show this help message`);
    console.log('');
    console.log(`${colors.cyan}Services:${colors.reset}`);
    SERVICES.forEach(service => {
      console.log(`  ${service.name.toLowerCase().padEnd(8)} ${service.icon} ${service.description}`);
    });
    console.log('');
    console.log(`${colors.cyan}Examples:${colors.reset}`);
    console.log(`  node scripts/install.js                    # Install all services`);
    console.log(`  node scripts/install.js client             # Install client only`);
    console.log(`  node scripts/install.js --clean            # Clean and install all`);
    console.log(`  node scripts/install.js --clean client     # Clean and install client`);
    return;
  }
  
  const shouldClean = args.includes('--clean') || args.includes('-c');
  const serviceName = args.find(arg => !arg.startsWith('-'));
  
  if (shouldClean) {
    if (serviceName) {
      const service = SERVICES.find(s => s.name.toLowerCase() === serviceName.toLowerCase());
      if (service) {
        await cleanService(service);
      }
    } else {
      await cleanAll();
    }
  }
  
  if (serviceName) {
    await installSpecific(serviceName);
  } else {
    await installAll();
  }
}

// Run the script
main().catch(console.error); 