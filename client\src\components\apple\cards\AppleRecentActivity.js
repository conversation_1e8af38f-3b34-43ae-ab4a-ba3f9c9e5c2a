import { useRef, useEffect, useState, useCallback } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { SplitText } from 'gsap/SplitText';
import { Physics2DPlugin } from 'gsap/Physics2DPlugin';
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin';
import { CustomEase } from 'gsap/CustomEase';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import { useLanguage } from '../../../contexts/LanguageContext';
import {
  UserPlusIcon,
  BriefcaseIcon,
  CheckCircleIcon,
  StarIcon,
  ClockIcon,
  MapPinIcon,
} from '@heroicons/react/24/outline';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, SplitText, Physics2DPlugin, MorphSVGPlugin, CustomEase, MotionPathPlugin);

// Premium easing curves
const liquidEases = {
  elastic: CustomEase.create("elastic", "M0,0 C0.25,0 0.4,1.4 0.7,1 C0.85,0.8 1,1 1,1"),
  bounce: CustomEase.create("bounce", "M0,0 C0.14,0 0.242,0.438 0.272,0.561 0.313,0.728 0.354,0.963 0.362,1 0.37,0.985 0.414,0.928 0.455,0.879 0.504,0.822 0.565,0.729 0.621,0.653 0.681,0.573 0.737,0.5 0.785,0.5 0.856,0.5 0.923,0.717 1,1"),
  liquid: CustomEase.create("liquid", "M0,0 C0.29,0.01 0.49,1.53 0.59,1.23 C0.69,0.93 1,1 1,1"),
  magnetic: CustomEase.create("magnetic", "M0,0 C0.5,0 0.5,1 1,1"),
  wave: CustomEase.create("wave", "M0,0 C0.2,0.8 0.8,0.2 1,1"),
  flow: CustomEase.create("flow", "M0,0 C0.4,0 0.6,1 1,1")
};

// Fix unused variables and useEffect dependencies
const AppleRecentActivity = () => {
  const { t } = useLanguage();
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const feedRef = useRef(null);
  const particleStreamRef = useRef(null);
  const morphingShapesRef = useRef([]);
  const activityRefs = useRef([]);
  const [activities, setActivities] = useState([]);

  const activityTypes = [
    {
      type: 'new_freelancer',
      icon: UserPlusIcon,
      color: 'blue',
      template: data => `${data.name} ${t('joinedAs')} ${data.skill} ${t('specialist')}`,
    },
    {
      type: 'job_posted',
      icon: BriefcaseIcon,
      color: 'green',
      template: data => `${t('newJobPosted')}: ${data.title} - ${data.budget}`,
    },
    {
      type: 'project_completed',
      icon: CheckCircleIcon,
      color: 'purple',
      template: data => `${data.freelancer} ${t('completedProject')} "${data.project}"`,
    },
    {
      type: 'review_posted',
      icon: StarIcon,
      color: 'yellow',
      template: data => `${data.client} ${t('leftReview')} (${data.rating}★) ${t('for')} ${data.freelancer}`,
    },
  ];

  const getColorClasses = (color) => {
    const colorMap = {
      blue: 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400',
      green: 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400',
      purple: 'bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400',
      yellow: 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400',
    };
    return colorMap[color] || colorMap.blue;
  };

  // Advanced Particle Stream System
  const createAdvancedParticleStream = () => {
    if (!particleStreamRef.current) return;

    const container = particleStreamRef.current;
    const particles = [];

    // Create 100 streaming particles for live activity feel
    for (let i = 0; i < 100; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-1 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-40';
      particle.style.left = '0%';
      particle.style.top = Math.random() * 100 + '%';
      container.appendChild(particle);
      particles.push(particle);

      // Create streaming motion path
      const path = `M0,${Math.random() * 100} Q${Math.random() * 50 + 25},${Math.random() * 100} ${100},${Math.random() * 100}`;
      
      gsap.to(particle, {
        motionPath: {
          path: path,
          autoRotate: true
        },
        duration: Math.random() * 8 + 4,
        repeat: -1,
        ease: liquidEases.flow,
        delay: Math.random() * 2
      });

      // Pulsing effect
      gsap.to(particle, {
        scale: "random(0.5, 2)",
        opacity: "random(0.1, 0.7)",
        duration: "random(1, 3)",
        repeat: -1,
        yoyo: true,
        ease: liquidEases.wave
      });
    }
  };

  // Morphing Background Blobs
  const createMorphingActivityBlobs = () => {
    morphingShapesRef.current.forEach((blob, index) => {
      if (!blob) return;

      const colors = [
        'from-blue-400/20 to-purple-400/20',
        'from-green-400/20 to-cyan-400/20',
        'from-purple-400/20 to-pink-400/20',
        'from-orange-400/20 to-red-400/20',
        'from-yellow-400/20 to-green-400/20'
      ];

      blob.className = `absolute bg-gradient-to-br ${colors[index % colors.length]} rounded-full`;

      const morphTimeline = gsap.timeline({ repeat: -1, yoyo: true });
      
      morphTimeline
        .to(blob, {
          borderRadius: "60% 40% 30% 70% / 60% 30% 70% 40%",
          scale: 1.2,
          rotation: 360,
          x: "random(-50, 50)",
          y: "random(-30, 30)",
          duration: 6,
          ease: liquidEases.liquid
        })
        .to(blob, {
          borderRadius: "40% 60% 70% 30% / 40% 70% 30% 60%",
          scale: 0.8,
          rotation: -180,
          x: "random(-30, 30)",
          y: "random(-50, 50)",
          duration: 4,
          ease: liquidEases.elastic
        })
        .to(blob, {
          borderRadius: "70% 30% 40% 60% / 30% 60% 40% 70%",
          scale: 1.1,
          rotation: 270,
          x: "random(-40, 40)",
          y: "random(-40, 40)",
          duration: 5,
          ease: liquidEases.wave
        });

      morphTimeline.delay(index * 1.2);
    });
  };

  // Advanced Title Animation with Real-time Effect
  const createAdvancedTitleAnimation = () => {
    if (!titleRef.current) return;

    const titleElement = titleRef.current.querySelector('h2');
    const subtitleElement = titleRef.current.querySelector('p');

    if (titleElement) {
      const titleSplit = new SplitText(titleElement, { type: "chars,words" });

      gsap.fromTo(titleSplit.chars, {
        opacity: 0,
        y: 80,
        rotationX: -90,
        transformOrigin: "center bottom"
      }, {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration: 1.5,
        stagger: 0.04,
        ease: liquidEases.bounce,
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      });

      // Add real-time pulse effect to "Recent Activity"
      gsap.to(titleElement, {
        textShadow: "0 0 20px rgba(59, 130, 246, 0.6)",
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: liquidEases.wave
      });
    }

    if (subtitleElement) {
      const subtitleSplit = new SplitText(subtitleElement, { type: "words" });

      gsap.fromTo(subtitleSplit.words, {
        opacity: 0,
        y: 40,
        scale: 0.8
      }, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1.0,
        stagger: 0.15,
        ease: liquidEases.elastic,
        delay: 0.8,
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      });
    }
  };

  // Advanced Activity Card Hover with Physics
  const createAdvancedActivityHover = (activityElement, index) => {
    if (!activityElement) return;

    const icon = activityElement.querySelector('.activity-icon');
    const content = activityElement.querySelector('.activity-content');
    const pulse = activityElement.querySelector('.activity-pulse');

    let isHovering = false;

    activityElement.addEventListener('mouseenter', () => {
      isHovering = true;

      // Magnetic hover timeline
      const hoverTL = gsap.timeline();

      hoverTL
        .to(activityElement, {
          scale: 1.03,
          y: -8,
          rotationY: 3,
          boxShadow: "0 20px 40px rgba(0,0,0,0.12)",
          duration: 0.6,
          ease: liquidEases.magnetic
        })
        .to(icon, {
          scale: 1.4,
          rotation: 360,
          duration: 1.0,
          ease: liquidEases.bounce
        }, 0)
        .to(pulse, {
          scale: 2,
          opacity: 0.8,
          duration: 0.8,
          ease: liquidEases.elastic
        }, 0.1);

      // Create activity burst particles
      createActivityBurstParticles(activityElement, index);
    });

    activityElement.addEventListener('mouseleave', () => {
      isHovering = false;

      gsap.to(activityElement, {
        scale: 1,
        y: 0,
        rotationY: 0,
        boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
        duration: 0.8,
        ease: liquidEases.elastic
      });

      gsap.to(icon, {
        scale: 1,
        rotation: 0,
        duration: 0.6,
        ease: liquidEases.bounce
      });

      gsap.to(pulse, {
        scale: 1,
        opacity: 0.3,
        duration: 0.5,
        ease: liquidEases.wave
      });
    });

    // Real-time mouse tracking
    activityElement.addEventListener('mousemove', (e) => {
      if (!isHovering) return;

      const rect = activityElement.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      const mouseX = e.clientX - centerX;
      const mouseY = e.clientY - centerY;

      gsap.to(activityElement, {
        x: mouseX * 0.05,
        y: mouseY * 0.05,
        duration: 0.3,
        ease: "power2.out"
      });

      gsap.to(icon, {
        x: mouseX * 0.1,
        y: mouseY * 0.1,
        duration: 0.2,
        ease: "power2.out"
      });
    });
  };

  // Create Activity Burst Particles
  const createActivityBurstParticles = (element, index) => {
    const colors = ['#3B82F6', '#10B981', '#8B5CF6', '#F59E0B'];
    const color = colors[index % colors.length];

    for (let i = 0; i < 12; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute pointer-events-none w-2 h-2 rounded-full';
      particle.style.background = color;
      particle.style.opacity = '0';
      element.appendChild(particle);

      const angle = (i / 12) * Math.PI * 2;
      const distance = 60 + Math.random() * 40;

      gsap.set(particle, {
        x: 0,
        y: 0,
        scale: 0
      });

      gsap.to(particle, {
        x: Math.cos(angle) * distance,
        y: Math.sin(angle) * distance,
        scale: 1.5,
        opacity: 0.9,
        duration: 0.8,
        ease: liquidEases.elastic
      });

      gsap.to(particle, {
        opacity: 0,
        scale: 0,
        duration: 0.6,
        delay: 0.8,
        ease: "power2.in",
        onComplete: () => {
          if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
          }
        }
      });
    }
  };

  // Advanced Feed Animation with Real-time Streaming
  const createAdvancedFeedAnimation = () => {
    if (!feedRef.current) return;

    const activityElements = Array.from(feedRef.current.children);

    activityElements.forEach((activity, index) => {
      // Store reference for hover effects
      activityRefs.current[index] = activity;

      // Create magnetic hover effect
      createAdvancedActivityHover(activity, index);

      // Streaming entrance animation
      gsap.fromTo(activity, {
        opacity: 0,
        x: -100,
        scale: 0.8,
        rotationY: -30
      }, {
        opacity: 1,
        x: 0,
        scale: 1,
        rotationY: 0,
        duration: 1.0,
        delay: index * 0.15,
        ease: liquidEases.elastic,
        scrollTrigger: {
          trigger: activity,
          start: 'top 95%',
          toggleActions: 'play none none reverse'
        }
      });

      // Real-time pulse animation for recent activities
      if (index < 3) { // First 3 activities get real-time pulse
        const pulse = activity.querySelector('.activity-pulse');
        if (pulse) {
          gsap.to(pulse, {
            scale: 1.5,
            opacity: 0.1,
            duration: 2,
            repeat: -1,
            yoyo: true,
            ease: liquidEases.wave
          });
        }
      }

      // Animate content with physics
      const icon = activity.querySelector('.activity-icon');
      const content = activity.querySelector('.activity-content');

      if (icon) {
        gsap.fromTo(icon, {
          scale: 0,
          rotation: -180
        }, {
          scale: 1,
          rotation: 0,
          duration: 0.8,
          delay: index * 0.15 + 0.3,
          ease: liquidEases.bounce,
          scrollTrigger: {
            trigger: activity,
            start: 'top 95%',
            toggleActions: 'play none none reverse'
          }
        });
      }

      if (content) {
        const contentChildren = Array.from(content.children);
        gsap.fromTo(contentChildren, {
          opacity: 0,
          y: 20
        }, {
          opacity: 1,
          y: 0,
          duration: 0.6,
          stagger: 0.1,
          delay: index * 0.15 + 0.5,
          ease: liquidEases.wave,
          scrollTrigger: {
            trigger: activity,
            start: 'top 95%',
            toggleActions: 'play none none reverse'
          }
        });
      }
    });
  };

  // Simulate real-time activity updates
  const simulateRealTimeUpdates = () => {
    setInterval(() => {
      if (activityRefs.current.length > 0) {
        const randomActivity = activityRefs.current[Math.floor(Math.random() * Math.min(3, activityRefs.current.length))];
        if (randomActivity) {
          const pulse = randomActivity.querySelector('.activity-pulse');
          if (pulse) {
            gsap.fromTo(pulse, {
              scale: 1,
              opacity: 0.3
            }, {
              scale: 2,
              opacity: 0,
              duration: 1.5,
              ease: liquidEases.elastic
            });
          }
        }
      }
    }, 3000); // Update every 3 seconds
  };

  const generateMockActivities = useCallback(() => {
    const mockData = [
      {
        id: 1,
        type: 'new_freelancer',
        data: { name: 'Sarah Chen', skill: 'UI/UX Design' },
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        location: 'San Francisco, CA',
      },
      {
        id: 2,
        type: 'job_posted',
        data: { title: 'React Developer Needed', budget: '$2,500' },
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
        location: 'Remote',
      },
      {
        id: 3,
        type: 'project_completed',
        data: { freelancer: 'Marcus Johnson', project: 'E-commerce Website' },
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        location: 'New York, NY',
      },
      {
        id: 4,
        type: 'review_posted',
        data: { client: 'TechCorp Inc.', rating: 5, freelancer: 'Elena Rodriguez' },
        timestamp: new Date(Date.now() - 45 * 60 * 1000),
        location: 'Miami, FL',
      },
      {
        id: 5,
        type: 'new_freelancer',
        data: { name: 'David Kim', skill: 'Mobile Development' },
        timestamp: new Date(Date.now() - 60 * 60 * 1000),
        location: 'Seattle, WA',
      },
      {
        id: 6,
        type: 'job_posted',
        data: { title: 'Content Writer for Blog', budget: '$800' },
        timestamp: new Date(Date.now() - 90 * 60 * 1000),
        location: 'Remote',
      },
      {
        id: 7,
        type: 'project_completed',
        data: { freelancer: 'Priya Patel', project: 'Data Analysis Dashboard' },
        timestamp: new Date(Date.now() - 120 * 60 * 1000),
        location: 'Austin, TX',
      },
      {
        id: 8,
        type: 'review_posted',
        data: { client: 'StartupXYZ', rating: 4.8, freelancer: 'Alex Thompson' },
        timestamp: new Date(Date.now() - 150 * 60 * 1000),
        location: 'London, UK',
      },
    ];

    setActivities(mockData);
  }, []);

  useEffect(() => {
    generateMockActivities();
  }, [generateMockActivities]);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Initialize all advanced animations
      createAdvancedParticleStream();
      createMorphingActivityBlobs();
      createAdvancedTitleAnimation();
      createAdvancedFeedAnimation();
      simulateRealTimeUpdates();
    }, sectionRef);

    return () => ctx.revert();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activities]);

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now - timestamp) / (1000 * 60));
    
    if (diffInMinutes < 1) return t('justNow');
    if (diffInMinutes < 60) return `${diffInMinutes}m ${t('ago')}`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ${t('ago')}`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ${t('ago')}`;
  };

  return (
    <>
      {/* Enhanced CSS */}
      <style>{`
        @keyframes liquid-stream {
          0%, 100% {
            border-radius: 50% 40% 60% 30% / 40% 60% 30% 50%;
          }
          25% {
            border-radius: 30% 50% 40% 60% / 60% 30% 50% 40%;
          }
          50% {
            border-radius: 60% 30% 50% 40% / 30% 50% 40% 60%;
          }
          75% {
            border-radius: 40% 60% 30% 50% / 50% 40% 60% 30%;
          }
        }
        
        .liquid-stream {
          animation: liquid-stream 10s ease-in-out infinite;
        }
        
        .activity-card {
          transform-style: preserve-3d;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .real-time-pulse {
          animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        @keyframes pulse {
          0%, 100% {
            opacity: 0.3;
            transform: scale(1);
          }
          50% {
            opacity: 0.8;
            transform: scale(1.2);
          }
        }
        
        .streaming-particles {
          will-change: transform;
        }
      `}</style>

      <section
        ref={sectionRef}
        className='relative py-20 bg-gray-50 dark:bg-gray-800 transition-colors duration-300 overflow-hidden'
      >
        {/* Advanced Multi-layer Background */}
        <div className='absolute inset-0'>
          <div 
            ref={el => morphingShapesRef.current[0] = el}
            className='absolute top-10 left-10 w-40 h-40 liquid-stream'
          />
          <div 
            ref={el => morphingShapesRef.current[1] = el}
            className='absolute top-1/4 right-20 w-32 h-32 liquid-stream'
          />
          <div 
            ref={el => morphingShapesRef.current[2] = el}
            className='absolute bottom-1/3 left-1/4 w-48 h-48 liquid-stream'
          />
          <div 
            ref={el => morphingShapesRef.current[3] = el}
            className='absolute bottom-10 right-10 w-36 h-36 liquid-stream'
          />
          <div 
            ref={el => morphingShapesRef.current[4] = el}
            className='absolute top-1/2 left-1/2 w-28 h-28 liquid-stream'
          />
        </div>

        {/* Advanced Particle Stream Canvas */}
        <div 
          ref={particleStreamRef}
          className='absolute inset-0 pointer-events-none overflow-hidden streaming-particles'
        />

        <div className='relative mx-auto max-w-4xl px-4 sm:px-6 lg:px-8'>
          {/* Enhanced Section Header */}
          <div ref={titleRef} className='text-center mb-12'>
            <h2 className='text-4xl sm:text-5xl lg:text-6xl font-black text-gray-900 dark:text-gray-100 mb-8 transition-colors duration-300'>
              {t('recentActivity')}
            </h2>
            <p className='text-xl sm:text-2xl lg:text-3xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed transition-colors duration-300'>
              {t('stayUpdatedWithLatest')}
            </p>
          </div>

          {/* Enhanced Activity Feed */}
          <div
            ref={feedRef}
            className='space-y-6'
          >
            {activities.map((activity, index) => {
              const activityType = activityTypes.find(type => type.type === activity.type);
              if (!activityType) return null;

              const IconComponent = activityType.icon;
              const colorClasses = getColorClasses(activityType.color);

              return (
                <div
                  key={activity.id}
                  className='activity-card relative bg-white dark:bg-gray-900 rounded-3xl p-8 border border-gray-100 dark:border-gray-700 transition-all duration-500 hover:shadow-2xl hover:border-gray-200 dark:hover:border-gray-600 transform-gpu'
                >
                  {/* Real-time Pulse Indicator */}
                  <div className={`activity-pulse absolute -top-2 -right-2 w-4 h-4 rounded-full ${colorClasses.split(' ')[0]} opacity-30`} />

                  <div className='flex items-start space-x-6'>
                    {/* Enhanced Icon */}
                    <div className={`activity-icon flex-shrink-0 w-16 h-16 rounded-2xl flex items-center justify-center ${colorClasses} shadow-lg transform-gpu`}>
                      <IconComponent className='h-8 w-8' />
                    </div>

                    {/* Enhanced Content */}
                    <div className='activity-content flex-1 min-w-0'>
                      <p className='text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3 transition-colors duration-300'>
                        {activityType.template(activity.data)}
                      </p>
                      
                      <div className='flex items-center space-x-6 text-gray-600 dark:text-gray-400'>
                        <div className='flex items-center space-x-2'>
                          <ClockIcon className='h-5 w-5' />
                          <span className='font-medium'>{formatTimeAgo(activity.timestamp)}</span>
                        </div>
                        <div className='flex items-center space-x-2'>
                          <MapPinIcon className='h-5 w-5' />
                          <span className='font-medium'>{activity.location}</span>
                        </div>
                      </div>
                    </div>

                    {/* Live Status Indicator */}
                    <div className='flex-shrink-0'>
                      <div className={`w-3 h-3 rounded-full ${index < 3 ? 'real-time-pulse' : ''} ${colorClasses.split(' ')[0]}`} />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Enhanced Load More Button */}
          <div className='text-center mt-12'>
            <button className='group px-8 py-4 text-lg font-semibold text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg'>
              <span className='mr-2'>{t('loadMoreActivity')}</span>
              <span className='inline-block transition-transform duration-300 group-hover:translate-x-1'>→</span>
            </button>
          </div>
        </div>
      </section>
    </>
  );
};

export default AppleRecentActivity;
