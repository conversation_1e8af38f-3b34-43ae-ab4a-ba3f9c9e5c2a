/**
 * Optimized Animation Hook for VWork Platform
 * Provides performance-aware GSAP animations with autoscaling
 */

import { useEffect, useRef, useCallback } from 'react';
import { gsap } from 'gsap';
import responsiveAutoscaling from '../services/responsiveAutoscaling';

export const useOptimizedAnimation = (animationType = 'default') => {
  const elementRef = useRef(null);
  const timelineRef = useRef(null);
  const performanceLevelRef = useRef(responsiveAutoscaling.currentPerformanceLevel);

  // Get optimized animation settings based on current performance level
  const getOptimizedSettings = useCallback(() => {
    const settings = responsiveAutoscaling.getOptimizedGSAPSettings();
    const shouldUseFullAnimations = responsiveAutoscaling.shouldUseFullAnimations();
    
    return {
      ...settings,
      enabled: shouldUseFullAnimations,
      force3D: settings.force3D && shouldUseFullAnimations,
      autoAlpha: settings.autoAlpha && shouldUseFullAnimations
    };
  }, []);

  // Create optimized timeline
  const createTimeline = useCallback((config = {}) => {
    const optimizedSettings = getOptimizedSettings();
    
    if (timelineRef.current) {
      timelineRef.current.kill();
    }
    
    timelineRef.current = gsap.timeline({
      paused: true,
      ...config,
      defaults: {
        ...optimizedSettings,
        ...config.defaults
      }
    });
    
    return timelineRef.current;
  }, [getOptimizedSettings]);

  // Animate element with performance optimization
  const animateElement = useCallback((target, vars, position) => {
    const optimizedSettings = getOptimizedSettings();
    
    if (!optimizedSettings.enabled) {
      // Skip animation if performance is too low
      if (vars.opacity !== undefined) {
        gsap.set(target, { opacity: vars.opacity });
      }
      if (vars.x !== undefined || vars.y !== undefined) {
        gsap.set(target, { x: vars.x || 0, y: vars.y || 0 });
      }
      return;
    }
    
    const optimizedVars = {
      ...vars,
      duration: vars.duration || optimizedSettings.duration,
      ease: vars.ease || optimizedSettings.ease,
      force3D: optimizedSettings.force3D
    };
    
    if (timelineRef.current) {
      return timelineRef.current.to(target, optimizedVars, position);
    } else {
      return gsap.to(target, optimizedVars);
    }
  }, [getOptimizedSettings]);

  // Stagger animation with performance optimization
  const staggerAnimation = useCallback((targets, vars, stagger = 0.1) => {
    const optimizedSettings = getOptimizedSettings();
    
    if (!optimizedSettings.enabled) {
      // Set final state immediately if performance is too low
      gsap.set(targets, {
        opacity: vars.opacity || 1,
        x: vars.x || 0,
        y: vars.y || 0
      });
      return;
    }
    
    const optimizedVars = {
      ...vars,
      duration: vars.duration || optimizedSettings.duration,
      ease: vars.ease || optimizedSettings.ease,
      stagger: stagger * (optimizedSettings.stagger || 1),
      force3D: optimizedSettings.force3D
    };
    
    if (timelineRef.current) {
      return timelineRef.current.to(targets, optimizedVars);
    } else {
      return gsap.to(targets, optimizedVars);
    }
  }, [getOptimizedSettings]);

  // Entrance animation
  const animateIn = useCallback((target = elementRef.current, config = {}) => {
    if (!target) return;
    
    const optimizedSettings = getOptimizedSettings();
    
    const defaultConfig = {
      from: { opacity: 0, y: 30, scale: 0.95 },
      to: { opacity: 1, y: 0, scale: 1 },
      duration: optimizedSettings.duration,
      ease: optimizedSettings.ease
    };
    
    const finalConfig = { ...defaultConfig, ...config };
    
    if (!optimizedSettings.enabled) {
      gsap.set(target, finalConfig.to);
      return;
    }
    
    gsap.fromTo(target, finalConfig.from, {
      ...finalConfig.to,
      duration: finalConfig.duration,
      ease: finalConfig.ease,
      force3D: optimizedSettings.force3D
    });
  }, [getOptimizedSettings]);

  // Exit animation
  const animateOut = useCallback((target = elementRef.current, config = {}) => {
    if (!target) return;
    
    const optimizedSettings = getOptimizedSettings();
    
    const defaultConfig = {
      to: { opacity: 0, y: -30, scale: 0.95 },
      duration: optimizedSettings.duration * 0.7,
      ease: 'power2.in'
    };
    
    const finalConfig = { ...defaultConfig, ...config };
    
    if (!optimizedSettings.enabled) {
      gsap.set(target, finalConfig.to);
      return;
    }
    
    return gsap.to(target, {
      ...finalConfig.to,
      duration: finalConfig.duration,
      ease: finalConfig.ease,
      force3D: optimizedSettings.force3D
    });
  }, [getOptimizedSettings]);

  // Hover animation
  const createHoverAnimation = useCallback((target = elementRef.current) => {
    if (!target) return { enter: () => {}, leave: () => {} };
    
    const optimizedSettings = getOptimizedSettings();
    
    if (!optimizedSettings.enabled) {
      return { enter: () => {}, leave: () => {} };
    }
    
    const enter = () => {
      gsap.to(target, {
        scale: 1.05,
        duration: optimizedSettings.duration * 0.5,
        ease: 'power2.out',
        force3D: optimizedSettings.force3D
      });
    };
    
    const leave = () => {
      gsap.to(target, {
        scale: 1,
        duration: optimizedSettings.duration * 0.5,
        ease: 'power2.out',
        force3D: optimizedSettings.force3D
      });
    };
    
    return { enter, leave };
  }, [getOptimizedSettings]);

  // Scroll-triggered animation
  const createScrollAnimation = useCallback((target = elementRef.current, config = {}) => {
    if (!target) return;
    
    const optimizedSettings = getOptimizedSettings();
    
    if (!optimizedSettings.enabled) {
      gsap.set(target, { opacity: 1, y: 0 });
      return;
    }
    
    const defaultConfig = {
      from: { opacity: 0, y: 50 },
      to: { opacity: 1, y: 0 },
      trigger: target,
      start: 'top 80%',
      toggleActions: 'play none none reverse'
    };
    
    const finalConfig = { ...defaultConfig, ...config };
    
    gsap.fromTo(target, finalConfig.from, {
      ...finalConfig.to,
      duration: optimizedSettings.duration,
      ease: optimizedSettings.ease,
      force3D: optimizedSettings.force3D,
      scrollTrigger: {
        trigger: finalConfig.trigger,
        start: finalConfig.start,
        toggleActions: finalConfig.toggleActions
      }
    });
  }, [getOptimizedSettings]);

  // Listen for performance level changes
  useEffect(() => {
    const handlePerformanceLevelChange = (event) => {
      const newLevel = event.detail.level;
      
      if (newLevel !== performanceLevelRef.current) {
        performanceLevelRef.current = newLevel;
        
        // Kill existing animations if performance dropped significantly
        if (newLevel === 'minimal' || newLevel === 'low') {
          if (timelineRef.current) {
            timelineRef.current.kill();
          }
          gsap.killTweensOf(elementRef.current);
        }
      }
    };
    
    window.addEventListener('performanceLevelChange', handlePerformanceLevelChange);
    
    return () => {
      window.removeEventListener('performanceLevelChange', handlePerformanceLevelChange);
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
      if (elementRef.current) {
        gsap.killTweensOf(elementRef.current);
      }
    };
  }, []);

  return {
    elementRef,
    timelineRef,
    createTimeline,
    animateElement,
    staggerAnimation,
    animateIn,
    animateOut,
    createHoverAnimation,
    createScrollAnimation,
    getOptimizedSettings,
    performanceLevel: performanceLevelRef.current
  };
};
