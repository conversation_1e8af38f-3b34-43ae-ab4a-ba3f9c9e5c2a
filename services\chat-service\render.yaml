services:
  - type: web
    runtime: node
    name: vwork-chat-service
    region: oregon
    branch: main
    rootDir: services/chat-service
    buildCommand: npm ci
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3005
      - key: AUTH_SERVICE_URL
        value: https://vwork-auth-service.onrender.com
      - key: CORS_ORIGINS
        value: https://vwork-client.onrender.com,https://vwork-gateway.onrender.com
    plan: free
