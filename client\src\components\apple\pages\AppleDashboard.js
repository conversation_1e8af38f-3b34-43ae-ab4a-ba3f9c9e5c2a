import { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { Link } from 'react-router-dom';
import {
  HomeIcon,
  TrophyIcon,
  CurrencyDollarIcon,
  ClockIcon,
  StarIcon,
  BoltIcon,
  ChartBarIcon,
  CheckCircleIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  EyeIcon,
  BriefcaseIcon,
} from '@heroicons/react/24/outline';
import { mockDataService } from '../../../services/mockData';
import { toast } from 'react-hot-toast';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useAuth } from '../../../contexts/AuthContext';

const AppleDashboard = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const pageRef = useRef(null);
  const statsRef = useRef(null);
  const activityRef = useRef(null);

  // Apple-style animations
  useEffect(() => {
    if (pageRef.current) {
      gsap.fromTo(
        pageRef.current,
        { y: 20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: 'power2.out' }
      );
    }

    if (statsRef.current) {
      gsap.fromTo(
        statsRef.current.children,
        { y: 30, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.6,
          ease: 'power2.out',
          stagger: 0.1,
          delay: 0.2,
        }
      );
    }
  }, [dashboardData]);

  // Load dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        const data = await mockDataService.getDashboardData();
        setDashboardData(data);
      } catch (error) {
        // console.('Error loading dashboard:', error);
        toast.error('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  const formatCurrency = amount => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatTimeAgo = timestamp => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInHours = Math.floor((now - time) / (1000 * 60 * 60));

    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    }
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const getActivityIcon = type => {
    switch (type) {
      case 'project_completed':
        return <CheckCircleIcon className='h-4 w-4' />;
      case 'payment_received':
        return <CurrencyDollarIcon className='h-4 w-4' />;
      case 'new_message':
        return <ChatBubbleLeftRightIcon className='h-4 w-4' />;
      case 'project_started':
        return <BoltIcon className='h-4 w-4' />;
      default:
        return <DocumentTextIcon className='h-4 w-4' />;
    }
  };

  const getStatusColor = status => {
    switch (status) {
      case 'on-track':
        return 'text-green-600';
      case 'ahead':
        return 'text-blue-600';
      case 'behind':
        return 'text-red-600';
      default:
        return 'text-orange-600';
    }
  };

  if (loading) {
    return (
      <div className='min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center transition-colors duration-300'>
        <div className='text-center'>
          <div className='w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6 animate-pulse'>
            <TrophyIcon className='h-8 w-8 text-white' />
          </div>
          <h3 className='text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-2 transition-colors duration-300'>
            {t('loadingDashboard') || (t('language') === 'vi' ? 'Đang tải Dashboard...' : 'Loading Dashboard...')}
          </h3>
          <p className='text-gray-600 dark:text-gray-400 transition-colors duration-300'>{t('gatheringData') || (t('language') === 'vi' ? 'Đang thu thập dữ liệu mới nhất của bạn' : 'Gathering your latest data')}</p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className='min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center transition-colors duration-300'>
        <div className='text-center'>
          <TrophyIcon className='h-16 w-16 text-red-500 mx-auto mb-4' />
          <h3 className='text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4 transition-colors duration-300'>
            {t('failedToLoadDashboard') || (t('language') === 'vi' ? 'Không thể tải Dashboard' : 'Failed to Load Dashboard')}
          </h3>
        </div>
      </div>
    );
  }

  const { currentUser, stats, recentActivity, activeProjects } = dashboardData;

  return (
    <div className='min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300'>
      <div
        ref={pageRef}
        className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'
      >
        {/* Header */}
        <div className='text-center mb-12'>
          <div className='w-16 h-16 bg-blue-500 rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg'>
            <TrophyIcon className='h-8 w-8 text-white' />
          </div>
          <h1 className='text-4xl md:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-4 transition-colors duration-300'>
            {t('welcomeBack')} {currentUser?.displayName || t('user')}!
          </h1>
          <p className='text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed transition-colors duration-300'>
            {t('hereDashboardActivity')}
          </p>
        </div>

        {/* Stats Cards */}
        <div
          ref={statsRef}
          className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'
        >
          <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 text-center shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-lg transition-all duration-200'>
            <div className='w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-xl mx-auto mb-4 flex items-center justify-center'>
              <CurrencyDollarIcon className='h-6 w-6 text-green-600 dark:text-green-400' />
            </div>
            <h3 className='text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1 transition-colors duration-300'>
              {t('totalEarnings')}
            </h3>
            <p className='text-3xl font-bold text-green-600 dark:text-green-400 transition-colors duration-300'>
              {formatCurrency(stats?.totalEarnings || 0)}
            </p>
            <p className='text-sm text-gray-600 dark:text-gray-400 mt-1 transition-colors duration-300'>
              +{formatCurrency(stats?.thisMonthEarnings || 0)} {t('thisMonth')}
            </p>
          </div>

          <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 text-center shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-lg transition-all duration-200'>
            <div className='w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl mx-auto mb-4 flex items-center justify-center'>
              <BoltIcon className='h-6 w-6 text-blue-600 dark:text-blue-400' />
            </div>
            <h3 className='text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1 transition-colors duration-300'>
              {t('activeProjects') || (t('language') === 'vi' ? 'Dự Án Đang Hoạt Động' : 'Active Projects')}
            </h3>
            <p className='text-3xl font-bold text-blue-600 dark:text-blue-400 transition-colors duration-300'>
              {stats?.activeProjects || 0}
            </p>
            <p className='text-sm text-gray-600 dark:text-gray-400 mt-1 transition-colors duration-300'>
              {stats?.completedProjects || 0} {t('completed') || (t('language') === 'vi' ? 'đã hoàn thành' : 'completed')}
            </p>
          </div>

          <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 text-center shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-lg transition-all duration-200'>
            <div className='w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-xl mx-auto mb-4 flex items-center justify-center'>
              <StarIcon className='h-6 w-6 text-yellow-600 dark:text-yellow-400' />
            </div>
            <h3 className='text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1 transition-colors duration-300'>
              {t('rating') || (t('language') === 'vi' ? 'Đánh Giá' : 'Rating')}
            </h3>
            <p className='text-3xl font-bold text-yellow-600 dark:text-yellow-400 transition-colors duration-300'>
              {stats?.averageRating || 0}
            </p>
            <p className='text-sm text-gray-600 dark:text-gray-400 mt-1 transition-colors duration-300'>
              {t('successRate') || (t('language') === 'vi' ? 'tỷ lệ thành công' : 'success rate')}
            </p>
          </div>

          <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 text-center shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-lg transition-all duration-200'>
            <div className='w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-xl mx-auto mb-4 flex items-center justify-center'>
              <ClockIcon className='h-6 w-6 text-purple-600 dark:text-purple-400' />
            </div>
            <h3 className='text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1 transition-colors duration-300'>
              {t('responseTime') || (t('language') === 'vi' ? 'Thời Gian Phản Hồi' : 'Response Time')}
            </h3>
            <p className='text-3xl font-bold text-purple-600 dark:text-purple-400 transition-colors duration-300'>
              {stats?.averageResponseTime || '1h'}
            </p>
            <p className='text-sm text-gray-600 dark:text-gray-400 mt-1 transition-colors duration-300'>
              {t('averageResponse') || (t('language') === 'vi' ? 'phản hồi trung bình' : 'average response')}
            </p>
          </div>
        </div>

        <div className='grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8'>
          {/* Recent Activity */}
          <div
            ref={activityRef}
            className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100'
          >
            <div className='flex items-center justify-between mb-6'>
              <h2 className='text-2xl font-semibold text-gray-900'>
                {t('recentActivity') || (t('language') === 'vi' ? 'Hoạt Động Gần Đây' : 'Recent Activity')}
              </h2>
              <ChartBarIcon className='h-6 w-6 text-gray-400' />
            </div>

            <div className='space-y-4'>
              {recentActivity?.slice(0, 5).map(activity => (
                <div
                  key={activity.id}
                  className='flex items-start space-x-4 p-4 bg-gray-50 rounded-xl'
                >
                  <div
                    className={`flex-shrink-0 p-2 rounded-lg ${
                      activity.status === 'positive'
                        ? 'bg-green-100 text-green-600'
                        : activity.status === 'negative'
                          ? 'bg-red-100 text-red-600'
                          : 'bg-blue-100 text-blue-600'
                    }`}
                  >
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className='flex-1 min-w-0'>
                    <h4 className='font-semibold text-gray-900'>
                      {activity.title}
                    </h4>
                    <p className='text-sm text-gray-600 mt-1'>
                      {activity.description}
                    </p>
                    <div className='flex items-center justify-between mt-2'>
                      <span className='text-xs text-gray-500'>
                        {formatTimeAgo(activity.timestamp)}
                      </span>
                      {activity.amount ? (
                        <span className='text-sm font-semibold text-green-600'>
                          +{formatCurrency(activity.amount)}
                        </span>
                      ) : null}
                    </div>
                  </div>
                </div>
              )) || []}
            </div>
          </div>

          {/* Active Projects */}
          <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100'>
            <div className='flex items-center justify-between mb-6'>
              <h2 className='text-2xl font-semibold text-gray-900'>
                {t('activeProjects') || (t('language') === 'vi' ? 'Dự Án Đang Hoạt Động' : 'Active Projects')}
              </h2>
              <BoltIcon className='h-6 w-6 text-gray-400' />
            </div>

            <div className='space-y-4'>
              {activeProjects?.map(project => (
                <div key={project.id} className='p-4 bg-gray-50 rounded-xl'>
                  <div className='flex items-center justify-between mb-3'>
                    <h4 className='font-semibold text-gray-900'>
                      {project.title}
                    </h4>
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${getStatusColor(project.status)} bg-white`}
                    >
                      {project.status}
                    </span>
                  </div>

                  <p className='text-sm text-gray-600 mb-3'>
                    {t('client') || (t('language') === 'vi' ? 'Khách Hàng' : 'Client')}: {project.client}
                  </p>

                  {/* Progress Bar */}
                  <div className='mb-3'>
                    <div className='flex justify-between text-sm text-gray-600 mb-1'>
                      <span>{t('progress') || (t('language') === 'vi' ? 'Tiến Độ' : 'Progress')}</span>
                      <span>{project.progress}%</span>
                    </div>
                    <div className='w-full bg-gray-200 rounded-full h-2'>
                      <div
                        className='bg-blue-500 h-2 rounded-full transition-all duration-300'
                        style={{ width: `${project.progress}%` }}
                      />
                    </div>
                  </div>

                  <div className='flex items-center justify-between text-sm'>
                    <span className='text-gray-600'>
                      {t('deadline') || (t('language') === 'vi' ? 'Hạn Chót' : 'Deadline')}: {project.deadline?.toLocaleDateString()}
                    </span>
                    <span className='font-semibold text-green-600'>
                      {formatCurrency(project.budget)}
                    </span>
                  </div>
                </div>
              )) || []}
            </div>

            <Link
              to='/projects'
              className='block mt-4 text-center btn btn-secondary py-3'
            >
              {t('viewAllProjects') || (t('language') === 'vi' ? 'Xem Tất Cả Dự Án' : 'View All Projects')}
            </Link>
          </div>
        </div>

        {/* Quick Actions */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100'>
          <h2 className='text-2xl font-semibold text-gray-900 mb-6 text-center'>
            {t('quickActions') || (t('language') === 'vi' ? 'Hành Động Nhanh' : 'Quick Actions')}
          </h2>

          <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
            <Link
              to='/projects'
              className='btn btn-primary text-center py-4 flex flex-col items-center'
            >
              <BoltIcon className='h-6 w-6 mb-2' />
              <span className='text-sm'>{t('browseProjects') || (t('language') === 'vi' ? 'Duyệt Dự Án' : 'Browse Projects')}</span>
            </Link>

            <Link
              to='/messages'
              className='btn btn-secondary text-center py-4 flex flex-col items-center'
            >
              <ChatBubbleLeftRightIcon className='h-6 w-6 mb-2' />
              <span className='text-sm'>{t('messages') || (t('language') === 'vi' ? 'Tin Nhắn' : 'Messages')}</span>
            </Link>

            <Link
              to='/settings'
              className='btn btn-secondary text-center py-4 flex flex-col items-center'
            >
              <HomeIcon className='h-6 w-6 mb-2' />
              <span className='text-sm'>{t('settings') || (t('language') === 'vi' ? 'Cài Đặt' : 'Settings')}</span>
            </Link>

            {user?.userType === 'client' ? (
              <Link
                to='/jobs/create'
                className='btn btn-secondary text-center py-4 flex flex-col items-center'
              >
                <BriefcaseIcon className='h-6 w-6 mb-2' />
                <span className='text-sm'>Đăng việc</span>
              </Link>
            ) : (
              <Link
                to='/freelancers'
                className='btn btn-secondary text-center py-4 flex flex-col items-center'
              >
                <EyeIcon className='h-6 w-6 mb-2' />
                <span className='text-sm'>{t('findTalent') || (t('language') === 'vi' ? 'Tìm Tài Năng' : 'Find Talent')}</span>
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppleDashboard;
