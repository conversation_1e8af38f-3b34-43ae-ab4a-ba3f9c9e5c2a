import { useState, useRef, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { gsap } from 'gsap';
import { useAuth } from '../../../contexts/AuthContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import FirebaseStatus from '../../common/FirebaseStatus';
import PasswordStrengthIndicator from '../../common/PasswordStrengthIndicator';
import AuthErrorDisplay from '../../common/AuthErrorDisplay';
import GoogleLoginHelper from '../../common/GoogleLoginHelper';
import {
  EyeIcon,
  EyeSlashIcon,
  BriefcaseIcon,
  UserIcon,
  EnvelopeIcon,
  LockClosedIcon,
  ArrowRightIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

const AppleAuthPage = () => {
  const { t } = useLanguage();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const mode = searchParams.get('mode');
  
  // Set initial state based on URL parameter
  const [isLogin, setIsLogin] = useState(mode !== 'signup');
  
  // Update URL when mode changes
  useEffect(() => {
    const newMode = isLogin ? 'signin' : 'signup';
    const newUrl = `/auth?mode=${newMode}`;
    window.history.replaceState(null, '', newUrl);
  }, [isLogin]);

  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    userType: 'freelancer',
  });
  const [errors, setErrors] = useState({});
  const [errorCode, setErrorCode] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showEmailVerification, setShowEmailVerification] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState('');
  const [googleLoginAttempted, setGoogleLoginAttempted] = useState(false);

  const { login, register, loginWithGoogle, resetPassword, sendVerificationEmail } = useAuth();
  const navigate = useNavigate();
  const containerRef = useRef(null);
  const formRef = useRef(null);
  const titleRef = useRef(null);

  // GSAP animations
  useEffect(() => {
    if (containerRef.current && formRef.current && titleRef.current) {
      const tl = gsap.timeline();
      
      tl.from(titleRef.current, {
        duration: 0.8,
        y: -30,
        opacity: 0,
        ease: 'power2.out'
      })
      .from(formRef.current, {
        duration: 0.8,
        y: 30,
        opacity: 0,
        ease: 'power2.out'
      }, '-=0.4');
    }
  }, [isLogin]);

  const validateForm = () => {
    const newErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = t('emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('emailInvalid');
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = t('passwordRequired');
    } else if (formData.password.length < 6) {
      newErrors.password = t('passwordMinLength');
    }

    // Registration-specific validation
    if (!isLogin) {
      if (!formData.firstName) {
        newErrors.firstName = t('firstNameRequired');
      }
      if (!formData.lastName) {
        newErrors.lastName = t('lastNameRequired');
      }
      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Xác nhận mật khẩu là bắt buộc';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = t('passwordsNotMatch');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // Clear submit error and error code when user starts typing
    if (errors.submit) {
      setErrors(prev => ({ ...prev, submit: '' }));
      setErrorCode(null);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    setErrors({}); // Clear previous errors
    setErrorCode(null); // Clear previous error code
    
    try {
      if (isLogin) {
        console.log('🔍 Attempting login for:', formData.email);
        const result = await login(formData.email, formData.password);
        
        if (result.success) {
          console.log('✅ Login successful, will redirect via AuthContext');
          // AuthContext will handle the redirect to /login-success
        } else {
          console.error('❌ Login failed:', result);
          
          if (result.needsEmailVerification) {
            setVerificationEmail(result.email);
            setShowEmailVerification(true);
          } else {
            // Handle specific error types for better UX
            const errorMessage = result.error || 'Đăng nhập thất bại. Vui lòng thử lại.';
            setErrors({ submit: errorMessage });
            setErrorCode(result.errorCode || null);
            
            // Log error details for debugging
            if (result.errorCode) {
              console.error('Error code:', result.errorCode);
              
              // Provide specific guidance based on error type
              if (result.errorCode === 'auth/invalid-credential') {
                console.log('💡 Suggest: Check email and password, or try password reset');
              } else if (result.errorCode === 'auth/too-many-requests') {
                console.log('💡 Suggest: Wait before trying again');
              }
            }
          }
        }
      } else {
        console.log('🔍 Attempting registration for:', formData.email);
        const result = await register(formData.email, formData.password, {
          name: `${formData.firstName} ${formData.lastName}`.trim(),
          userType: formData.userType,
        });
        
        if (result.success) {
          console.log('✅ Registration successful');
          if (result.needsEmailVerification) {
            setVerificationEmail(result.email);
            setShowEmailVerification(true);
          } else {
            // Navigate based on user type (this should not happen with email verification)
            if (formData.userType === 'freelancer') {
              navigate('/profile-setup');
            } else if (formData.userType === 'client') {
              navigate('/client-setup');
            } else {
              navigate('/dashboard');
            }
          }
        } else {
          console.error('❌ Registration failed:', result);
          
          const errorMessage = result.error || 'Đăng ký thất bại. Vui lòng thử lại.';
          setErrors({ submit: errorMessage });
          setErrorCode(result.errorCode || null);
          
          // Log error details for debugging
          if (result.errorCode) {
            console.error('Error code:', result.errorCode);
            
            // Provide specific guidance based on error type
            if (result.errorCode === 'auth/email-already-in-use') {
              console.log('💡 Suggest: Try logging in instead, or use password reset');
            } else if (result.errorCode === 'auth/weak-password') {
              console.log('💡 Suggest: Use a stronger password');
            }
          }
        }
      }
    } catch (error) {
      console.error('🔍 Form submission error:', error);
      const errorMessage = error.message || 'Đã xảy ra lỗi không mong muốn. Vui lòng thử lại.';
      setErrors({ submit: errorMessage });
      setErrorCode(error.code || null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    try {
      setIsLoading(true);
      setErrors({}); // Clear previous errors
      setGoogleLoginAttempted(true);

      const result = await loginWithGoogle(formData.userType);

      if (result.success) {
        // Google accounts are automatically verified
        if (result.userType === 'freelancer') {
          navigate('/profile-setup');
        } else if (result.userType === 'client') {
          navigate('/client-setup');
        } else {
          navigate('/dashboard');
        }
      } else {
        // Handle different error types
        if (result.userCancelled) {
          // User intentionally closed popup - show gentle message with retry hint
          setErrors({
            submit: 'Bạn đã đóng cửa sổ đăng nhập Google. Nhấn nút "Đăng nhập bằng Google" bên dưới để thử lại.'
          });
        } else if (result.errorCode === 'auth/popup-blocked') {
          // Popup was blocked - show helpful instructions
          setErrors({
            submit: 'Cửa sổ đăng nhập Google bị chặn. Vui lòng cho phép popup trong cài đặt trình duyệt và thử lại.'
          });
        } else {
          // Other errors
          setErrors({ submit: result.error });
        }
        setErrorCode(result.errorCode);
      }
    } catch (error) {
      console.error('Google login error:', error);
      setErrors({ submit: error.message || 'Đã xảy ra lỗi khi đăng nhập bằng Google.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerification = async () => {
    try {
      const result = await sendVerificationEmail();
      if (result.success) {
        // Show success message
      } else {
        setErrors({ submit: result.error });
      }
    } catch (error) {
      setErrors({ submit: 'Không thể gửi lại email xác thực.' });
    }
  };

  const handleForgotPassword = async () => {
    if (!formData.email) {
      setErrors({ email: 'Vui lòng nhập email để đặt lại mật khẩu' });
      return;
    }

    try {
      const result = await resetPassword(formData.email);
      if (result.success) {
        setErrors({ submit: '' });
        // Show success message or redirect
      } else {
        setErrors({ submit: result.error });
      }
    } catch (error) {
      setErrors({ submit: 'Không thể gửi email đặt lại mật khẩu.' });
    }
  };

  const toggleAuthMode = () => {
    setIsLogin(!isLogin);
    setErrors({});
    setErrorCode(null);
    setShowEmailVerification(false);
    setFormData({
      email: '',
      password: '',
      confirmPassword: '',
      firstName: '',
      lastName: '',
      userType: 'freelancer',
    });
  };

  // Email verification UI
  if (showEmailVerification) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-medieval-brown-50 to-medieval-red-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <div className="bg-white rounded-2xl shadow-xl border border-medieval-brown-200 p-8">
            {/* Logo */}
            <div className="text-center mb-8">
              <div className="mx-auto w-16 h-16 bg-medieval-red-600 rounded-full flex items-center justify-center mb-4">
                <span className="text-white font-cinzel-decorative text-xl font-bold">V</span>
              </div>
              <h1 className="font-cinzel-decorative text-xl font-bold text-medieval-brown-800">
                VWork Guild
              </h1>
            </div>

            {/* Email Verification Content */}
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6">
                <EnvelopeIcon className="w-8 h-8 text-blue-600" />
              </div>
              <h2 className="font-cinzel-decorative text-2xl font-bold text-medieval-brown-800 mb-4">
                Xác Thực Email
              </h2>
              <p className="font-cinzel text-medieval-brown-600 mb-6">
                Chúng tôi đã gửi email xác thực đến <span className="font-semibold">{verificationEmail}</span>. 
                Vui lòng kiểm tra hộp thư và nhấp vào link để hoàn tất đăng ký.
              </p>
              
              <div className="bg-medieval-brown-50 border border-medieval-brown-200 rounded-lg p-4 mb-6">
                <div className="flex items-center space-x-2 text-medieval-brown-600">
                  <ExclamationTriangleIcon className="w-5 h-5" />
                  <p className="font-cinzel text-sm">
                    Bạn phải xác thực email trước khi có thể tiếp tục sử dụng VWork
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <button
                  onClick={handleResendVerification}
                  className="w-full btn-medieval-primary"
                >
                  Gửi Lại Email Xác Thực
                </button>
                
                <button
                  onClick={() => navigate('/verify-email')}
                  className="w-full btn-medieval-secondary"
                >
                  Mở Trang Xác Thực
                </button>
                
                <button
                  onClick={toggleAuthMode}
                  className="w-full text-medieval-brown-600 hover:text-medieval-brown-800 font-cinzel text-sm"
                >
                  Quay Lại Đăng Ký
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-medieval-brown-50 to-medieval-red-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full" ref={containerRef}>
        <div className="bg-white rounded-2xl shadow-xl border border-medieval-brown-200 p-8">
          {/* Logo */}
          <div className="text-center mb-8">
            <div className="mx-auto w-16 h-16 bg-medieval-red-600 rounded-full flex items-center justify-center mb-4">
              <span className="text-white font-cinzel-decorative text-xl font-bold">V</span>
            </div>
            <h1 className="font-cinzel-decorative text-xl font-bold text-medieval-brown-800">
              VWork Guild
            </h1>
          </div>

          {/* Title */}
          <div className="text-center mb-8" ref={titleRef}>
            <h2 className="font-cinzel-decorative text-2xl font-bold text-medieval-brown-800 mb-2">
              {isLogin ? t('welcomeBack') : t('createYourAccount')}
            </h2>
            <p className="font-cinzel text-medieval-brown-600">
              {isLogin ? t('signInToAccount') : t('joinWorldsLargest')}
            </p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6" ref={formRef}>
            {/* User Type Selection (Registration only) */}
            {!isLogin && (
              <div>
                <label className="block font-cinzel text-sm font-medium text-medieval-brown-700 mb-3">
                  {t('iWantTo')}
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, userType: 'freelancer' }))}
                    className={`relative p-4 rounded-lg border-2 transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-medieval-red-500 focus:ring-opacity-50 ${
                      formData.userType === 'freelancer'
                        ? 'border-medieval-red-500 bg-blue-50 text-blue-700 shadow-lg scale-105'
                        : 'border-medieval-brown-200 hover:border-medieval-brown-300 hover:shadow-md'
                    }`}
                  >
                    {formData.userType === 'freelancer' && (
                      <div className="absolute top-2 right-2 w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                    )}
                    <UserIcon className={`w-6 h-6 mx-auto mb-2 transition-colors duration-300 ${
                      formData.userType === 'freelancer' ? 'text-blue-600' : 'text-medieval-brown-500'
                    }`} />
                    <span className="font-cinzel text-sm font-medium">{t('findWork')}</span>
                    <div className="mt-1 text-xs font-cinzel text-medieval-brown-500">
                      Freelancer
                    </div>
                  </button>
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, userType: 'client' }))}
                    className={`relative p-4 rounded-lg border-2 transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-medieval-red-500 focus:ring-opacity-50 ${
                      formData.userType === 'client'
                        ? 'border-medieval-red-500 bg-green-50 text-green-700 shadow-lg scale-105'
                        : 'border-medieval-brown-200 hover:border-medieval-brown-300 hover:shadow-md'
                    }`}
                  >
                    {formData.userType === 'client' && (
                      <div className="absolute top-2 right-2 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    )}
                    <BriefcaseIcon className={`w-6 h-6 mx-auto mb-2 transition-colors duration-300 ${
                      formData.userType === 'client' ? 'text-green-600' : 'text-medieval-brown-500'
                    }`} />
                    <span className="font-cinzel text-sm font-medium">{t('hireTalent')}</span>
                    <div className="mt-1 text-xs font-cinzel text-medieval-brown-500">
                      Client
                    </div>
                  </button>
                </div>
                

              </div>
            )}

            {/* Name Fields (Registration only) */}
            {!isLogin && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="firstName"
                    className="block font-cinzel text-sm font-medium text-medieval-brown-700 mb-2"
                  >
                    {t('firstName')}
                  </label>
                  <input
                    id="firstName"
                    name="firstName"
                    type="text"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className={`form-input-medieval w-full px-4 py-3 font-cinzel ${
                      errors.firstName ? 'border-red-500' : ''
                    }`}
                    placeholder={t('firstName')}
                  />
                  {errors.firstName && (
                    <p className="mt-1 text-sm text-red-600 font-cinzel">{errors.firstName}</p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="lastName"
                    className="block font-cinzel text-sm font-medium text-medieval-brown-700 mb-2"
                  >
                    {t('lastName')}
                  </label>
                  <input
                    id="lastName"
                    name="lastName"
                    type="text"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className={`form-input-medieval w-full px-4 py-3 font-cinzel ${
                      errors.lastName ? 'border-red-500' : ''
                    }`}
                    placeholder={t('lastName')}
                  />
                  {errors.lastName && (
                    <p className="mt-1 text-sm text-red-600 font-cinzel">{errors.lastName}</p>
                  )}
                </div>
              </div>
            )}

            {/* Email */}
            <div>
              <label
                htmlFor="email"
                className="block font-cinzel text-sm font-medium text-medieval-brown-700 mb-2"
              >
                {t('emailAddress')}
              </label>
              <div className="relative">
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`form-input-medieval w-full px-4 py-3 pl-12 font-cinzel ${
                    errors.email ? 'border-red-500' : ''
                  }`}
                  placeholder={t('emailAddress')}
                />
                <EnvelopeIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-medieval-brown-400" />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600 font-cinzel">{errors.email}</p>
              )}
            </div>

            {/* Password */}
            <div>
              <label
                htmlFor="password"
                className="block font-cinzel text-sm font-medium text-medieval-brown-700 mb-2"
              >
                {t('password')}
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={handleInputChange}
                  className={`form-input-medieval w-full px-4 py-3 pl-12 pr-12 font-cinzel ${
                    errors.password ? 'border-red-500' : ''
                  }`}
                  placeholder={t('password')}
                />
                <LockClosedIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-medieval-brown-400" />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-medieval-brown-400 hover:text-medieval-brown-600"
                  title={showPassword ? t('hidePassword') : t('showPassword')}
                >
                  {showPassword ? (
                    <EyeSlashIcon className="w-5 h-5" />
                  ) : (
                    <EyeIcon className="w-5 h-5" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600 font-cinzel">{errors.password}</p>
              )}
              {!isLogin && formData.password && (
                <div className="mt-2">
                  <PasswordStrengthIndicator password={formData.password} />
                </div>
              )}
            </div>

            {/* Confirm Password (Registration only) */}
            {!isLogin && (
              <div>
                <label
                  htmlFor="confirmPassword"
                  className="block font-cinzel text-sm font-medium text-medieval-brown-700 mb-2"
                >
                  {t('confirmPassword')}
                </label>
                <div className="relative">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className={`form-input-medieval w-full px-4 py-3 pl-12 font-cinzel ${
                      errors.confirmPassword ? 'border-red-500' : ''
                    }`}
                    placeholder={t('confirmPassword')}
                  />
                  <LockClosedIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-medieval-brown-400" />
                </div>
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-red-600 font-cinzel">{errors.confirmPassword}</p>
                )}
              </div>
            )}

            {/* Submit Error */}
            <AuthErrorDisplay error={errors.submit} errorCode={errorCode} />

            {/* Google Login Helper */}
            <GoogleLoginHelper errorCode={errorCode} attempted={googleLoginAttempted} />

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full btn-medieval-primary group"
            >
              {isLoading ? (
                t('processing')
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <span>{isLogin ? 'Đăng Nhập' : t('createAccount')}</span>
                  <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </div>
              )}
            </button>

            {/* Google Login */}
            <button
              type="button"
              onClick={handleGoogleLogin}
              disabled={isLoading}
              className="w-full btn-medieval-secondary flex items-center justify-center space-x-2"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-medieval-red-600"></div>
                  <span>Đang mở cửa sổ Google...</span>
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  <span>{isLogin ? 'Đăng Nhập' : 'Đăng Ký'} bằng Google</span>
                </>
              )}
            </button>
          </form>

          {/* Footer Links */}
          <div className="mt-8 text-center space-y-4">
            {isLogin && (
              <button
                onClick={handleForgotPassword}
                className="font-cinzel text-sm text-medieval-red-600 hover:text-medieval-red-800"
              >
                {t('forgotPassword')}
              </button>
            )}
            
            <div className="flex items-center justify-center space-x-2 font-cinzel text-sm">
              <span className="text-medieval-brown-600">
                {isLogin ? t('dontHaveAccount') : t('alreadyHaveAccount')}
              </span>
              <button
                onClick={toggleAuthMode}
                className="text-medieval-red-600 hover:text-medieval-red-800 font-medium"
              >
                {isLogin ? 'Đăng Ký' : 'Đăng Nhập'}
              </button>
            </div>
          </div>

          {/* Firebase Status */}
          <div className="mt-8">
            <FirebaseStatus />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppleAuthPage;
