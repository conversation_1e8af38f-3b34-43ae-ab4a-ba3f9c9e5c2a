# 🚀 VWork - Mạng xã hội Freelancer Toàn diện

## ⚡ Cài đặt nhanh

### Windows
```bash
# Chạy script setup tự động
setup-vwork.bat
```

### Linux/macOS
```bash
# Chạy script setup tự động
chmod +x setup-vwork.sh
./setup-vwork.sh
```

### Sử dụng npm scripts
```bash
# Cài đặt tất cả dependencies
npm run install:all

# Setup toàn bộ hệ thống
npm run setup

# Khởi động hệ thống
npm run dev
```

## 🎯 Tính năng chính

### 🏰 Backend (Server)
- **Hybrid Database**: PostgreSQL + MongoDB + Redis
- **Authentication**: Firebase Auth + JWT
- **Real-time**: Socket.IO cho chat và notifications
- **Social Features**: Posts, comments, likes, follows
- **Project Management**: Quests, bids, payments
- **API RESTful**: Đầy đủ endpoints cho mọi tính năng

### 🎨 Frontend (Client)
- **React**: Modern UI với hooks và context
- **Real-time**: Socket.IO integration
- **Social Feed**: Timeline, posts, interactions
- **Project Marketplace**: Browse, apply, manage projects
- **Responsive**: Mobile-first design
- **PWA**: Progressive Web App features

## 🗄️ Kiến trúc Database

### PostgreSQL (Dữ liệu có cấu trúc)
- **Users**: Thông tin người dùng, hồ sơ
- **Projects**: Dự án, công việc, bids
- **Payments**: Thanh toán, giao dịch
- **Relationships**: Follows, connections

### MongoDB (Dữ liệu mạng xã hội)
- **Posts**: Bài đăng, bình luận, reactions
- **Messages**: Tin nhắn real-time
- **Notifications**: Thông báo hệ thống
- **UserSocial**: Dữ liệu mạng xã hội

### Redis (Cache & Session)
- **Session**: Quản lý phiên đăng nhập
- **Cache**: Tăng tốc độ truy vấn
- **Real-time**: Socket.IO session storage

## 🚀 Khởi động hệ thống

### Phương pháp 1: Script tự động
```bash
# Windows
start-vwork.bat

# Linux/macOS
./start-vwork.sh
```

### Phương pháp 2: npm scripts
```bash
# Khởi động cả client và server
npm run dev

# Hoặc khởi động riêng lẻ
npm run dev:server  # Chỉ server
npm run dev:client  # Chỉ client
```

### Phương pháp 3: Thủ công
```bash
# Terminal 1 - Server
cd server && npm run dev

# Terminal 2 - Client
cd client && npm start
```

## 📱 Access URLs

- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:8080
- **API Health**: http://localhost:8080/health
- **API Docs**: http://localhost:8080/api/v1/docs

## 🔧 Cài đặt thủ công

### Yêu cầu hệ thống
- Node.js 16+
- npm 8+
- PostgreSQL 12+
- MongoDB 4.4+
- Redis 6+ (optional)

### Cài đặt databases

#### PostgreSQL
```bash
# Windows
# Download từ: https://www.postgresql.org/download/windows/

# macOS
brew install postgresql@14
brew services start postgresql@14

# Ubuntu
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
```

#### MongoDB
```bash
# Windows
# Download từ: https://www.mongodb.com/try/download/community

# macOS
brew install mongodb-community
brew services start mongodb-community

# Ubuntu
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt update
sudo apt install mongodb-org
sudo systemctl start mongod
```

#### Redis
```bash
# Windows
# Download từ: https://github.com/microsoftarchive/redis/releases

# macOS
brew install redis
brew services start redis

# Ubuntu
sudo apt install redis-server
sudo systemctl start redis-server
```

### Setup thủ công
```bash
# 1. Cài đặt dependencies
npm run install:all

# 2. Setup toàn bộ hệ thống
npm run setup

# 3. Khởi động
npm run dev
```

## 🛠️ Development

### Scripts hữu ích
```bash
# Cài đặt dependencies
npm run install:all

# Setup toàn bộ hệ thống
npm run setup

# Khởi động development
npm run dev

# Build production
npm run build

# Reset database
npm run db:reset

# Seed database
npm run db:seed

# Clean và reinstall
npm run clean:install

# Test
npm run test
```

### Cấu trúc thư mục
```
Vwork/
├── client/                 # React Frontend
│   ├── src/
│   ├── public/
│   └── package.json
├── server/                 # Node.js Backend
│   ├── config/            # Database configs
│   ├── models/            # Database models
│   ├── routes/            # API routes
│   ├── services/          # Business logic
│   └── package.json
├── scripts/               # Setup scripts
├── setup-vwork.bat        # Windows setup
├── setup-vwork.sh         # Linux/macOS setup
└── package.json
```

## 🔍 Troubleshooting

### Lỗi database
```bash
# Kiểm tra kết nối
cd server && npm run dev

# Reset database
npm run db:reset

# Seed lại
npm run db:seed
```

### Lỗi dependencies
```bash
# Clean và reinstall
npm run clean:install
```

### Lỗi port
```bash
# Kiểm tra port đang sử dụng
netstat -ano | findstr :3000
netstat -ano | findstr :8080

# Kill process
taskkill /PID <PID> /F
```

## 📚 Documentation

- **API Docs**: http://localhost:8080/api/v1/docs
- **Database Schema**: `server/models/`
- **Client Components**: `client/src/components/`
- **Setup Guide**: `VWORK_GUIDE.md`

## 🤝 Contributing

1. Fork repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**🎉 Chúc bạn phát triển thành công với VWork!**
