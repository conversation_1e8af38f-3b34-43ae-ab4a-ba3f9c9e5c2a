const Joi = require('joi');

// Validation middleware
const validateBody = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details.map(detail => detail.message)
      });
    }
    next();
  };
};

// Common validation schemas
const schemas = {
  createProject: Joi.object({
    title: Joi.string().min(3).max(100).required(),
    description: Joi.string().min(10).max(2000).required(),
    budget: Joi.number().min(0).required(),
    deadline: Joi.date().iso().required(),
    skills: Joi.array().items(Joi.string()).min(1).required(),
    category: Joi.string().required()
  }),
  
  updateProject: Joi.object({
    title: Joi.string().min(3).max(100),
    description: Joi.string().min(10).max(2000),
    budget: Joi.number().min(0),
    deadline: Joi.date().iso(),
    skills: Joi.array().items(Joi.string()),
    category: Joi.string(),
    status: Joi.string().valid('draft', 'open', 'in-progress', 'completed', 'cancelled')
  })
};

module.exports = {
  validateBody,
  schemas
};
