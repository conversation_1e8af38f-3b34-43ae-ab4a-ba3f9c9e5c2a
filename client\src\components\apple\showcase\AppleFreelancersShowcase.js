import React, { useRef, useEffect, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { SplitText } from 'gsap/SplitText';
import { Physics2DPlugin } from 'gsap/Physics2DPlugin';
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin';
import { CustomEase } from 'gsap/CustomEase';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import { useLanguage } from '../../../contexts/LanguageContext';
import {
  StarIcon,
  MapPinIcon,
  CheckBadgeIcon,
  HeartIcon,
  ChatBubbleLeftIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

// Register GSAP plugins
gsap.registerPlugin(<PERSON><PERSON><PERSON><PERSON>ger, SplitText, Physics2DPlugin, MorphSVGPlugin, CustomEase, MotionPathPlugin);

// Premium easing curves for freelancer showcase animations
const freelancerEases = {
  elastic: CustomEase.create("elastic", "M0,0 C0.25,0 0.4,1.4 0.7,1 C0.85,0.8 1,1 1,1"),
  bounce: CustomEase.create("bounce", "M0,0 C0.14,0 0.242,0.438 0.272,0.561 0.313,0.728 0.354,0.963 0.362,1 0.37,0.985 0.414,0.928 0.455,0.879 0.504,0.822 0.565,0.729 0.621,0.653 0.681,0.573 0.737,0.5 0.785,0.5 0.856,0.5 0.923,0.717 1,1"),
  liquid: CustomEase.create("liquid", "M0,0 C0.29,0.01 0.49,1.53 0.59,1.23 C0.69,0.93 1,1 1,1"),
  magnetic: CustomEase.create("magnetic", "M0,0 C0.5,0 0.5,1 1,1"),
  wave: CustomEase.create("wave", "M0,0 C0.2,0.8 0.8,0.2 1,1"),
  talent: CustomEase.create("talent", "M0,0 C0.3,0 0.7,1 1,1")
};

// Fix unused variables and useEffect dependencies
const AppleFreelancersShowcase = () => {
  const { t } = useLanguage();
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const freelancersRef = useRef(null);
  const particleCanvasRef = useRef(null);
  const morphingShapesRef = useRef([]);
  const freelancerCardRefs = useRef([]);
  const [likedFreelancers, setLikedFreelancers] = useState(new Set());

  const topFreelancers = [
    {
      id: 1,
      name: 'Sarah Chen',
      title: 'Senior UI/UX Designer',
      avatar: 'https://ui-avatars.com/api/?name=Sarah+Chen&size=150&background=F59E0B&color=fff',
      rating: 4.9,
      reviews: 127,
      hourlyRate: 85,
      location: 'San Francisco, CA',
      skills: ['Figma', 'Adobe XD', 'Prototyping', 'User Research'],
      completedProjects: 89,
      responseTime: '1 hour',
      verified: true,
      topRated: true,
      description: 'Passionate designer with 8+ years of experience creating beautiful and functional user interfaces.',
    },
    {
      id: 2,
      name: 'Marcus Johnson',
      title: 'Full Stack Developer',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      rating: 4.8,
      reviews: 203,
      hourlyRate: 95,
      location: 'New York, NY',
      skills: ['React', 'Node.js', 'Python', 'AWS'],
      completedProjects: 156,
      responseTime: '30 minutes',
      verified: true,
      topRated: true,
      description: 'Expert developer specializing in modern web applications and cloud architecture.',
    },
    {
      id: 3,
      name: 'Elena Rodriguez',
      title: 'Digital Marketing Specialist',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      rating: 4.9,
      reviews: 94,
      hourlyRate: 65,
      location: 'Miami, FL',
      skills: ['SEO', 'Google Ads', 'Social Media', 'Analytics'],
      completedProjects: 78,
      responseTime: '2 hours',
      verified: true,
      topRated: false,
      description: 'Results-driven marketer helping businesses grow their online presence and revenue.',
    },
    {
      id: 4,
      name: 'David Kim',
      title: 'Mobile App Developer',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      rating: 4.7,
      reviews: 145,
      hourlyRate: 80,
      location: 'Seattle, WA',
      skills: ['React Native', 'Swift', 'Kotlin', 'Firebase'],
      completedProjects: 112,
      responseTime: '1 hour',
      verified: true,
      topRated: true,
      description: 'Mobile development expert with a track record of delivering high-quality apps.',
    },
    {
      id: 5,
      name: 'Priya Patel',
      title: 'Data Scientist',
      avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
      rating: 4.8,
      reviews: 76,
      hourlyRate: 110,
      location: 'Austin, TX',
      skills: ['Python', 'Machine Learning', 'TensorFlow', 'SQL'],
      completedProjects: 45,
      responseTime: '3 hours',
      verified: true,
      topRated: false,
      description: 'Data scientist with expertise in machine learning and predictive analytics.',
    },
    {
      id: 6,
      name: 'Alex Thompson',
      title: 'Content Writer',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
      rating: 4.9,
      reviews: 189,
      hourlyRate: 45,
      location: 'London, UK',
      skills: ['Copywriting', 'SEO Writing', 'Blog Posts', 'Technical Writing'],
      completedProjects: 234,
      responseTime: '4 hours',
      verified: true,
      topRated: true,
      description: 'Professional writer creating engaging content that drives results for businesses.',
    },
  ];

  // Advanced Floating Talent Particles
  const createAdvancedTalentParticles = () => {
    if (!particleCanvasRef.current) return;

    const container = particleCanvasRef.current;
    const particles = [];

    // Create 100 professional talent particles
    for (let i = 0; i < 100; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-1 h-1 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full opacity-50';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.top = Math.random() * 100 + '%';
      container.appendChild(particle);
      particles.push(particle);

      // Create professional networking paths
      const pathData = `M${Math.random() * 100},${Math.random() * 100} Q${Math.random() * 100},${Math.random() * 100} ${Math.random() * 100},${Math.random() * 100}`;
      
      gsap.to(particle, {
        motionPath: {
          path: pathData,
          autoRotate: true
        },
        duration: Math.random() * 12 + 8,
        repeat: -1,
        ease: freelancerEases.talent,
        delay: Math.random() * 4
      });

      // Professional talent pulse
      gsap.to(particle, {
        scale: "random(0.5, 3)",
        opacity: "random(0.3, 0.9)",
        duration: "random(3, 6)",
        repeat: -1,
        yoyo: true,
        ease: freelancerEases.wave
      });
    }
  };

  // Morphing Professional Backgrounds
  const createMorphingTalentShapes = () => {
    morphingShapesRef.current.forEach((shape, index) => {
      if (!shape) return;

      const colors = [
        'from-purple-400/15 to-blue-400/15',
        'from-blue-400/15 to-indigo-400/15',
        'from-indigo-400/15 to-purple-400/15',
        'from-purple-400/15 to-pink-400/15',
        'from-pink-400/15 to-red-400/15',
        'from-red-400/15 to-orange-400/15'
      ];

      shape.className = `absolute bg-gradient-to-br ${colors[index % colors.length]} rounded-full`;

      const morphTimeline = gsap.timeline({ repeat: -1, yoyo: true });
      
      morphTimeline
        .to(shape, {
          borderRadius: "50% 60% 40% 70% / 60% 40% 70% 50%",
          scale: 1.5,
          rotation: 360,
          x: "random(-50, 50)",
          y: "random(-40, 40)",
          duration: 7,
          ease: freelancerEases.liquid
        })
        .to(shape, {
          borderRadius: "40% 50% 70% 60% / 50% 70% 40% 60%",
          scale: 0.9,
          rotation: -180,
          x: "random(-40, 40)",
          y: "random(-50, 50)",
          duration: 5,
          ease: freelancerEases.elastic
        })
        .to(shape, {
          borderRadius: "70% 40% 50% 60% / 40% 60% 50% 70%",
          scale: 1.3,
          rotation: 270,
          x: "random(-45, 45)",
          y: "random(-45, 45)",
          duration: 6,
          ease: freelancerEases.wave
        });

      morphTimeline.delay(index * 1.5);
    });
  };

  // Advanced Title Animation with Professional Theme
  const createAdvancedTitleAnimation = () => {
    if (!titleRef.current) return;

    const titleElement = titleRef.current.querySelector('h2');
    const subtitleElement = titleRef.current.querySelector('p');

    if (titleElement) {
      const titleSplit = new SplitText(titleElement, { type: "chars,words" });

      gsap.fromTo(titleSplit.chars, {
        opacity: 0,
        y: 120,
        rotationX: -90,
        transformOrigin: "center bottom"
      }, {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration: 2.2,
        stagger: 0.03,
        ease: freelancerEases.bounce,
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      });

      // Add professional excellence glow
      gsap.to(titleElement, {
        textShadow: "0 0 40px rgba(139, 92, 246, 0.6)",
        duration: 4,
        repeat: -1,
        yoyo: true,
        ease: freelancerEases.wave
      });
    }

    if (subtitleElement) {
      const subtitleSplit = new SplitText(subtitleElement, { type: "words" });

      gsap.fromTo(subtitleSplit.words, {
        opacity: 0,
        y: 70,
        scale: 0.6
      }, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1.6,
        stagger: 0.15,
        ease: freelancerEases.elastic,
        delay: 1.2,
          scrollTrigger: {
            trigger: titleRef.current,
            start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      });
    }
  };

  // Advanced Magnetic Freelancer Card Hover
  const createMagneticFreelancerHover = (freelancerElement, index) => {
    if (!freelancerElement) return;

    const avatar = freelancerElement.querySelector('.freelancer-avatar');
    const content = freelancerElement.querySelector('.freelancer-content');
    const buttons = freelancerElement.querySelector('.freelancer-actions');
    const badges = freelancerElement.querySelector('.freelancer-badges');

    let isHovering = false;

    freelancerElement.addEventListener('mouseenter', () => {
      isHovering = true;

      // Magnetic freelancer hover timeline
      const hoverTL = gsap.timeline();

      hoverTL
        .to(freelancerElement, {
          scale: 1.08,
          y: -25,
          rotationY: 8,
          rotationX: 4,
          boxShadow: "0 50px 100px rgba(0,0,0,0.25)",
          duration: 0.9,
          ease: freelancerEases.magnetic
        })
        .to(avatar, {
          scale: 1.15,
          rotation: 5,
          duration: 0.8,
          ease: freelancerEases.bounce
        }, 0)
        .to(content, {
          y: -5,
          duration: 0.6,
          ease: freelancerEases.elastic
        }, 0.2)
        .to(badges, {
          scale: 1.1,
          duration: 0.7,
          ease: freelancerEases.wave
        }, 0.1)
        .to(buttons, {
          y: -10,
          scale: 1.05,
          duration: 0.6,
          ease: freelancerEases.bounce
        }, 0.3);

      // Create professional excellence particles
      createFreelancerExcellenceParticles(freelancerElement, index);
    });

    freelancerElement.addEventListener('mouseleave', () => {
      isHovering = false;

      gsap.to(freelancerElement, {
        scale: 1,
        y: 0,
        rotationY: 0,
        rotationX: 0,
        boxShadow: "0 20px 40px rgba(0,0,0,0.12)",
        duration: 1.1,
        ease: freelancerEases.elastic
      });

      gsap.to(avatar, {
        scale: 1,
        rotation: 0,
        duration: 0.8,
        ease: freelancerEases.bounce
      });

      gsap.to(content, {
        y: 0,
        duration: 0.6,
        ease: freelancerEases.wave
      });

      gsap.to(badges, {
        scale: 1,
        duration: 0.5,
        ease: freelancerEases.elastic
      });

      gsap.to(buttons, {
        y: 0,
        scale: 1,
        duration: 0.6,
        ease: freelancerEases.bounce
      });
    });

    // Real-time mouse tracking for professional interaction
    freelancerElement.addEventListener('mousemove', (e) => {
      if (!isHovering) return;

      const rect = freelancerElement.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      const mouseX = e.clientX - centerX;
      const mouseY = e.clientY - centerY;

      gsap.to(freelancerElement, {
        x: mouseX * 0.05,
        y: mouseY * 0.05,
        duration: 0.3,
        ease: "power2.out"
      });

      gsap.to(avatar, {
        x: mouseX * 0.08,
        y: mouseY * 0.08,
        duration: 0.2,
        ease: "power2.out"
      });

      gsap.to(content, {
        x: mouseX * 0.03,
        y: mouseY * 0.03,
        duration: 0.4,
        ease: "power2.out"
      });
    });
  };

  // Create Freelancer Excellence Particles
  const createFreelancerExcellenceParticles = (element, index) => {
    const colors = ['#8B5CF6', '#3B82F6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444'];
    const color = colors[index % colors.length];

    for (let i = 0; i < 18; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute pointer-events-none w-2 h-2 rounded-full';
      particle.style.background = color;
      particle.style.opacity = '0';
      element.appendChild(particle);

      const angle = (i / 18) * Math.PI * 2;
      const distance = 90 + Math.random() * 60;

      gsap.set(particle, {
        x: 0,
        y: 0,
        scale: 0
      });

      gsap.to(particle, {
        x: Math.cos(angle) * distance,
        y: Math.sin(angle) * distance,
        scale: 2.2,
        opacity: 0.9,
        duration: 1.4,
        ease: freelancerEases.elastic
      });

      gsap.to(particle, {
        opacity: 0,
        scale: 0,
        duration: 1.2,
        delay: 1.4,
        ease: "power2.in",
        onComplete: () => {
          if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
          }
        }
      });
    }
  };

  // Advanced Freelancer Grid Animation
  const createAdvancedFreelancerGrid = () => {
    if (!freelancersRef.current) return;

    const freelancerElements = Array.from(freelancersRef.current.children);

    freelancerElements.forEach((freelancer, index) => {
      // Store reference for hover effects
      freelancerCardRefs.current[index] = freelancer;

      // Create magnetic hover effect
      createMagneticFreelancerHover(freelancer, index);

      // Professional showcase entrance animation
      gsap.fromTo(freelancer, {
        opacity: 0,
        y: 120,
        scale: 0.7,
        rotationY: 45,
        rotationX: 25
      }, {
        opacity: 1,
        y: 0,
        scale: 1,
        rotationY: 0,
        rotationX: 0,
        duration: 2.0,
        delay: index * 0.15,
        ease: freelancerEases.elastic,
        scrollTrigger: {
          trigger: freelancer,
          start: 'top 90%',
          toggleActions: 'play none none reverse'
        }
      });

      // Animate freelancer components
      const avatar = freelancer.querySelector('.freelancer-avatar');
      const content = freelancer.querySelector('.freelancer-content');
      const stats = freelancer.querySelector('.freelancer-stats');

      if (avatar) {
        gsap.fromTo(avatar, {
          scale: 0,
          rotation: -270
        }, {
          scale: 1,
          rotation: 0,
          duration: 1.2,
          delay: index * 0.15 + 0.4,
          ease: freelancerEases.bounce,
          scrollTrigger: {
            trigger: freelancer,
            start: 'top 90%',
            toggleActions: 'play none none reverse'
          }
        });
      }

      if (content) {
        const contentChildren = Array.from(content.children);
        gsap.fromTo(contentChildren, {
          opacity: 0,
          x: -60
        }, {
          opacity: 1,
          x: 0,
          duration: 1.0,
          stagger: 0.12,
          delay: index * 0.15 + 0.8,
          ease: freelancerEases.wave,
          scrollTrigger: {
            trigger: freelancer,
            start: 'top 90%',
            toggleActions: 'play none none reverse'
          }
        });
      }

      if (stats) {
        const statItems = Array.from(stats.children);
        gsap.fromTo(statItems, {
          opacity: 0,
          y: 40,
          scale: 0.8
        }, {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8,
          stagger: 0.1,
          delay: index * 0.15 + 1.2,
          ease: freelancerEases.bounce,
          scrollTrigger: {
            trigger: freelancer,
            start: 'top 90%',
            toggleActions: 'play none none reverse'
          }
        });
      }
    });
  };

  const toggleLikeFreelancer = (freelancerId) => {
    const newLikedFreelancers = new Set(likedFreelancers);
    if (newLikedFreelancers.has(freelancerId)) {
      newLikedFreelancers.delete(freelancerId);
    } else {
      newLikedFreelancers.add(freelancerId);
    }
    setLikedFreelancers(newLikedFreelancers);
  };

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Initialize all advanced animations
      createAdvancedTalentParticles();
      createMorphingTalentShapes();
      createAdvancedTitleAnimation();
      createAdvancedFreelancerGrid();
    }, sectionRef);

    return () => ctx.revert();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {/* Enhanced CSS */}
      <style>{`
        @keyframes liquid-talent {
          0%, 100% {
            border-radius: 50% 60% 40% 70% / 60% 40% 70% 50%;
          }
          25% {
            border-radius: 40% 50% 70% 60% / 50% 70% 40% 60%;
          }
          50% {
            border-radius: 70% 40% 50% 60% / 40% 60% 50% 70%;
          }
          75% {
            border-radius: 60% 70% 40% 50% / 70% 50% 60% 40%;
          }
        }
        
        .liquid-talent {
          animation: liquid-talent 18s ease-in-out infinite;
        }
        
        .freelancer-card {
          transform-style: preserve-3d;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .talent-particles {
          will-change: transform;
        }
        
        .freelancer-card:hover {
          transform: perspective(1000px) rotateX(4deg) rotateY(8deg) translateZ(50px);
        }
      `}</style>

    <section
      ref={sectionRef}
        className='relative py-20 bg-white dark:bg-gray-900 transition-colors duration-300 overflow-hidden'
      >
        {/* Advanced Multi-layer Background */}
        <div className='absolute inset-0'>
          <div 
            ref={el => morphingShapesRef.current[0] = el}
            className='absolute top-24 left-20 w-56 h-56 liquid-talent'
          />
          <div 
            ref={el => morphingShapesRef.current[1] = el}
            className='absolute top-48 right-24 w-48 h-48 liquid-talent'
          />
          <div 
            ref={el => morphingShapesRef.current[2] = el}
            className='absolute bottom-28 left-1/4 w-64 h-64 liquid-talent'
          />
          <div 
            ref={el => morphingShapesRef.current[3] = el}
            className='absolute bottom-24 right-20 w-52 h-52 liquid-talent'
          />
          <div 
            ref={el => morphingShapesRef.current[4] = el}
            className='absolute top-1/3 left-1/2 w-40 h-40 liquid-talent'
          />
          <div 
            ref={el => morphingShapesRef.current[5] = el}
            className='absolute top-24 left-1/3 w-44 h-44 liquid-talent'
          />
        </div>

        {/* Advanced Talent Particle Canvas */}
        <div 
          ref={particleCanvasRef}
          className='absolute inset-0 pointer-events-none overflow-hidden talent-particles'
        />

        <div className='relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          {/* Enhanced Section Header */}
        <div ref={titleRef} className='text-center mb-16'>
            <h2 className='text-4xl sm:text-5xl lg:text-6xl font-black text-gray-900 dark:text-gray-100 mb-8 transition-colors duration-300'>
            {t('topFreelancers')}
          </h2>
            <p className='text-xl sm:text-2xl lg:text-3xl text-gray-600 dark:text-gray-400 max-w-4xl mx-auto leading-relaxed transition-colors duration-300'>
            {t('workWithBestTalent')}
          </p>
        </div>

          {/* Enhanced Freelancers Grid */}
        <div
          ref={freelancersRef}
            className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 lg:gap-12'
        >
            {topFreelancers.map((freelancer, index) => (
            <div
              key={freelancer.id}
                className='group freelancer-card bg-white dark:bg-gray-800 rounded-3xl p-8 border border-gray-100 dark:border-gray-700 transition-all duration-500 hover:shadow-2xl hover:border-gray-200 dark:hover:border-gray-600 transform-gpu'
            >
                {/* Enhanced Header */}
                <div className='flex items-start justify-between mb-6'>
                <div className='flex items-center'>
                    <div className='freelancer-avatar relative'>
                    <img
                      src={freelancer.avatar}
                      alt={freelancer.name}
                        className='w-20 h-20 rounded-2xl object-cover border-4 border-gray-100 dark:border-gray-600 shadow-lg'
                    />
                    {freelancer.verified && (
                        <CheckBadgeIcon className='absolute -bottom-2 -right-2 h-8 w-8 text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800 rounded-full shadow-lg' />
                    )}
                  </div>
                    <div className='ml-5'>
                      <h3 className='text-2xl font-bold text-gray-900 dark:text-gray-100 transition-colors duration-300'>
                      {freelancer.name}
                    </h3>
                      <p className='text-lg text-gray-600 dark:text-gray-400 transition-colors duration-300'>
                      {freelancer.title}
                    </p>
                  </div>
                </div>
                  
                <button
                  onClick={() => toggleLikeFreelancer(freelancer.id)}
                    className='p-3 rounded-2xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200'
                >
                  {likedFreelancers.has(freelancer.id) ? (
                      <HeartSolidIcon className='h-6 w-6 text-red-500' />
                  ) : (
                      <HeartIcon className='h-6 w-6 text-gray-400 dark:text-gray-500' />
                  )}
                </button>
              </div>

                {/* Enhanced Badges */}
                <div className='freelancer-badges flex flex-wrap gap-3 mb-6'>
                {freelancer.topRated && (
                    <span className='px-3 py-1 text-sm font-bold text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20 rounded-full'>
                    {t('topRated')}
                  </span>
                )}
                  <span className='px-3 py-1 text-sm font-bold text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 rounded-full'>
                  {t('available')}
                </span>
              </div>

                {/* Enhanced Rating and Stats */}
                <div className='freelancer-content space-y-4 mb-6'>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center'>
                      <StarIcon className='h-5 w-5 text-yellow-500 mr-2' />
                      <span className='text-lg font-bold text-gray-900 dark:text-gray-100'>
                      {freelancer.rating}
                    </span>
                      <span className='text-gray-600 dark:text-gray-400 ml-2'>
                      ({freelancer.reviews} {t('reviews')})
                    </span>
                  </div>
                    <div className='text-lg font-bold text-gray-900 dark:text-gray-100'>
                    ${freelancer.hourlyRate}/{t('hour')}
                  </div>
                </div>

                  <div className='flex items-center text-gray-600 dark:text-gray-400'>
                    <MapPinIcon className='h-5 w-5 mr-2' />
                    <span className='font-medium'>{freelancer.location}</span>
              </div>

                  {/* Enhanced Description */}
                  <p className='text-gray-600 dark:text-gray-400 leading-relaxed transition-colors duration-300'>
                {freelancer.description}
              </p>

                  {/* Enhanced Skills */}
                  <div className='flex flex-wrap gap-2'>
                {freelancer.skills.slice(0, 3).map((skill) => (
                  <span
                    key={skill}
                        className='px-3 py-1 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 rounded-full'
                  >
                    {skill}
                  </span>
                ))}
                {freelancer.skills.length > 3 && (
                      <span className='px-3 py-1 text-sm font-medium text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-full'>
                    +{freelancer.skills.length - 3}
                  </span>
                )}
                  </div>
              </div>

                {/* Enhanced Stats */}
                <div className='freelancer-stats grid grid-cols-2 gap-6 mb-6 text-center'>
                  <div>
                    <div className='text-2xl font-bold text-gray-900 dark:text-gray-100'>
                    {freelancer.completedProjects}
                    </div>
                    <div className='text-sm text-gray-600 dark:text-gray-400'>{t('projects')}</div>
                  </div>
                  <div>
                    <div className='text-2xl font-bold text-gray-900 dark:text-gray-100'>
                      {freelancer.responseTime}
                </div>
                    <div className='text-sm text-gray-600 dark:text-gray-400'>{t('response')}</div>
                </div>
              </div>

                {/* Enhanced Action Buttons */}
                <div className='freelancer-actions flex gap-3'>
                  <button className='flex-1 px-6 py-3 text-lg font-bold text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform group-hover:scale-105'>
                  {t('hire')}
                </button>
                  <button className='px-4 py-3 text-lg font-bold text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-2xl transition-colors duration-200 flex items-center'>
                    <ChatBubbleLeftIcon className='h-5 w-5 mr-2' />
                  {t('message')}
                </button>
                  <button className='px-4 py-3 text-lg font-bold text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-2xl transition-colors duration-200'>
                    <EyeIcon className='h-5 w-5' />
                </button>
              </div>
            </div>
          ))}
        </div>

          {/* Enhanced View All Button */}
          <div className='text-center mt-16'>
            <button className='group px-10 py-5 text-xl font-bold text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105'>
              <span className='mr-3'>{t('viewAllFreelancers')}</span>
              <span className='inline-block transition-transform duration-300 group-hover:translate-x-2'>→</span>
          </button>
        </div>
      </div>
    </section>
    </>
  );
};

export default AppleFreelancersShowcase;
