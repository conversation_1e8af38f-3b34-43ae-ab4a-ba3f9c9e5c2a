#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Store all spawned processes for proper cleanup
let spawnedProcesses = [];
const pidsFile = path.join(__dirname, '..', '.vwork-pids.json');

// Cleanup function
function cleanup() {
  console.log('\n🧹 Cleaning up processes...');
  
  // Kill all spawned processes
  spawnedProcesses.forEach((proc, index) => {
    try {
      if (proc && proc.pid && !proc.killed) {
        console.log(`Stopping process ${index + 1} (PID: ${proc.pid})`);
        if (os.platform() === 'win32') {
          exec(`taskkill /F /T /PID ${proc.pid}`, () => {});
        } else {
          process.kill(-proc.pid); // Kill process group
        }
      }
    } catch (error) {
      console.log(`Could not kill process ${index + 1}:`, error.message);
    }
  });

  // Save PIDs to file for stop script
  const allPids = spawnedProcesses.map(proc => proc.pid).filter(pid => pid);
  try {
    fs.writeFileSync(pidsFile, JSON.stringify(allPids, null, 2));
    console.log('💾 Saved process PIDs for cleanup');
  } catch (error) {
    console.log('⚠️  Could not save PID file:', error.message);
  }
  
  setTimeout(() => {
    process.exit(0);
  }, 1000);
}

// Handle process termination signals
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);
process.on('exit', cleanup);

// Load environment variables
const envPath = path.join(__dirname, '..', '.env');
if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
  console.log('✅ Environment variables loaded from .env');
} else {
  console.log('⚠️  No .env file found in root directory');
}

// Kill processes running on specific ports with better error handling
async function killProcessesOnPorts(ports = [3000, 3001, 3002, 3003, 3004, 3005, 8080]) {
  console.log('🔍 Checking for processes on ports:', ports.join(', '));
  
  const isWindows = os.platform() === 'win32';
  
  for (const port of ports) {
    try {
      console.log(`   Checking port ${port}...`);
      
      let command;
      if (isWindows) {
        command = `netstat -ano | findstr :${port}`;
      } else {
        command = `lsof -ti:${port}`;
      }
      
      const result = await new Promise((resolve) => {
        exec(command, { timeout: 5000 }, (error, stdout, stderr) => {
          if (error) {
            resolve(null);
          } else {
            resolve(stdout.trim());
          }
        });
      });
      
      if (result && result.length > 0) {
        console.log(`⚠️  Found process on port ${port}, killing...`);
        
        if (isWindows) {
          const lines = result.split('\n');
          const pidsToKill = new Set();
          
          for (const line of lines) {
            const parts = line.trim().split(/\s+/);
            if (parts.length > 4) {
              const pid = parts[parts.length - 1];
              if (pid && !isNaN(pid) && pid !== '0') {
                pidsToKill.add(pid);
              }
            }
          }
          
          for (const pid of pidsToKill) {
            console.log(`     Killing PID ${pid}...`);
            await new Promise((resolve) => {
              exec(`taskkill /F /T /PID ${pid}`, { timeout: 3000 }, () => resolve());
            });
          }
        } else {
          const pids = result.split('\n').filter(pid => pid && !isNaN(pid));
          for (const pid of pids) {
            console.log(`     Killing PID ${pid}...`);
            await new Promise((resolve) => {
              exec(`kill -9 ${pid}`, { timeout: 3000 }, () => resolve());
            });
          }
        }
        
        // Wait a bit for processes to die
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log(`✅ Cleaned port ${port}`);
      } else {
        console.log(`✅ Port ${port} is free`);
      }
    } catch (error) {
      console.log(`⚠️  Error checking port ${port}: ${error.message}`);
    }
  }
  
  // Extra cleanup step - use kill-port as backup
  console.log('🧹 Running additional port cleanup...');
  for (const port of ports) {
    try {
      await new Promise((resolve) => {
        exec(`npx kill-port ${port}`, { timeout: 5000 }, () => resolve());
      });
    } catch (error) {
      // Ignore errors from kill-port
    }
  }
}

// Check and create environment files
async function setupEnvironmentFiles() {
  const rootEnvPath = path.join(__dirname, '..', '.env');
  const clientDir = path.join(__dirname, '..', 'client');
  const microservicesDir = path.join(__dirname, '..', 'microservices');
  
  // Create client .env file
  const clientEnvPath = path.join(clientDir, '.env');
  let clientEnvContent = `# Client Environment Configuration
PORT=3000
REACT_APP_API_URL=http://localhost:8080
NODE_ENV=development
`;

  if (fs.existsSync(rootEnvPath)) {
    const rootEnvContent = fs.readFileSync(rootEnvPath, 'utf8');
    const clientEnvLines = rootEnvContent.split('\n')
      .filter(line => {
        const trimmed = line.trim();
        return trimmed.startsWith('REACT_APP_') || 
               trimmed.startsWith('NODE_ENV') ||
               trimmed.startsWith('#') ||
               trimmed === '';
      });
    clientEnvContent += '\n' + clientEnvLines.join('\n');
  }

  fs.writeFileSync(clientEnvPath, clientEnvContent);
  console.log('✅ Client .env file created');

  // Create microservices .env files
  const services = [
    { name: 'auth-service', port: 3001 },
    { name: 'user-service', port: 3002 },
    { name: 'project-service', port: 3003 },
    { name: 'payment-service', port: 3004 },
    { name: 'chat-service', port: 3005 },
    { name: 'api-gateway', port: 8080 }
  ];

  for (const service of services) {
    const serviceDir = path.join(microservicesDir, service.name);
    const serviceEnvPath = path.join(serviceDir, '.env');
    
    if (fs.existsSync(serviceDir)) {
      let serviceEnvContent = `NODE_ENV=development
PORT=${service.port}
CORS_ORIGIN=http://localhost:3000
`;
      
      if (fs.existsSync(rootEnvPath)) {
        const rootEnvContent = fs.readFileSync(rootEnvPath, 'utf8');
        serviceEnvContent += '\n' + rootEnvContent;
      }
      
      fs.writeFileSync(serviceEnvPath, serviceEnvContent);
      console.log(`✅ ${service.name} .env file created`);
    }
  }
}

// Check dependencies
async function checkDependencies() {
  console.log('📦 Checking dependencies...');
  
  const directories = [
    { name: 'client', path: path.join(__dirname, '..', 'client') },
    { name: 'auth-service', path: path.join(__dirname, '..', 'microservices', 'auth-service') },
    { name: 'user-service', path: path.join(__dirname, '..', 'microservices', 'user-service') },
    { name: 'project-service', path: path.join(__dirname, '..', 'microservices', 'project-service') },
    { name: 'payment-service', path: path.join(__dirname, '..', 'microservices', 'payment-service') },
    { name: 'chat-service', path: path.join(__dirname, '..', 'microservices', 'chat-service') },
    { name: 'api-gateway', path: path.join(__dirname, '..', 'microservices', 'api-gateway') }
  ];

  for (const dir of directories) {
    if (fs.existsSync(dir.path) && !fs.existsSync(path.join(dir.path, 'node_modules'))) {
      console.log(`📦 Installing ${dir.name} dependencies...`);
      await runCommand('npm install', dir.path);
    }
  }
}

// Run command utility
function runCommand(command, cwd) {
  return new Promise((resolve, reject) => {
    const childProcess = exec(command, { cwd }, (error) => {
      if (error) {
        console.error(`Error in ${cwd}: ${error.message}`);
        reject(error);
      } else {
        resolve();
      }
    });
    
    childProcess.stdout.on('data', (data) => {
      process.stdout.write(data);
    });
    
    childProcess.stderr.on('data', (data) => {
      process.stderr.write(data);
    });
  });
}

// Kill processes by specific port
async function killProcessesByPort(port) {
  const isWindows = os.platform() === 'win32';
  
  try {
    let command;
    if (isWindows) {
      command = `netstat -ano | findstr :${port}`;
    } else {
      command = `lsof -ti:${port}`;
    }
    
    const result = await new Promise((resolve) => {
      exec(command, { timeout: 3000 }, (error, stdout, stderr) => {
        if (error) {
          resolve(null);
        } else {
          resolve(stdout.trim());
        }
      });
    });
    
    if (result && result.length > 0) {
      console.log(`     Killing processes on port ${port}...`);
      
      if (isWindows) {
        const lines = result.split('\n');
        const pidsToKill = new Set();
        
        for (const line of lines) {
          const parts = line.trim().split(/\s+/);
          if (parts.length > 4) {
            const pid = parts[parts.length - 1];
            if (pid && !isNaN(pid) && pid !== '0') {
              pidsToKill.add(pid);
            }
          }
        }
        
        for (const pid of pidsToKill) {
          await new Promise((resolve) => {
            exec(`taskkill /F /T /PID ${pid}`, { timeout: 3000 }, () => resolve());
          });
        }
      } else {
        const pids = result.split('\n').filter(pid => pid && !isNaN(pid));
        for (const pid of pids) {
          await new Promise((resolve) => {
            exec(`kill -9 ${pid}`, { timeout: 3000 }, () => resolve());
          });
        }
      }
    }
  } catch (error) {
    console.log(`     Error killing processes on port ${port}: ${error.message}`);
  }
}

// Check if port is available
async function isPortAvailable(port) {
  return new Promise((resolve) => {
    const isWindows = os.platform() === 'win32';
    const command = isWindows 
      ? `netstat -ano | findstr :${port}`
      : `lsof -ti:${port}`;
    
    exec(command, { timeout: 3000 }, (error, stdout) => {
      if (error || !stdout.trim()) {
        resolve(true); // Port is available
      } else {
        resolve(false); // Port is in use
      }
    });
  });
}

// Start service in new terminal window with port verification
function startServiceInWindow(serviceName, servicePath, command, port) {
  const isWindows = os.platform() === 'win32';
  
  if (isWindows) {
    const windowTitle = `VWork - ${serviceName} (Port ${port})`;
    
    // Add port verification to the command
    const safeCommand = `echo Starting ${serviceName} on port ${port}... && ` +
                       `netstat -ano | findstr :${port} && echo Port ${port} is busy! && pause || ` +
                       `(echo Port ${port} is free, starting service... && ${command})`;
    
    const startCommand = `start "${windowTitle}" cmd /k "${safeCommand}"`;
    
    console.log(`🚀 Starting ${serviceName} in new window (Port ${port})...`);
    
    const childProcess = spawn('cmd', ['/c', startCommand], {
      detached: true,
      stdio: ['ignore', 'pipe', 'pipe'],
      shell: true
    });
    
    spawnedProcesses.push(childProcess);
    
    // Log process PID
    console.log(`   └─ PID: ${childProcess.pid}`);
    
  } else {
    // For Unix-like systems, start in background
    console.log(`🚀 Starting ${serviceName} (Port ${port})...`);
    
    const childProcess = spawn('bash', ['-c', `cd "${servicePath}" && ${command}`], {
      detached: true,
      stdio: ['ignore', 'pipe', 'pipe']
    });
    
    spawnedProcesses.push(childProcess);
    
    // Handle output
    childProcess.stdout.on('data', (data) => {
      console.log(`[${serviceName}] ${data.toString()}`);
    });
    
    childProcess.stderr.on('data', (data) => {
      console.log(`[${serviceName} ERROR] ${data.toString()}`);
    });
    
    console.log(`   └─ PID: ${childProcess.pid}`);
  }
}

// Main function
async function startAll() {
  try {
    console.log('🚀 Starting VWork Full Stack Application...\n');
    
    // Clean up existing processes
    await killProcessesOnPorts([3000, 3001, 3002, 3003, 3004, 3005, 8080]);
    
    // Setup environment files
    await setupEnvironmentFiles();
    
    // Check dependencies
    await checkDependencies();
    
    console.log('\n🎉 Starting all services in separate windows...\n');
    
    const projectRoot = path.join(__dirname, '..');
    
    // Define services to start - start backend services first, then frontend
    const services = [
      { name: 'API Gateway', path: path.join(projectRoot, 'microservices', 'api-gateway'), command: 'npm start', port: 8080 },
      { name: 'Auth Service', path: path.join(projectRoot, 'microservices', 'auth-service'), command: 'npm start', port: 3001 },
      { name: 'User Service', path: path.join(projectRoot, 'microservices', 'user-service'), command: 'npm start', port: 3002 },
      { name: 'Project Service', path: path.join(projectRoot, 'microservices', 'project-service'), command: 'npm start', port: 3003 },
      { name: 'Payment Service', path: path.join(projectRoot, 'microservices', 'payment-service'), command: 'npm start', port: 3004 },
      { name: 'Chat Service', path: path.join(projectRoot, 'microservices', 'chat-service'), command: 'npm start', port: 3005 },
      { name: 'Client', path: path.join(projectRoot, 'client'), command: 'npm start', port: 3000 }
    ];
    
    // Start each service with longer delays
    for (const service of services) {
      if (fs.existsSync(service.path)) {
        // Double-check port availability before starting
        const portAvailable = await isPortAvailable(service.port);
        if (!portAvailable) {
          console.log(`⚠️  Port ${service.port} is still in use, trying to kill...`);
          await killProcessesByPort(service.port);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        startServiceInWindow(service.name, service.path, service.command, service.port);
        
        // Longer delay between starts, especially for API Gateway
        if (service.name === 'API Gateway') {
          console.log('   Waiting for API Gateway to initialize...');
          await new Promise(resolve => setTimeout(resolve, 3000));
        } else if (service.name === 'Client') {
          console.log('   Waiting before starting client...');
          await new Promise(resolve => setTimeout(resolve, 2000));
        } else {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      } else {
        console.log(`⚠️  Service directory not found: ${service.path}`);
      }
    }
    
    // Save all PIDs to file
    const allPids = spawnedProcesses.map(proc => proc.pid).filter(pid => pid);
    fs.writeFileSync(pidsFile, JSON.stringify(allPids, null, 2));
    
    console.log('\n✅ All services started successfully!');
    console.log('📋 Service URLs:');
    services.forEach(service => {
      if (service.name === 'Client') {
        console.log(`   - ${service.name}: http://localhost:${service.port}`);
      } else {
        console.log(`   - ${service.name}: http://localhost:${service.port}`);
      }
    });
    
    console.log('\n💡 Commands:');
    console.log('   - Stop all services: npm stop');
    console.log('   - View logs: Check individual terminal windows');
    console.log('\n🔄 Press Ctrl+C to stop all services from this terminal');
    
    // Keep process alive
    process.stdin.resume();
    
  } catch (error) {
    console.error('❌ Error starting application:', error.message);
    cleanup();
    process.exit(1);
  }
}

// Start the application
startAll();