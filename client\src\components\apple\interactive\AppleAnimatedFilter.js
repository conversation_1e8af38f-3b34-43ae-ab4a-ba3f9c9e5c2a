import { useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { animationController } from '../../../utils/animations';
import {
  FunnelIcon,
  ChevronDownIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
} from '@heroicons/react/24/outline';

const AppleAnimatedFilter = ({
  filters = [],
  activeFilters = [],
  onFilterChange,
  onSearch,
  searchQuery = '',
  className = '',
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchFocused, setSearchFocused] = useState(false);
  const filterRef = useRef(null);
  const searchRef = useRef(null);
  const filtersContainerRef = useRef(null);

  // Animation setup
  useEffect(() => {
    if (filterRef.current) {
      animationController.fadeIn(filterRef.current, {
        duration: 0.6,
        delay: 0.2,
      });
    }
  }, []);

  // Expand/collapse animation
  useEffect(() => {
    if (filtersContainerRef.current) {
      if (isExpanded) {
        gsap.to(filtersContainerRef.current, {
          height: 'auto',
          opacity: 1,
          duration: 0.4,
          ease: 'power2.out',
        });
      } else {
        gsap.to(filtersContainerRef.current, {
          height: 0,
          opacity: 0,
          duration: 0.3,
          ease: 'power2.in',
        });
      }
    }
  }, [isExpanded]);

  const handleFilterToggle = (filterId) => {
    const newActiveFilters = activeFilters.includes(filterId)
      ? activeFilters.filter(id => id !== filterId)
      : [...activeFilters, filterId];
    
    onFilterChange?.(newActiveFilters);
  };

  const clearAllFilters = () => {
    onFilterChange?.([]);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    onSearch?.(searchQuery);
  };

  return (
    <div
      ref={filterRef}
      className={`bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700 transition-all duration-300 ${className}`}
    >
      {/* Header */}
      <div className='p-6 border-b border-gray-100 dark:border-gray-700'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-3'>
            <div className='p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
              <FunnelIcon className='h-5 w-5 text-blue-600 dark:text-blue-400' />
            </div>
            <div>
              <h3 className='text-lg font-semibold text-gray-900 dark:text-gray-100'>
                Smart Filters
              </h3>
              <p className='text-sm text-gray-600 dark:text-gray-400'>
                {activeFilters.length} active filters
              </p>
            </div>
          </div>
          
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className='p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200'
          >
            <ChevronDownIcon 
              className={`h-5 w-5 text-gray-600 dark:text-gray-400 transition-transform duration-200 ${
                isExpanded ? 'rotate-180' : ''
              }`} 
            />
          </button>
        </div>

        {/* Search Bar */}
        <form onSubmit={handleSearchSubmit} className='mt-4'>
          <div className='relative'>
            <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
              <MagnifyingGlassIcon className='h-5 w-5 text-gray-400 dark:text-gray-500' />
            </div>
            <input
              ref={searchRef}
              type='text'
              value={searchQuery}
              onChange={(e) => onSearch?.(e.target.value)}
              onFocus={() => setSearchFocused(true)}
              onBlur={() => setSearchFocused(false)}
              placeholder='Search projects, skills, or keywords...'
              className={`block w-full pl-10 pr-4 py-3 border rounded-xl text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 dark:bg-gray-700 transition-all duration-200 ${
                searchFocused 
                  ? 'border-blue-500 bg-white dark:bg-gray-600' 
                  : 'border-gray-200 dark:border-gray-600 hover:bg-white dark:hover:bg-gray-600'
              }`}
            />
          </div>
        </form>
      </div>

      {/* Filters Container */}
      <div
        ref={filtersContainerRef}
        className='overflow-hidden'
        style={{ height: 0, opacity: 0 }}
      >
        <div className='p-6 space-y-6'>
          {/* Active Filters */}
          {activeFilters.length > 0 && (
            <div>
              <div className='flex items-center justify-between mb-3'>
                <h4 className='text-sm font-medium text-gray-900 dark:text-gray-100'>
                  Active Filters
                </h4>
                <button
                  onClick={clearAllFilters}
                  className='text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200'
                >
                  Clear All
                </button>
              </div>
              <div className='flex flex-wrap gap-2'>
                {activeFilters.map((filterId) => {
                  const filter = filters.find(f => f.id === filterId);
                  return filter ? (
                    <span
                      key={filterId}
                      className='inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200'
                    >
                      {filter.label}
                      <button
                        onClick={() => handleFilterToggle(filterId)}
                        className='ml-2 hover:text-blue-600 dark:hover:text-blue-400'
                      >
                        <XMarkIcon className='h-3 w-3' />
                      </button>
                    </span>
                  ) : null;
                })}
              </div>
            </div>
          )}

          {/* Filter Categories */}
          {filters.map((filterCategory) => (
            <div key={filterCategory.id}>
              <h4 className='text-sm font-medium text-gray-900 dark:text-gray-100 mb-3'>
                {filterCategory.label}
              </h4>
              <div className='space-y-2'>
                {filterCategory.options?.map((option) => (
                  <label
                    key={option.id}
                    className='flex items-center space-x-3 cursor-pointer group'
                  >
                    <input
                      type='checkbox'
                      checked={activeFilters.includes(option.id)}
                      onChange={() => handleFilterToggle(option.id)}
                      className='w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 focus:ring-2'
                    />
                    <span className='text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors duration-200'>
                      {option.label}
                    </span>
                    {option.count && (
                      <span className='text-xs text-gray-500 dark:text-gray-400'>
                        ({option.count})
                      </span>
                    )}
                  </label>
                ))}
              </div>
            </div>
          ))}

          {/* Advanced Filters Button */}
          <div className='pt-4 border-t border-gray-100 dark:border-gray-700'>
            <button className='flex items-center space-x-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200'>
              <AdjustmentsHorizontalIcon className='h-4 w-4' />
              <span>Advanced Filters</span>
            </button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      {!isExpanded && activeFilters.length > 0 && (
        <div className='px-6 pb-4'>
          <div className='flex flex-wrap gap-2'>
            {activeFilters.slice(0, 3).map((filterId) => {
              const filter = filters.find(f => f.id === filterId);
              return filter ? (
                <span
                  key={filterId}
                  className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200'
                >
                  {filter.label}
                </span>
              ) : null;
            })}
            {activeFilters.length > 3 && (
              <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'>
                +{activeFilters.length - 3} more
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AppleAnimatedFilter;
