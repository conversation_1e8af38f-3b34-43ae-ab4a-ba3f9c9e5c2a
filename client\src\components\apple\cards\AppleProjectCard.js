import { useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { animationController } from '../../../utils/animations';
import {
  ClockIcon,
  CurrencyDollarIcon,
  UserIcon,
  StarIcon,
  MapPinIcon,
  HeartIcon,
  BookmarkIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';

const AppleProjectCard = ({ project, index = 0 }) => {
  const cardRef = useRef(null);

  const {
    id,
    title,
    description,
    price,
    images = [],
    techStack = [],
    freelancer,
    soldCount = 0,
    postedAt,
    category,
  } = project;

  useEffect(() => {
    if (cardRef.current) {
      animationController.scaleIn(cardRef.current, {
        delay: index * 0.1,
        scrollTrigger: {
          trigger: cardRef.current,
          start: 'top 90%',
          end: 'bottom 10%',
          toggleActions: 'play none none reverse',
        },
      });
    }
  }, [index]);

  const formatPrice = (price) => `$${price}`;

  const formatTimeAgo = (date) => {
    const now = new Date();
    const posted = new Date(date);
    const diffInHours = Math.floor((now - posted) / (1000 * 60 * 60));
    if (diffInHours < 1) return 'Vừa đăng';
    if (diffInHours < 24) return `${diffInHours} giờ trước`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} ngày trước`;
    const diffInWeeks = Math.floor(diffInDays / 7);
    return `${diffInWeeks} tuần trước`;
  };

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className='group'
    >
      <div className='bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-100 dark:border-gray-700 hover:border-gray-200 dark:hover:border-gray-600 transition-all duration-300 hover:shadow-xl hover:-translate-y-2'>
        {/* Product Image */}
        <motion.div
          className='w-full h-48 rounded-xl overflow-hidden mb-4 shadow-md'
          whileHover={{ scale: 1.04 }}
          transition={{ type: 'spring', stiffness: 300 }}
        >
          <img
            src={images[0]}
            alt={title}
            className='object-cover w-full h-full transition-transform duration-300 group-hover:scale-105'
            loading='lazy'
          />
        </motion.div>
        {/* Title */}
        <h3 className='text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300'>
          {title}
        </h3>
        {/* Description */}
        <p className='text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3 leading-relaxed transition-colors duration-300'>
          {description}
        </p>
        {/* Tech Stack */}
        <div className='flex flex-wrap gap-2 mb-4'>
          {techStack.slice(0, 4).map((tech, idx) => (
            <span
              key={idx}
              className='px-2 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 rounded-full'
            >
              {tech}
            </span>
          ))}
          {techStack.length > 4 && (
            <span className='px-2 py-1 text-xs font-medium text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-full'>
              +{techStack.length - 4} more
            </span>
          )}
        </div>
        {/* Price & Sold */}
        <div className='flex items-center justify-between mb-4'>
          <span className='text-lg font-bold text-green-600 dark:text-green-400'>
            {formatPrice(price)}
          </span>
          <span className='text-xs text-gray-500 dark:text-gray-400'>
            Đã bán: <b>{soldCount}</b>
          </span>
        </div>
        {/* Freelancer Info */}
        <div className='flex items-center justify-between mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg'>
          <div className='flex items-center'>
            <img
              src={freelancer.avatar}
              alt={freelancer.name}
              className='w-8 h-8 rounded-full object-cover border border-blue-200 dark:border-blue-700'
            />
            <div className='ml-3'>
              <p className='text-sm font-medium text-gray-900 dark:text-gray-100'>
                {freelancer.name}
              </p>
              <div className='flex items-center'>
                <svg className='h-3 w-3 text-yellow-500 mr-1' fill='currentColor' viewBox='0 0 20 20'><path d='M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.18c.969 0 1.371 1.24.588 1.81l-3.388 2.46a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.388-2.46a1 1 0 00-1.176 0l-3.388 2.46c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.045 9.394c-.783-.57-.38-1.81.588-1.81h4.18a1 1 0 00.95-.69l1.286-3.967z'/></svg>
                <span className='text-xs text-gray-600 dark:text-gray-400'>
                  {freelancer.rating.toFixed(1)} ({freelancer.reviewCount})
                </span>
              </div>
            </div>
          </div>
          <span className='text-xs text-gray-500 dark:text-gray-400'>
            {formatTimeAgo(postedAt)}
          </span>
        </div>
        {/* Action Buttons */}
        <div className='flex gap-2'>
          <Link
            to={`/projects/${id}`}
            className='flex-1 px-4 py-3 text-center text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-all duration-200 group-hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2'
          >
            Xem chi tiết
          </Link>
          <motion.button
            whileTap={{ scale: 0.95 }}
            whileHover={{ scale: 1.05 }}
            className='flex-1 px-4 py-3 text-center text-sm font-medium text-white bg-green-500 hover:bg-green-600 rounded-lg transition-all duration-200 group-hover:shadow-md focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2'
          >
            Mua ngay
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default AppleProjectCard;
