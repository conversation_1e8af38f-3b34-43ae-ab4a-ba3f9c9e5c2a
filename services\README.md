# VWork Platform Backend Services

## Overview
VWork là nền tảng freelancing dựa trên microservices với các service sau:

- **API Gateway** (Port 8080) - Đ<PERSON><PERSON> tuyến request đến các service phù hợp
- **Auth Service** (Port 3001) - Xác thực Firebase và quản lý user
- **User Service** (Port 3002) - Hồ sơ user và quản lý freelancer
- **Project Service** (Port 3003) - Tạo project, đấu thầu và quản lý
- **Job Service** (Port 3004) - Đăng tin tuyển dụng và ứng tuyển
- **Chat Service** (Port 3005) - Nhắn tin real-time
- **Search Service** (Port 3009) - Tìm kiếm toàn cục

## Kiến trúc Microservices

### Tính năng chính
- **Độc lập hoàn toàn**: Mỗi service có thể deploy riêng lẻ
- **Không dùng shared modules**: Mỗi service có utils riêng
- **API Gateway**: <PERSON><PERSON><PERSON> tuyến và load balancing
- **Firebase Auth**: <PERSON><PERSON><PERSON> thực tập trung
- **Standardized Responses**: Format response thống nhất

### Cấu trúc Service
```
services/
├── api-gateway/          # API Gateway (Port 8080)
├── auth-service/         # Authentication (Port 3001)
├── user-service/         # User Management (Port 3002)
├── project-service/      # Project Management (Port 3003)
├── job-service/          # Job Management (Port 3004)
├── chat-service/         # Messaging (Port 3005)
├── payment-service/      # Payment Processing (Port 3006)
└── start-all.js         # Script khởi động tất cả
```

## Quick Start

### Prerequisites
- Node.js 16+ 
- npm hoặc yarn
- Firebase project setup

### Environment Variables
Tạo file `.env` trong mỗi service directory:

```bash
# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_SERVICE_ACCOUNT_KEY=your-service-account-json

# Service URLs (cho production)
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002
PROJECT_SERVICE_URL=http://localhost:3003
JOB_SERVICE_URL=http://localhost:3004
CHAT_SERVICE_URL=http://localhost:3005
SEARCH_SERVICE_URL=http://localhost:3009

# CORS Origins
CORS_ORIGIN=http://localhost:3000,http://localhost:3006
```

### Installation

1. **Cài đặt dependencies cho tất cả services:**
```bash
# Cài đặt từng service
cd auth-service && npm install
cd ../user-service && npm install  
cd ../project-service && npm install
cd ../job-service && npm install
cd ../chat-service && npm install
cd ../api-gateway && npm install
```

2. **Khởi động tất cả services:**
```bash
# Từ thư mục services
node start-all.js
```

Hoặc khởi động riêng lẻ:
```bash
# API Gateway
cd api-gateway && npm run dev

# Auth Service  
cd auth-service && npm run dev

# User Service
cd user-service && npm run dev

# Project Service
cd project-service && npm run dev

# Job Service
cd job-service && npm run dev

# Chat Service
cd chat-service && npm run dev
```

### Testing

Chạy API integration tests:
```bash
# Cài đặt axios cho testing
npm install axios

# Chạy tests
node test-api.js
```

## API Documentation

### Base URL
- Development: `http://localhost:8080/api/v1`
- Tất cả endpoints trả về JSON responses chuẩn

### Authentication
Protected endpoints yêu cầu Firebase ID token:
```
Authorization: Bearer <firebase_id_token>
```

### Key Endpoints

#### Authentication
- `POST /auth/register` - Đăng ký user mới
- `POST /auth/login` - Đăng nhập với Firebase token
- `GET /auth/me` - Lấy thông tin user hiện tại

#### Users & Freelancers
- `GET /users/:id` - Lấy hồ sơ user
- `PUT /users/:id` - Cập nhật hồ sơ user
- `GET /freelancers` - Danh sách freelancers với filters

#### Projects
- `GET /projects` - Danh sách projects với filters
- `GET /projects/:id` - Chi tiết project
- `POST /projects` - Tạo project mới
- `POST /projects/:id/bids` - Nộp bid

#### Jobs
- `GET /jobs` - Danh sách jobs với filters
- `GET /jobs/:id` - Chi tiết job
- `POST /jobs` - Tạo job mới
- `POST /jobs/:id/apply` - Ứng tuyển job

#### Search
- `GET /search` - Tìm kiếm toàn cục
- `GET /search/suggestions` - Gợi ý tìm kiếm
- `GET /search/popular` - Tìm kiếm phổ biến

### Response Format
```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

## Deploy lên Render

### Deploy từng service riêng lẻ

1. **Auth Service:**
```bash
# Tạo service trên Render
# Root Directory: services/auth-service
# Build Command: npm ci
# Start Command: npm start
```

2. **User Service:**
```bash
# Root Directory: services/user-service
# Build Command: npm ci
# Start Command: npm start
```

3. **Project Service:**
```bash
# Root Directory: services/project-service
# Build Command: npm ci
# Start Command: npm start
```

4. **Job Service:**
```bash
# Root Directory: services/job-service
# Build Command: npm ci
# Start Command: npm start
```

5. **Chat Service:**
```bash
# Root Directory: services/chat-service
# Build Command: npm ci
# Start Command: npm start
```

6. **API Gateway:**
```bash
# Root Directory: services/api-gateway
# Build Command: npm ci
# Start Command: npm start
```

### Environment Variables cho Production

Cập nhật các service URLs trong API Gateway:
```
AUTH_SERVICE_URL=https://vwork-auth-service.onrender.com
USER_SERVICE_URL=https://vwork-user-service.onrender.com
PROJECT_SERVICE_URL=https://vwork-project-service.onrender.com
JOB_SERVICE_URL=https://vwork-job-service.onrender.com
CHAT_SERVICE_URL=https://vwork-chat-service.onrender.com
```

## Development

### Thêm Endpoints mới
1. Thêm route vào service phù hợp
2. Cập nhật validation schemas nếu cần
3. Thêm proxy route trong API Gateway
4. Cập nhật API documentation
5. Thêm tests

### Service Communication
Services có thể gọi nhau qua HTTP:
```javascript
const axios = require('axios');
const response = await axios.get('http://localhost:3002/users/123');
```

### Error Handling
Tất cả services sử dụng error responses chuẩn:
```javascript
res.apiError('Error message', 'ERROR_CODE', 500);
```

## Production Deployment

### Docker Support
Mỗi service bao gồm Dockerfile để containerization.

### Environment Configuration
- Sử dụng environment variables cho tất cả configuration
- Configs riêng cho dev/staging/production
- Sử dụng secrets management cho dữ liệu nhạy cảm

### Monitoring
- Health check endpoints: `/health`
- Structured logging với request IDs
- Performance metrics collection

## Troubleshooting

### Common Issues

1. **Port conflicts:** Kiểm tra ports 3001-3005, 3009, 8080 có available
2. **Firebase errors:** Verify Firebase configuration và service account
3. **CORS issues:** Cập nhật CORS_ORIGIN environment variable
4. **Service unavailable:** Kiểm tra tất cả services đang chạy

### Health Checks
- API Gateway: `http://localhost:8080/health`
- Auth Service: `http://localhost:3001/health`
- User Service: `http://localhost:3002/health`
- Project Service: `http://localhost:3003/health`
- Job Service: `http://localhost:3004/health`
- Chat Service: `http://localhost:3005/health`
- Search Service: `http://localhost:3009/health`

### Logs
Mỗi service log ra console với format có cấu trúc:
```
[Service Name] LOG_LEVEL: Message
```

## Contributing

1. Follow existing code structure và patterns
2. Thêm validation cho tất cả inputs
3. Include error handling
4. Cập nhật documentation
5. Thêm tests cho features mới

## License
MIT License
