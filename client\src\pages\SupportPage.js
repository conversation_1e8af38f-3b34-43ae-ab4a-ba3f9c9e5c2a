import React from 'react';
import {
  QuestionMarkCircleIcon,
  ShieldCheckIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';
import { ApplePageWrapper } from '../components/apple';

const SupportPage = () => {
  return (
    <ApplePageWrapper variant='gray'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        {/* Apple-style Header */}
        <div className='text-center mb-12'>
          <div className='w-16 h-16 bg-blue-500 rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg'>
            <QuestionMarkCircleIcon className='h-8 w-8 text-white' />
          </div>
          <h1 className='text-4xl md:text-5xl font-bold text-gray-900 mb-4'>
            Support Center
          </h1>
          <p className='text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed'>
            Get assistance from our dedicated support team
          </p>
        </div>

        {/* Apple-style Content */}
        <div className='bg-white rounded-2xl p-8 max-w-4xl mx-auto shadow-sm border border-gray-100'>
          <div className='text-center'>
            <div className='w-12 h-12 bg-blue-100 rounded-xl mx-auto mb-4 flex items-center justify-center'>
              <ChatBubbleLeftRightIcon className='h-6 w-6 text-blue-600' />
            </div>
            <h3 className='text-2xl font-semibold text-gray-900 mb-4'>
              Help Center Coming Soon
            </h3>
            <p className='text-lg text-gray-600 mb-8'>
              Our team is preparing comprehensive guides and documentation. Soon
              you will have access to all the resources needed for your platform
              journey.
            </p>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-8'>
              <div className='bg-gray-50 rounded-xl p-6 text-center'>
                <div className='w-12 h-12 bg-orange-100 rounded-xl mx-auto mb-4 flex items-center justify-center'>
                  <QuestionMarkCircleIcon className='h-6 w-6 text-orange-600' />
                </div>
                <h4 className='font-semibold text-gray-900 mb-2'>FAQs</h4>
                <p className='text-sm text-gray-600'>
                  Common platform questions
                </p>
              </div>
              <div className='bg-gray-50 rounded-xl p-6 text-center'>
                <div className='w-12 h-12 bg-blue-100 rounded-xl mx-auto mb-4 flex items-center justify-center'>
                  <ChatBubbleLeftRightIcon className='h-6 w-6 text-blue-600' />
                </div>
                <h4 className='font-semibold text-gray-900 mb-2'>Live Chat</h4>
                <p className='text-sm text-gray-600'>
                  Speak with support advisors
                </p>
              </div>
              <div className='bg-gray-50 rounded-xl p-6 text-center'>
                <div className='w-12 h-12 bg-green-100 rounded-xl mx-auto mb-4 flex items-center justify-center'>
                  <ShieldCheckIcon className='h-6 w-6 text-green-600' />
                </div>
                <h4 className='font-semibold text-gray-900 mb-2'>Guides</h4>
                <p className='text-sm text-gray-600'>Step-by-step tutorials</p>
              </div>
            </div>
            <div className='flex justify-center space-x-4'>
              <button className='btn btn-primary px-6 py-3'>
                Return to Dashboard
              </button>
              <button className='btn btn-secondary px-6 py-3'>
                Get Started
              </button>
            </div>
          </div>
        </div>
      </div>
    </ApplePageWrapper>
  );
};

export default SupportPage;
