import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { sendEmailVerification } from 'firebase/auth';
import { auth } from '../config/firebase';
import { toast } from 'react-hot-toast';

const EmailVerificationPage = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    // If user is already verified, check their status and redirect appropriately
    if (user?.emailVerified) {
      // Let ProfileGuard handle the redirect based on user status
      navigate('/login-success');
    }
    
    // If no user, redirect to login
    if (!user) {
      navigate('/login');
    }
  }, [user, navigate]);

  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const handleResendVerification = async () => {
    if (!user || countdown > 0) return;
    
    setLoading(true);
    try {
      await sendEmailVerification(auth.currentUser);
      toast.success('Email xác thực đã được gửi!');
      setCountdown(60); // 60 second cooldown
    } catch (error) {
      console.error('Error sending verification:', error);
      toast.error('Không thể gửi email xác thực. Vui lòng thử lại.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshStatus = async () => {
    if (!auth.currentUser) return;
    
    try {
      await auth.currentUser.reload();
      if (auth.currentUser.emailVerified) {
        toast.success('Email đã được xác thực!');
        window.location.reload(); // Refresh to update auth state
      } else {
        toast.error('Email chưa được xác thực');
      }
    } catch (error) {
      console.error('Error refreshing status:', error);
      toast.error('Không thể kiểm tra trạng thái email');
    }
  };

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-orange-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="w-full max-w-md bg-white rounded-2xl shadow-xl p-8"
      >
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className="text-6xl mb-4"
          >
            📧
          </motion.div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            Xác thực Email
          </h1>
          <p className="text-gray-600 text-sm">
            Vui lòng xác thực email của bạn để tiếp tục sử dụng VWork
          </p>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-3">
            <span className="text-yellow-600 text-xl">⚠️</span>
            <div>
              <p className="text-sm text-yellow-800 font-medium">
                Email chưa được xác thực
              </p>
              <p className="text-sm text-yellow-700 mt-1">
                Chúng tôi đã gửi email xác thực tới: <br />
                <span className="font-medium">{user.email}</span>
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <button
            onClick={handleResendVerification}
            disabled={loading || countdown > 0}
            className={`w-full py-3 px-4 rounded-lg font-medium transition-all ${
              loading || countdown > 0
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Đang gửi...
              </div>
            ) : countdown > 0 ? (
              `Gửi lại sau ${countdown}s`
            ) : (
              'Gửi lại email xác thực'
            )}
          </button>

          <button
            onClick={handleRefreshStatus}
            className="w-full py-3 px-4 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Tôi đã xác thực email
          </button>

          <button
            onClick={handleLogout}
            className="w-full py-2 px-4 text-gray-500 hover:text-gray-700 transition-colors"
          >
            Đăng xuất
          </button>
        </div>

        <div className="mt-8 text-center text-sm text-gray-500">
          <p>Không nhận được email?</p>
          <p className="mt-1">
            Kiểm tra thư mục spam hoặc liên hệ hỗ trợ
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default EmailVerificationPage;