import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircleIcon } from '@heroicons/react/24/solid';
import { PROFILE_STEPS } from '../../utils/userStatus';

const ProfileStepIndicator = ({ currentStep, totalSteps = 5, userType = 'freelancer' }) => {
  const steps = [
    { number: 1, title: 'Thông tin cơ bản', icon: '👤' },
    { number: 2, title: 'Kỹ năng', icon: '🎯', showFor: ['freelancer'] },
    { number: 3, title: 'Kinh nghiệm', icon: '💼' },
    { number: 4, title: 'Portfolio', icon: '📁', showFor: ['freelancer'] },
    { number: 5, title: 'T<PERSON><PERSON> chọn', icon: '⚙️' }
  ];

  // Filter steps based on user type
  const filteredSteps = steps.filter(step => 
    !step.showFor || step.showFor.includes(userType)
  );

  return (
    <div className="w-full max-w-4xl mx-auto mb-8">
      <div className="relative">
        {/* Progress Line */}
        <div className="absolute top-6 left-0 w-full h-0.5 bg-gray-200">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${((currentStep - 1) / (filteredSteps.length - 1)) * 100}%` }}
            transition={{ duration: 0.5 }}
            className="h-full bg-blue-500"
          />
        </div>

        {/* Steps */}
        <div className="relative flex justify-between">
          {filteredSteps.map((step, index) => {
            const isCompleted = currentStep > step.number;
            const isCurrent = currentStep === step.number;
            const isUpcoming = currentStep < step.number;

            return (
              <motion.div
                key={step.number}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex flex-col items-center"
              >
                {/* Step Circle */}
                <div
                  className={`
                    relative w-12 h-12 rounded-full flex items-center justify-center
                    border-2 transition-all duration-300
                    ${isCompleted 
                      ? 'bg-green-500 border-green-500 text-white' 
                      : isCurrent 
                        ? 'bg-blue-500 border-blue-500 text-white' 
                        : 'bg-white border-gray-300 text-gray-400'
                    }
                  `}
                >
                  {isCompleted ? (
                    <CheckCircleIcon className="w-6 h-6" />
                  ) : (
                    <span className="text-lg">{step.icon}</span>
                  )}
                  
                  {/* Current step pulse */}
                  {isCurrent && (
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="absolute inset-0 rounded-full bg-blue-500 opacity-20"
                    />
                  )}
                </div>

                {/* Step Title */}
                <div className="mt-3 text-center">
                  <div
                    className={`
                      text-sm font-medium transition-colors
                      ${isCompleted 
                        ? 'text-green-600' 
                        : isCurrent 
                          ? 'text-blue-600' 
                          : 'text-gray-400'
                      }
                    `}
                  >
                    Bước {step.number}
                  </div>
                  <div
                    className={`
                      text-xs mt-1 max-w-20 transition-colors
                      ${isCompleted 
                        ? 'text-green-500' 
                        : isCurrent 
                          ? 'text-blue-500' 
                          : 'text-gray-400'
                      }
                    `}
                  >
                    {step.title}
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Progress Text */}
      <div className="text-center mt-6">
        <div className="text-sm text-gray-600">
          Bước {currentStep} / {filteredSteps.length}
        </div>
        <div className="w-full max-w-xs mx-auto mt-2 bg-gray-200 rounded-full h-2">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${(currentStep / filteredSteps.length) * 100}%` }}
            transition={{ duration: 0.5 }}
            className="h-2 bg-blue-500 rounded-full"
          />
        </div>
      </div>
    </div>
  );
};

export default ProfileStepIndicator;
