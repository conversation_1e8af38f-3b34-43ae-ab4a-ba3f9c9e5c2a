/**
 * Job Service Utilities
 * Standalone utilities for job service (no shared dependencies)
 */

const Joi = require('joi');

// Response utilities
class ApiResponse {
  static success(data = null, message = 'Success', pagination = null) {
    const response = {
      success: true,
      data,
      message
    };

    if (pagination) {
      response.pagination = pagination;
    }

    return response;
  }

  static error(message = 'An error occurred', code = 'INTERNAL_ERROR', statusCode = 500) {
    return {
      success: false,
      error: message,
      message,
      code,
      statusCode
    };
  }

  static validationError(errors, message = 'Validation failed') {
    return {
      success: false,
      error: message,
      message,
      code: 'VALIDATION_ERROR',
      statusCode: 422,
      errors
    };
  }

  static unauthorized(message = 'Unauthorized access') {
    return {
      success: false,
      error: message,
      message,
      code: 'UNAUTHORIZED',
      statusCode: 401
    };
  }

  static forbidden(message = 'Access forbidden') {
    return {
      success: false,
      error: message,
      message,
      code: 'FORBIDDEN',
      statusCode: 403
    };
  }

  static notFound(message = 'Resource not found') {
    return {
      success: false,
      error: message,
      message,
      code: 'NOT_FOUND',
      statusCode: 404
    };
  }

  static badRequest(message = 'Bad request') {
    return {
      success: false,
      error: message,
      message,
      code: 'BAD_REQUEST',
      statusCode: 400
    };
  }
}

// Response middleware
const responseMiddleware = (req, res, next) => {
  res.apiSuccess = (data, message, pagination) => {
    const response = ApiResponse.success(data, message, pagination);
    res.status(200).json(response);
  };

  res.apiError = (message, code, statusCode = 500) => {
    const response = ApiResponse.error(message, code, statusCode);
    res.status(statusCode).json(response);
  };

  res.apiValidationError = (errors, message) => {
    const response = ApiResponse.validationError(errors, message);
    res.status(422).json(response);
  };

  res.apiUnauthorized = (message) => {
    const response = ApiResponse.unauthorized(message);
    res.status(401).json(response);
  };

  res.apiForbidden = (message) => {
    const response = ApiResponse.forbidden(message);
    res.status(403).json(response);
  };

  res.apiNotFound = (message) => {
    const response = ApiResponse.notFound(message);
    res.status(404).json(response);
  };

  res.apiBadRequest = (message) => {
    const response = ApiResponse.badRequest(message);
    res.status(400).json(response);
  };

  next();
};

// Validation utilities
const validateBody = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      return res.apiValidationError(errors, 'Validation failed');
    }

    req.body = value;
    next();
  };
};

const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      return res.apiValidationError(errors, 'Validation failed');
    }

    req.query = value;
    next();
  };
};

// Auth middleware
const verifyFirebaseToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.apiUnauthorized('No token provided');
    }

    // For now, just check if token exists
    // In production, this should call Auth Service
    if (token && token !== 'undefined' && token !== 'null') {
      // Mock user data for development
      req.user = {
        uid: 'mock-user-id',
        email: '<EMAIL>',
        name: 'Mock User',
        userType: 'freelancer'
      };
      next();
    } else {
      res.apiUnauthorized('Invalid token');
    }

  } catch (error) {
    console.error('❌ Token verification failed:', error.message);
    res.apiUnauthorized('Invalid token');
  }
};

const optionalAuth = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token || token === 'undefined' || token === 'null') {
      return next(); // Continue without user data
    }

    // Mock user data for development
    req.user = {
      uid: 'mock-user-id',
      email: '<EMAIL>',
      name: 'Mock User',
      userType: 'freelancer'
    };

    next();

  } catch (error) {
    console.error('❌ Optional auth failed:', error.message);
    next(); // Continue without user data
  }
};

// Pagination utility
const createPagination = (page, limit, total) => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    page: parseInt(page),
    limit: parseInt(limit),
    total: parseInt(total),
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  };
};

// Job service validation schemas
const schemas = {
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20)
  }),

  jobCreate: Joi.object({
    title: Joi.string().min(5).max(200).required(),
    description: Joi.string().min(20).max(5000).required(),
    company: Joi.string().min(2).max(200).required(),
    location: Joi.string().max(200).required(),
    type: Joi.string().valid('full-time', 'part-time', 'contract', 'freelance').required(),
    category: Joi.string().max(100).required(),
    salary: Joi.object({
      min: Joi.number().min(0).required(),
      max: Joi.number().min(Joi.ref('min')).required(),
      currency: Joi.string().length(3).default('USD'),
      period: Joi.string().valid('hour', 'month', 'year').required()
    }).required(),
    skills: Joi.array().items(Joi.string().max(50)).min(1).max(10).required(),
    requirements: Joi.array().items(Joi.string().max(200)).max(10).optional(),
    benefits: Joi.array().items(Joi.string().max(200)).max(10).optional()
  }),

  jobApplication: Joi.object({
    coverLetter: Joi.string().min(50).max(2000).required(),
    resume: Joi.string().uri().required(),
    portfolio: Joi.array().items(Joi.string().uri()).max(5).optional()
  })
};

module.exports = {
  ApiResponse,
  responseMiddleware,
  validateBody,
  validateQuery,
  verifyFirebaseToken,
  optionalAuth,
  createPagination,
  schemas
}; 