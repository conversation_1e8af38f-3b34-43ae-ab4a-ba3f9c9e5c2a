/* Dark Mode Comprehensive Support for VWork */

/* Global Dark Mode Variables */
:root.dark {
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --text-primary: #f9fafb;
  --text-secondary: #e5e7eb;
  --text-tertiary: #d1d5db;
  --border-primary: #374151;
  --border-secondary: #4b5563;
  --shadow-color: rgba(0, 0, 0, 0.5);
}

/* Critical Text Visibility Fixes */
.dark {
  /* Ensure all text is visible in dark mode */
  color: #f9fafb;
}

/* Gradient Text Fixes for Dark Mode */
.dark .marketplace-text {
  background: linear-gradient(90deg, #60A5FA, #A78BFA, #60A5FA);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: #60A5FA; /* Fallback for browsers that don't support bg-clip */
}

.dark .marketplace-text:not([style*="background-clip"]) {
  color: #60A5FA !important;
  -webkit-text-fill-color: #60A5FA !important;
}

/* Ensure gradient text works in dark mode */
.dark .bg-gradient-to-r {
  background: linear-gradient(90deg, #60A5FA, #A78BFA, #818CF8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dark .bg-gradient-to-r:not([style*="background-clip"]) {
  color: #60A5FA !important;
  -webkit-text-fill-color: #60A5FA !important;
}

/* Common text color fixes */
.dark .text-gray-900 {
  color: #f9fafb !important;
}

.dark .text-gray-800 {
  color: #f3f4f6 !important;
}

.dark .text-gray-700 {
  color: #e5e7eb !important;
}

.dark .text-gray-600 {
  color: #d1d5db !important;
}

.dark .text-gray-500 {
  color: #9ca3af !important;
}

.dark .text-gray-400 {
  color: #9ca3af !important;
}

.dark .text-gray-300 {
  color: #d1d5db !important;
}

.dark .text-gray-200 {
  color: #e5e7eb !important;
}

.dark .text-gray-100 {
  color: #f3f4f6 !important;
}

/* Blue text colors for dark mode */
.dark .text-blue-600 {
  color: #60a5fa !important;
}

.dark .text-blue-500 {
  color: #3b82f6 !important;
}

.dark .text-blue-400 {
  color: #60a5fa !important;
}

/* Background color fixes */
.dark .bg-white {
  background-color: #1f2937 !important;
}

.dark .bg-gray-50 {
  background-color: #374151 !important;
}

.dark .bg-gray-100 {
  background-color: #4b5563 !important;
}

.dark .bg-gray-200 {
  background-color: #6b7280 !important;
}

/* Border color fixes */
.dark .border-gray-200 {
  border-color: #4b5563 !important;
}

.dark .border-gray-300 {
  border-color: #6b7280 !important;
}

.dark .border-gray-400 {
  border-color: #9ca3af !important;
}

.dark .border-gray-500 {
  border-color: #9ca3af !important;
}

.dark .border-gray-600 {
  border-color: #6b7280 !important;
}

.dark .border-gray-700 {
  border-color: #4b5563 !important;
}

/* Apple Components Dark Mode Overrides */

/* Hero Section */
.dark .hero-section {
  background: linear-gradient(135deg, #111827 0%, #1f2937 50%, #111827 100%);
}

.dark .hero-background {
  background: linear-gradient(to bottom right, #111827, #1f2937, #111827);
}

/* Feature Cards */
.dark .feature-card,
.dark .stat-card,
.dark .project-card {
  background: #1f2937;
  border-color: #374151;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
}

.dark .feature-card:hover,
.dark .stat-card:hover,
.dark .project-card:hover {
  background: #374151;
  border-color: #4b5563;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
}

/* Navigation */
.dark .nav-item {
  color: #d1d5db;
}

.dark .nav-item:hover {
  color: #f9fafb;
}

.dark .nav-item.active {
  color: #60a5fa;
}

/* Buttons */
.dark .btn-apple-primary {
  background: #3b82f6;
  color: #ffffff;
}

.dark .btn-apple-primary:hover {
  background: #2563eb;
}

.dark .btn-apple-secondary {
  background: #374151;
  color: #e5e7eb;
  border-color: #4b5563;
}

.dark .btn-apple-secondary:hover {
  background: #4b5563;
  color: #f9fafb;
}

/* Forms */
.dark input,
.dark textarea,
.dark select {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.dark input::placeholder,
.dark textarea::placeholder {
  color: #9ca3af;
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Modals and Overlays */
.dark .modal-overlay {
  background: rgba(0, 0, 0, 0.8);
}

.dark .modal-content {
  background: #1f2937;
  border-color: #374151;
}

/* Tables */
.dark .table-header {
  background: #374151;
  color: #f9fafb;
}

.dark .table-row {
  background: #1f2937;
  border-color: #374151;
}

.dark .table-row:hover {
  background: #374151;
}

/* Dropdowns */
.dark .dropdown-menu {
  background: #1f2937;
  border-color: #374151;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
}

.dark .dropdown-item {
  color: #e5e7eb;
}

.dark .dropdown-item:hover {
  background: #374151;
  color: #f9fafb;
}

/* Tooltips */
.dark .tooltip {
  background: #374151;
  color: #f9fafb;
  border-color: #4b5563;
}

/* Progress Bars */
.dark .progress-bar {
  background: #374151;
}

.dark .progress-fill {
  background: #3b82f6;
}

/* Badges */
.dark .badge {
  background: #374151;
  color: #e5e7eb;
}

.dark .badge-primary {
  background: #3b82f6;
  color: #ffffff;
}

.dark .badge-success {
  background: #10b981;
  color: #ffffff;
}

.dark .badge-warning {
  background: #f59e0b;
  color: #ffffff;
}

.dark .badge-danger {
  background: #ef4444;
  color: #ffffff;
}

/* Alerts */
.dark .alert {
  background: #374151;
  border-color: #4b5563;
  color: #e5e7eb;
}

.dark .alert-info {
  background: #1e3a8a;
  border-color: #3b82f6;
  color: #dbeafe;
}

.dark .alert-success {
  background: #064e3b;
  border-color: #10b981;
  color: #d1fae5;
}

.dark .alert-warning {
  background: #451a03;
  border-color: #f59e0b;
  color: #fef3c7;
}

.dark .alert-danger {
  background: #450a0a;
  border-color: #ef4444;
  color: #fee2e2;
}

/* Scrollbar for dark mode */
.dark ::-webkit-scrollbar {
  width: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Code blocks and pre elements */
.dark pre,
.dark code {
  background: #1f2937;
  color: #e5e7eb;
  border-color: #374151;
}

/* Dividers */
.dark .divider {
  border-color: #374151;
}

/* Loading spinners */
.dark .spinner {
  border-color: #374151;
  border-top-color: #3b82f6;
}

/* Dashboard specific styles */
.dark .dashboard-card {
  background: #1f2937;
  border-color: #374151;
}

.dark .project-card-header {
  background: #374151;
  color: #f9fafb;
}

.dark .profile-card {
  background: #1f2937;
  border-color: #374151;
}

/* Chat and messaging */
.dark .chat-message {
  background: #374151;
  color: #e5e7eb;
}

.dark .chat-message.own {
  background: #3b82f6;
  color: #ffffff;
}

/* Search components */
.dark .search-input {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.dark .search-results {
  background: #1f2937;
  border-color: #374151;
}

/* Filter buttons */
.dark .filter-button {
  background: #374151;
  color: #e5e7eb;
  border-color: #4b5563;
}

.dark .filter-button.active {
  background: #3b82f6;
  color: #ffffff;
}

/* Pagination */
.dark .pagination-button {
  background: #374151;
  color: #e5e7eb;
  border-color: #4b5563;
}

.dark .pagination-button:hover {
  background: #4b5563;
  color: #f9fafb;
}

.dark .pagination-button.active {
  background: #3b82f6;
  color: #ffffff;
}

/* Ensure all elements have proper dark mode support */
.dark * {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}
