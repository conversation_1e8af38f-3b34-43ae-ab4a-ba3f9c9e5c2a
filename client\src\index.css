@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cinzel+Decorative:wght@400;700;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=MedievalSharp&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Dark Mode Styles */
@import './styles/dark-mode.css';

@layer base {
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Dark mode body styles */
  .dark body {
    background-color: #111827;
    color: #f9fafb;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    letter-spacing: -0.025em;
  }

  p {
    line-height: 1.65;
  }

  /* Focus styles for accessibility */
  button:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible,
  a:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  /* Dark mode focus styles */
  .dark button:focus-visible,
  .dark input:focus-visible,
  .dark textarea:focus-visible,
  .dark select:focus-visible,
  .dark a:focus-visible {
    outline: 2px solid #60a5fa;
    outline-offset: 2px;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md transition-all duration-200;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700;
  }

  /* Card Components */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  /* Form Components */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  /* Glass morphism */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-gray-900/80 backdrop-blur-md border border-gray-700/30;
  }

  /* Apple Theme Dark Mode Support */
  .apple-theme-dark {
    @apply bg-gray-900 text-gray-100;
  }

  .apple-theme-light {
    @apply bg-white text-gray-900;
  }

  /* Dark mode card styles */
  .dark .card-medieval {
    @apply bg-gray-800 border-gray-700;
  }

  .dark .text-medieval-brown-800 {
    @apply text-gray-100;
  }

  .dark .text-medieval-brown-700 {
    @apply text-gray-200;
  }

  .dark .text-medieval-brown-600 {
    @apply text-gray-300;
  }

  .dark .bg-medieval-parchment-50 {
    @apply bg-gray-700;
  }

  .dark .border-medieval-gold-200 {
    @apply border-gray-600;
  }

  .dark .form-input-medieval {
    @apply bg-gray-700 border-gray-600 text-gray-100;
  }

  .dark .btn-medieval {
    @apply bg-primary-600 hover:bg-primary-700;
  }

  .dark .btn-medieval-secondary {
    @apply bg-gray-700 hover:bg-gray-600 text-gray-100;
  }

  /* Simplified Dark Mode Support */

  /* Basic dark mode overrides without circular dependencies */
  .dark {
    color-scheme: dark;
  }
}

@layer utilities {
  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  /* Text utilities */
  .text-gradient {
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Container utilities */
  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .section-spacing {
    @apply py-16 sm:py-20 lg:py-24;
  }

  /* Header utilities */
  .header-button-priority {
    min-width: -webkit-fill-available !important;
    min-width: -moz-fit-content !important;
    min-width: fit-content !important;
    flex-shrink: 0 !important;
    white-space: nowrap !important;
    position: relative !important;
    z-index: 10 !important;
  }

  .header-compact-spacing {
    gap: 0.25rem !important;
  }

  @media (min-width: 1024px) {
    .header-compact-spacing {
      gap: 0.5rem !important;
    }
  }

  /* Header layout fixes for zoom issues */
  .header-container {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
    min-height: 4rem !important;
    gap: 0.5rem !important;
  }

  .header-logo-section {
    flex-shrink: 0 !important;
    min-width: -webkit-fill-available !important;
    min-width: -moz-fit-content !important;
    min-width: fit-content !important;
    max-width: 150px !important;
  }

  .header-nav-section {
    flex: 1 1 auto !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    min-width: 0 !important;
    overflow: hidden !important;
  }

  .header-actions-section {
    flex-shrink: 0 !important;
    min-width: -webkit-fill-available !important;
    min-width: -moz-fit-content !important;
    min-width: fit-content !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.125rem !important;
  }

  @media (min-width: 1024px) {
    .header-actions-section {
      gap: 0.25rem !important;
    }
  }

  /* Ensure navigation doesn't overflow */
  .header-nav-item {
    flex-shrink: 1 !important;
    min-width: -webkit-fill-available !important;
    min-width: -moz-fit-content !important;
    min-width: fit-content !important;
    white-space: nowrap !important;
  }

  /* Critical button protection */
  .header-critical-button {
    position: relative !important;
    z-index: 20 !important;
    flex-shrink: 0 !important;
    min-width: -webkit-fill-available !important;
    min-width: -moz-fit-content !important;
    min-width: fit-content !important;
    white-space: nowrap !important;
  }

  /* Zoom-specific fixes */
  @media screen and (min-resolution: 96dpi) and (max-resolution: 120dpi) {
    .header-container {
      gap: 0.125rem !important;
    }

    .header-nav-section {
      max-width: calc(100vw - 350px) !important;
    }

    .header-actions-section {
      min-width: 150px !important;
    }
  }

  /* High DPI / Zoom level adjustments */
  @media screen and (min-resolution: 120dpi) {
    .header-container {
      gap: 0.125rem !important;
    }

    .header-nav-section {
      max-width: calc(100vw - 350px) !important;
    }

    .header-nav-item {
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }
  }

  /* Force layout stability at all zoom levels */
  .header-container * {
    box-sizing: border-box !important;
  }

  /* Prevent navigation overflow at any zoom level */
  .header-nav-section > div {
    overflow: hidden !important;
    flex-wrap: nowrap !important;
  }

  /* Ensure critical button always has space */
  .header-critical-button {
    min-width: 70px !important;
    max-width: 100px !important;
    margin-right: 0 !important;
    right: 0 !important;
    order: 999 !important;
  }

  /* Additional compact spacing for tight layouts */
  .header-compact-mode {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
  }

  @media (max-width: 1280px) {
    .header-critical-button {
      font-size: 0.875rem !important;
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }
  }

  /* Force button to always be visible */
  .header-critical-button {
    position: relative !important;
    right: 0 !important;
    margin-right: 0 !important;
    order: 999 !important;
  }

  /* Ensure actions section has enough space */
  .header-actions-section {
    min-width: 80px !important;
    justify-content: flex-end !important;
  }

  /* Scrollbar utilities with enhanced browser compatibility */
  .scrollbar-thin {
    -ms-overflow-style: thin;
    /* Firefox */
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
    /* Fallback for browsers that don't support scrollbar-width */
    overflow: auto;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.7);
  }

  .scrollbar-thin::-webkit-scrollbar-corner {
    background: transparent;
  }
}

/* Keyframes for animations */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { 
    opacity: 0; 
    transform: translateY(30px); 
  }
  100% { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes scaleIn {
  0% { 
    opacity: 0; 
    transform: scale(0.9); 
  }
  100% { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* Blob animation for hero section */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Global scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Text selection */
::selection {
  background-color: #3b82f6;
  color: white;
}

.dark ::selection {
  background-color: #60a5fa;
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Medieval Theme Specific Styles */
.theme-medieval {
  --font-display: 'Cinzel Decorative', serif;
  --font-heading: 'Cinzel', serif;
  --font-serif: 'Cinzel', serif;
  --font-medieval: 'MedievalSharp', serif;
}

.theme-medieval .font-display {
  font-family: var(--font-display);
}

.theme-medieval .font-heading {
  font-family: var(--font-heading);
}

.theme-medieval .font-serif {
  font-family: var(--font-serif);
}

.theme-medieval .font-medieval {
  font-family: var(--font-medieval);
}

/* Medieval specific animations */
@keyframes medieval-glow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(183, 151, 57, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(183, 151, 57, 0.8), 0 0 30px rgba(183, 151, 57, 0.6);
  }
}

.theme-medieval .animate-medieval-glow {
  animation: medieval-glow 2s ease-in-out infinite;
}

/* Medieval scrollbar */
.theme-medieval::-webkit-scrollbar {
  width: 12px;
}

.theme-medieval::-webkit-scrollbar-track {
  background: #1f3e5c;
}

.theme-medieval::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #b79739, #6b0f1a);
  border-radius: 6px;
}

.theme-medieval::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #d4af37, #8b0000);
}

/* Medieval text selection */
.theme-medieval ::selection {
  background: rgba(183, 151, 57, 0.3);
  color: #b79739;
}

/* Medieval ornate borders */
.ornate-border {
  position: relative;
}

.ornate-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #b79739, #6b0f1a, #1f3e5c, #b79739);
  border-radius: inherit;
  z-index: -1;
  opacity: 0.7;
}

/* Medieval card hover effects */
.theme-medieval .card-medieval:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 20px 40px rgba(183, 151, 57, 0.3);
}

/* Medieval button enhancements */
.theme-medieval .btn-medieval {
  background: linear-gradient(135deg, #b79739 0%, #d4af37 50%, #b79739 100%);
  border: 2px solid #d4af37;
  color: #6b0f1a;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.theme-medieval .btn-medieval::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.theme-medieval .btn-medieval:hover::before {
  left: 100%;
}

.theme-medieval .btn-medieval:hover {
  background: linear-gradient(135deg, #d4af37 0%, #ffd700 50%, #d4af37 100%);
  box-shadow: 0 8px 25px rgba(183, 151, 57, 0.4);
  transform: translateY(-2px);
}

/* Medieval layout adjustments */
.theme-medieval .bg-primary-600 {
  background-color: #b79739 !important;
}

.theme-medieval .bg-secondary-600 {
  background-color: #6b0f1a !important;
}

.theme-medieval .text-primary-600 {
  color: #b79739 !important;
}

.theme-medieval .text-secondary-600 {
  color: #6b0f1a !important;
}

.theme-medieval .border-primary-600 {
  border-color: #b79739 !important;
}

/* Medieval spacing and typography adjustments */
.theme-medieval h1,
.theme-medieval h2,
.theme-medieval h3,
.theme-medieval h4,
.theme-medieval h5,
.theme-medieval h6 {
  font-family: var(--font-display);
  letter-spacing: 0.5px;
}

.theme-medieval p,
.theme-medieval span,
.theme-medieval div {
  font-family: var(--font-serif);
}

/* Medieval navigation enhancements */
.theme-medieval .nav-link {
  position: relative;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

.theme-medieval .nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #b79739, #d4af37);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.theme-medieval .nav-link:hover::after,
.theme-medieval .nav-link.active::after {
  width: 100%;
}

/* Medieval form styling */
.theme-medieval .form-input {
  border: 2px solid rgba(183, 151, 57, 0.3);
  background: rgba(31, 62, 92, 0.1);
  color: #b79739;
}

.theme-medieval .form-input:focus {
  border-color: #b79739;
  box-shadow: 0 0 0 3px rgba(183, 151, 57, 0.1);
}

.theme-medieval .form-input::placeholder {
  color: rgba(183, 151, 57, 0.6);
}

/* Medieval modal and popup styling */
.theme-medieval .modal-overlay {
  background: rgba(31, 62, 92, 0.8);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

.theme-medieval .modal-content {
  background: linear-gradient(135deg, #1f3e5c 0%, #6b0f1a 100%);
  border: 2px solid #b79739;
  box-shadow: 0 25px 50px rgba(183, 151, 57, 0.3);
}

.mx-auto.max-w-8xl.px-2.sm\:px-3.lg\:px-4.xl\:px-6 {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

/* Hide debug information overlays */
.debug-overlay,
[class*="debug-panel"],
[class*="device-detection"],
div[style*="position: fixed"][style*="z-index"]:has-text("Device Detection"),
div[style*="position: fixed"][style*="z-index"]:has-text("Debug"),
div[style*="position: fixed"][style*="z-index"]:has-text("Detected:"),
div[style*="position: fixed"][style*="z-index"]:has-text("Method:"),
div[style*="position: fixed"][style*="z-index"]:has-text("Browser:"),
div[style*="position: fixed"][style*="z-index"]:has-text("Performance:") {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Hide any fixed positioned debug elements */
body > div[style*="position: fixed"]:not(.header):not(.modal):not(.toast):not(.notification) {
  display: none !important;
}

/* Hide debug info that might contain device detection information */
.debug-info,
.device-info,
.performance-debug,
.detection-debug {
  display: none !important;
}
