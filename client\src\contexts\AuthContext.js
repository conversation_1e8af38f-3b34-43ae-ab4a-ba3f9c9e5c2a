import { createContext, useContext, useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';
const API_URL = `${API_BASE_URL}/api/v1`;

// API Helper Functions
const apiCall = async (endpoint, options = {}) => {
  const url = `${API_URL}${endpoint}`;
  const token = localStorage.getItem('auth_token');
  
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }
    
    return data;
  } catch (error) {
    console.error(`API call failed for ${endpoint}:`, error);
    throw error;
  }
};

// User Types
export const USER_TYPES = {
  FREELANCER: 'freelancer',
  CLIENT: 'client'
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem('auth_token');
      
      if (token) {
        try {
          // Verify token and get user data
          const response = await apiCall('/auth/me');
          if (response.success) {
            setUser(response.data);
            setIsAuthenticated(true);
          } else {
            // Invalid token, clear it
            localStorage.removeItem('auth_token');
          }
        } catch (error) {
          console.error('Token verification failed:', error);
          localStorage.removeItem('auth_token');
        }
      }
      
      setLoading(false);
    };

    initializeAuth();
  }, []);

  // Register function
  const register = async (userData) => {
    try {
      setLoading(true);
      
      const response = await apiCall('/auth/register-direct', {
        method: 'POST',
        body: JSON.stringify(userData),
      });

      if (response.success) {
        const { user: newUser, token } = response.data;
        
        // Store token
        localStorage.setItem('auth_token', token);
        
        // Update state
        setUser(newUser);
        setIsAuthenticated(true);
        
        toast.success('Registration successful!');
        
        return {
          success: true,
          user: newUser,
          needsEmailVerification: !newUser.emailVerified
        };
      } else {
        throw new Error(response.message || 'Registration failed');
      }
    } catch (error) {
      console.error('Registration error:', error);
      toast.error(error.message || 'Registration failed');
      return {
        success: false,
        error: error.message
      };
    } finally {
      setLoading(false);
    }
  };

  // Login function
  const login = async (email, password) => {
    try {
      setLoading(true);
      
      const response = await apiCall('/auth/login-direct', {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      });

      if (response.success) {
        const { user: loggedInUser, token } = response.data;
        
        // Store token
        localStorage.setItem('auth_token', token);
        
        // Update state
        setUser(loggedInUser);
        setIsAuthenticated(true);
        
        toast.success('Login successful!');
        
        return {
          success: true,
          user: loggedInUser,
          needsEmailVerification: !loggedInUser.emailVerified
        };
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error(error.message || 'Login failed');
      return {
        success: false,
        error: error.message
      };
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Clear token
      localStorage.removeItem('auth_token');
      
      // Update state
      setUser(null);
      setIsAuthenticated(false);
      
      toast.success('Logged out successfully');
      
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error: error.message };
    }
  };

  // Google login placeholder (can be implemented later if needed)
  const loginWithGoogle = async (userType) => {
    toast.error('Google login not implemented yet. Please use email/password.');
    return { success: false, error: 'Google login not implemented' };
  };

  // Email verification placeholder
  const sendVerificationEmail = async () => {
    toast.info('Email verification not implemented yet.');
    return { success: false, error: 'Email verification not implemented' };
  };

  // Password reset placeholder
  const resetPassword = async (email) => {
    toast.info('Password reset not implemented yet.');
    return { success: false, error: 'Password reset not implemented' };
  };

  // Update profile placeholder
  const updateUserProfile = async (profileData) => {
    toast.info('Profile update not implemented yet.');
    return { success: false, error: 'Profile update not implemented' };
  };

  // Email verification handlers (placeholders)
  const verifyEmail = async (code) => {
    toast.info('Email verification not implemented yet.');
    return { success: false, error: 'Email verification not implemented' };
  };

  const handleResendVerification = async () => {
    return await sendVerificationEmail();
  };

  const handleContinueVerification = () => {
    // Navigate to dashboard or appropriate page
    return { success: true };
  };

  const value = {
    user,
    loading,
    isAuthenticated,
    firebaseReady: true, // Always true since we're not using Firebase
    debugMode: false,
    toggleDebugMode: () => {},
    register,
    login,
    loginWithGoogle,
    logout,
    sendVerificationEmail,
    verifyEmail,
    resetPassword,
    updateUserProfile,
    USER_TYPES,
    showEmailVerification: false,
    verificationEmail: '',
    handleResendVerification,
    handleContinueVerification,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
