#!/usr/bin/env node

/**
 * Script to install dependencies for all services
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const services = [
  'shared',
  'api-gateway',
  'auth-service',
  'user-service',
  'project-service',
  'job-service',
  'chat-service',
  'search-service'
];

const log = (message, color = 'reset') => {
  const colors = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    cyan: '\x1b[36m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
};

const installService = (serviceName) => {
  return new Promise((resolve, reject) => {
    const servicePath = path.join(__dirname, '..', 'services', serviceName);
    
    if (!fs.existsSync(servicePath)) {
      log(`⚠️ Service directory not found: ${serviceName}`, 'yellow');
      resolve({ service: serviceName, status: 'skipped' });
      return;
    }

    if (!fs.existsSync(path.join(servicePath, 'package.json'))) {
      log(`⚠️ No package.json found for: ${serviceName}`, 'yellow');
      resolve({ service: serviceName, status: 'skipped' });
      return;
    }

    log(`📦 Installing dependencies for ${serviceName}...`, 'cyan');
    
    const child = spawn('npm', ['ci'], {
      cwd: servicePath,
      stdio: 'pipe',
      shell: process.platform === 'win32'
    });

    let output = '';
    
    child.stdout.on('data', (data) => {
      output += data.toString();
    });

    child.stderr.on('data', (data) => {
      output += data.toString();
    });

    child.on('close', (code) => {
      if (code === 0) {
        log(`✅ ${serviceName} dependencies installed successfully`, 'green');
        resolve({ service: serviceName, status: 'success' });
      } else {
        log(`❌ Failed to install dependencies for ${serviceName}`, 'red');
        console.error(output);
        resolve({ service: serviceName, status: 'failed', error: output });
      }
    });

    child.on('error', (error) => {
      log(`❌ Error installing ${serviceName}: ${error.message}`, 'red');
      reject({ service: serviceName, status: 'error', error: error.message });
    });
  });
};

const main = async () => {
  log('📦 Installing dependencies for all services...', 'cyan');
  log('===============================================', 'cyan');

  const results = [];
  
  for (const service of services) {
    try {
      const result = await installService(service);
      results.push(result);
    } catch (error) {
      results.push(error);
    }
  }

  log('\n📊 Installation Summary:', 'cyan');
  log('========================', 'cyan');
  
  const successful = results.filter(r => r.status === 'success');
  const failed = results.filter(r => r.status === 'failed' || r.status === 'error');
  const skipped = results.filter(r => r.status === 'skipped');

  log(`✅ Successful: ${successful.length}`, 'green');
  log(`❌ Failed: ${failed.length}`, 'red');
  log(`⚠️ Skipped: ${skipped.length}`, 'yellow');

  if (failed.length > 0) {
    log('\n❌ Failed services:', 'red');
    failed.forEach(f => log(`   • ${f.service}`, 'red'));
  }

  if (skipped.length > 0) {
    log('\n⚠️ Skipped services:', 'yellow');
    skipped.forEach(s => log(`   • ${s.service}`, 'yellow'));
  }

  log('\n🎉 Service installation completed!', 'green');
};

if (require.main === module) {
  main().catch(error => {
    log(`❌ Installation failed: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { installService };
