// Mock Data Service for VWork Guild Platform
// Realistic medieval-themed freelance data

// ================================
// USER DATA & GUILD SYSTEM
// ================================

export const mockUsers = [
  {
    id: 'user1',
    email: '<EMAIL>',
    displayName: '<PERSON> the Web Mage',
    photoURL:
      'https://ui-avatars.com/api/?name=<PERSON>+Mage&size=150&background=4F46E5&color=fff',
    userType: 'freelancer',
    guild: {
      level: 'master',
      title: 'Master of Web Sorcery',
      experiencePoints: 2850,
      joinDate: new Date('2023-01-15'),
      completedQuests: 47,
      successRate: 98,
    },
    profile: {
      bio: 'Crafting digital realms with React magic and Node.js alchemy. Specializing in responsive castles and PWA fortresses.',
      location: 'Ho Chi Minh City, Vietnam',
      hourlyRate: 45,
      skills: [
        'React',
        'Node.js',
        'MongoDB',
        'TypeScript',
        'GSAP',
        'Tailwind CSS',
      ],
      languages: ['Vietnamese', 'English', 'Japanese'],
      availability: 'available',
    },
    stats: {
      totalEarnings: 18500,
      activeProjects: 3,
      rating: 4.9,
      responseTime: '2 hours',
      completionRate: 98,
    },
  },
  {
    id: 'user2',
    email: '<EMAIL>',
    displayName: 'Sir Marcus the Designer',
    photoURL:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
    userType: 'freelancer',
    guild: {
      level: 'journeyman',
      title: 'Journeyman of Visual Arts',
      experiencePoints: 1200,
      joinDate: new Date('2023-06-10'),
      completedQuests: 23,
      successRate: 95,
    },
    profile: {
      bio: 'Creating stunning visual experiences through UI/UX design and brand identity. Adobe Creative Suite master.',
      location: 'Da Nang, Vietnam',
      hourlyRate: 35,
      skills: [
        'Figma',
        'Photoshop',
        'Illustrator',
        'UI/UX Design',
        'Branding',
        'Motion Graphics',
      ],
      languages: ['Vietnamese', 'English'],
      availability: 'busy',
    },
    stats: {
      totalEarnings: 12300,
      activeProjects: 5,
      rating: 4.8,
      responseTime: '4 hours',
      completionRate: 95,
    },
  },
  {
    id: 'client1',
    email: '<EMAIL>',
    displayName: 'Lady Catherine of StartupLand',
    photoURL:
      'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150',
    userType: 'client',
    guild: {
      level: 'lord',
      title: 'Noble Patron of Innovation',
      experiencePoints: 3200,
      joinDate: new Date('2022-11-20'),
      completedQuests: 0,
      successRate: 100,
    },
    profile: {
      bio: 'Building the future of e-commerce. Always seeking talented guild members for ambitious projects.',
      location: 'Singapore',
      company: 'InnovateTech Solutions',
      industry: 'Technology',
    },
    stats: {
      totalSpent: 45000,
      activeProjects: 2,
      rating: 4.7,
      responseTime: '1 hour',
      projectsPosted: 12,
    },
  },
];

// ================================
// PROJECT DATA
// ================================

export const mockProjects = [
  {
    id: 'proj1',
    title: 'E-commerce Enchantment Portal',
    description:
      'Seeking a master web sorcerer to craft a magnificent e-commerce castle with React magic. The portal shall include mystical features like real-time inventory tracking, payment processing through ancient gateways, and responsive design that adapts to all viewing crystals.',
    category: 'web-development',
    clientId: 'client1',
    client: {
      name: 'Lady Catherine of StartupLand',
      rating: 4.7,
      avatar:
        'https://ui-avatars.com/api/?name=Lady+Catherine&size=50&background=10B981&color=fff',
      company: 'InnovateTech Solutions',
      verified: true,
    },
    budget: {
      type: 'fixed',
      amount: 2500,
      currency: 'USD',
    },
    skills: ['React', 'Node.js', 'MongoDB', 'Stripe API', 'Tailwind CSS'],
    status: 'open',
    urgency: 'normal',
    isNDA: true,
    deadline: new Date('2024-03-15'),
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-12'),
    stats: {
      totalBids: 12,
      averageBid: 2200,
      viewCount: 89,
    },
    features: [
      'User authentication & profiles',
      'Product catalog with search & filters',
      'Shopping cart & wishlist',
      'Payment integration (Stripe)',
      'Admin dashboard',
      'Order tracking system',
      'Responsive design',
    ],
  },
  {
    id: 'proj2',
    title: 'Mobile App Enchantment for Food Delivery',
    description:
      'Noble guild seeks skilled mobile enchanters to forge a React Native application for swift food delivery across the kingdom. The app must include real-time tracking, payment spells, and intuitive navigation.',
    category: 'mobile-development',
    clientId: 'client1',
    client: {
      name: 'Lord William of TechRealm',
      rating: 4.5,
      avatar:
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50',
      company: 'FoodieKingdom Ltd',
      verified: true,
    },
    budget: {
      type: 'hourly',
      amount: 40,
      currency: 'USD',
      estimatedHours: 80,
    },
    skills: [
      'React Native',
      'Firebase',
      'Google Maps API',
      'Push Notifications',
    ],
    status: 'open',
    urgency: 'urgent',
    isNDA: false,
    deadline: new Date('2024-02-28'),
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-10'),
    stats: {
      totalBids: 8,
      averageBid: 3200,
      viewCount: 56,
    },
  },
  {
    id: 'proj3',
    title: 'Brand Identity Design Quest',
    description:
      'A mystical tech startup requires a master of visual arts to forge their complete brand identity. This sacred quest includes logo design, color palettes, typography selection, and brand guidelines.',
    category: 'design',
    clientId: 'client1',
    client: {
      name: 'Princess Sarah of Innovation',
      rating: 4.9,
      avatar:
        'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=50',
      company: 'MysticTech Ventures',
      verified: true,
    },
    budget: {
      type: 'fixed',
      amount: 1200,
      currency: 'USD',
    },
    skills: [
      'Logo Design',
      'Brand Identity',
      'Figma',
      'Illustrator',
      'Typography',
    ],
    status: 'in-progress',
    urgency: 'normal',
    isNDA: true,
    deadline: new Date('2024-03-01'),
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-11'),
    stats: {
      totalBids: 15,
      averageBid: 1100,
      viewCount: 123,
    },
    assignedTo: 'user2',
    progress: 65,
  },
  {
    id: 'proj4',
    title: 'Content Writing for Ancient Wisdom Blog',
    description:
      'Seeking scribes skilled in the ancient arts of SEO and content creation. Write compelling articles about technology trends, coding tutorials, and digital transformation.',
    category: 'writing',
    clientId: 'client1',
    client: {
      name: 'Master David the Wise',
      rating: 4.6,
      avatar: 'https://images.unsplash.com/photo-1556157382-97eda2d62296?w=50',
      company: 'TechWisdom Publications',
      verified: false,
    },
    budget: {
      type: 'per-piece',
      amount: 150,
      currency: 'USD',
      pieces: 10,
    },
    skills: ['Content Writing', 'SEO', 'Technical Writing', 'Research'],
    status: 'open',
    urgency: 'normal',
    isNDA: false,
    deadline: new Date('2024-04-15'),
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-12'),
    stats: {
      totalBids: 6,
      averageBid: 140,
      viewCount: 34,
    },
  },
  {
    id: 'proj5',
    title: 'Video Production for Product Launch',
    description:
      'Create epic promotional videos for our new SaaS product launch. Need 3 videos: product demo, customer testimonials, and animated explainer.',
    category: 'video',
    clientId: 'client1',
    client: {
      name: 'Duke Alexander of Marketing',
      rating: 4.4,
      avatar:
        'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=50',
      company: 'LaunchPad Studios',
      verified: true,
    },
    budget: {
      type: 'fixed',
      amount: 3500,
      currency: 'USD',
    },
    skills: [
      'Video Editing',
      'Motion Graphics',
      'After Effects',
      'Premiere Pro',
    ],
    status: 'open',
    urgency: 'urgent',
    isNDA: true,
    deadline: new Date('2024-02-20'),
    createdAt: new Date('2024-01-11'),
    updatedAt: new Date('2024-01-11'),
    stats: {
      totalBids: 4,
      averageBid: 3200,
      viewCount: 28,
    },
  },
];

// ================================
// DASHBOARD DATA
// ================================

export const mockDashboardData = {
  currentUser: mockUsers[0], // Elena the Web Mage
  stats: {
    totalEarnings: 18500,
    thisMonthEarnings: 4200,
    activeProjects: 3,
    completedProjects: 47,
    averageRating: 4.9,
    responseTime: '2 hours',
    successRate: 98,
    totalBids: 156,
    acceptedBids: 47,
  },
  recentActivity: [
    {
      id: 'act1',
      type: 'project_completed',
      title: 'Quest Complete: Restaurant Website',
      description:
        'Successfully delivered responsive website for Golden Dragon Restaurant',
      timestamp: new Date('2024-01-11T14:30:00'),
      amount: 1800,
      status: 'positive',
    },
    {
      id: 'act2',
      type: 'bid_accepted',
      title: 'Bid Accepted: E-commerce Portal',
      description:
        'Your proposal for the e-commerce enchantment project has been accepted!',
      timestamp: new Date('2024-01-10T09:15:00'),
      amount: 2500,
      status: 'positive',
    },
    {
      id: 'act3',
      type: 'payment_received',
      title: 'Payment Received',
      description: 'Milestone payment for Mobile App Development project',
      timestamp: new Date('2024-01-09T16:45:00'),
      amount: 1200,
      status: 'positive',
    },
    {
      id: 'act4',
      type: 'new_message',
      title: 'New Message from Client',
      description: 'Lady Catherine sent updates on project requirements',
      timestamp: new Date('2024-01-08T11:20:00'),
      status: 'neutral',
    },
    {
      id: 'act5',
      type: 'review_received',
      title: 'Received 5-Star Review',
      description:
        '"Elena delivered exceptional work on our website. Highly recommended!"',
      timestamp: new Date('2024-01-07T13:10:00'),
      status: 'positive',
    },
  ],
  activeProjects: [
    {
      id: 'proj1',
      title: 'E-commerce Enchantment Portal',
      client: 'Lady Catherine of StartupLand',
      progress: 25,
      deadline: new Date('2024-03-15'),
      budget: 2500,
      status: 'on-track',
    },
    {
      id: 'proj6',
      title: 'Mobile Banking App UI/UX',
      client: 'Sir Thomas of FinTech',
      progress: 60,
      deadline: new Date('2024-02-28'),
      budget: 3200,
      status: 'on-track',
    },
    {
      id: 'proj7',
      title: 'SaaS Dashboard Redesign',
      client: 'Duchess Emma of Analytics',
      progress: 80,
      deadline: new Date('2024-02-15'),
      budget: 1800,
      status: 'ahead',
    },
  ],
  upcomingDeadlines: [
    {
      projectId: 'proj7',
      title: 'SaaS Dashboard Redesign',
      deadline: new Date('2024-02-15'),
      daysLeft: 5,
      priority: 'high',
    },
    {
      projectId: 'proj6',
      title: 'Mobile Banking App UI/UX',
      deadline: new Date('2024-02-28'),
      daysLeft: 18,
      priority: 'medium',
    },
    {
      projectId: 'proj1',
      title: 'E-commerce Enchantment Portal',
      deadline: new Date('2024-03-15'),
      daysLeft: 34,
      priority: 'low',
    },
  ],
  guildProgress: {
    currentLevel: 'master',
    currentXP: 2850,
    nextLevelXP: 3500,
    progressToNext: 82,
    badges: [
      {
        id: 'web-master',
        name: 'Web Sorcery Master',
        icon: '🧙‍♀️',
        earned: true,
      },
      {
        id: 'fast-responder',
        name: 'Swift Messenger',
        icon: '⚡',
        earned: true,
      },
      {
        id: 'quality-work',
        name: 'Quality Craftsman',
        icon: '⭐',
        earned: true,
      },
      {
        id: 'client-favorite',
        name: 'Client Champion',
        icon: '💎',
        earned: true,
      },
      { id: 'mentor', name: 'Guild Mentor', icon: '🎓', earned: false },
    ],
    weeklyGoals: {
      bidsSent: { current: 3, target: 5, progress: 60 },
      projectsCompleted: { current: 1, target: 2, progress: 50 },
      clientRating: { current: 4.9, target: 4.8, progress: 100 },
    },
  },
};

// ================================
// SETTINGS DATA
// ================================

export const mockSettingsData = {
  profile: {
    avatar:
      'https://images.unsplash.com/photo-1494790108755-2616b612b3fd?w=150',
    displayName: 'Elena the Web Mage',
    bio: 'Crafting digital realms with React magic and Node.js alchemy. Specializing in responsive castles and PWA fortresses.',
    location: 'Ho Chi Minh City, Vietnam',
    website: 'https://elena-webmage.dev',
    hourlyRate: 45,
    skills: [
      'React',
      'Node.js',
      'MongoDB',
      'TypeScript',
      'GSAP',
      'Tailwind CSS',
    ],
    languages: ['Vietnamese', 'English', 'Japanese'],
    portfolio: [
      {
        id: 'port1',
        title: 'E-commerce Fashion Store',
        description: 'Modern e-commerce platform with React & Node.js',
        image:
          'https://picsum.photos/300/200?random=1',
        url: 'https://fashion-store-demo.com',
        technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      },
      {
        id: 'port2',
        title: 'Restaurant Management System',
        description: 'Complete POS and management system for restaurants',
        image:
          'https://picsum.photos/300/200?random=2',
        url: 'https://restaurant-system-demo.com',
        technologies: ['React', 'Express', 'PostgreSQL', 'Real-time'],
      },
    ],
  },
  account: {
    email: '<EMAIL>',
    emailVerified: true,
    phoneNumber: '+84 123 456 789',
    phoneVerified: false,
    twoFactorEnabled: false,
    lastLogin: new Date('2024-01-12T08:30:00'),
    accountCreated: new Date('2023-01-15T10:00:00'),
  },
  preferences: {
    language: 'vi',
    timezone: 'Asia/Ho_Chi_Minh',
    theme: 'medieval',
    themeMode: 'light', // 'light' or 'dark'
    emailNotifications: {
      newProjects: true,
      bidAccepted: true,
      messages: true,
      payments: true,
      newsletter: false,
    },
    pushNotifications: {
      enabled: true,
      newMessages: true,
      projectUpdates: true,
      deadlineReminders: true,
    },
    privacy: {
      profileVisible: true,
      showRating: true,
      showEarnings: false,
      showLastSeen: true,
    },
  },
  payment: {
    methods: [
      {
        id: 'pay1',
        type: 'bank',
        name: 'Vietcombank',
        accountNumber: '****1234',
        isDefault: true,
        verified: true,
      },
      {
        id: 'pay2',
        type: 'paypal',
        email: '<EMAIL>',
        isDefault: false,
        verified: true,
      },
    ],
    earnings: {
      totalEarnings: 18500,
      availableBalance: 2340,
      pendingBalance: 1200,
      withdrawnThisMonth: 3500,
    },
  },
};

// ================================
// MESSAGES & CONVERSATIONS
// ================================

export const mockConversations = [
  {
    id: 'conv1',
    participants: ['user1', 'client1'],
    projectId: 'proj1',
    projectTitle: 'E-commerce Enchantment Portal',
    lastMessage: {
      content:
        'The wireframes look fantastic! Can we schedule a call to discuss the database schema?',
      timestamp: new Date('2024-01-12T14:30:00'),
      senderId: 'client1',
    },
    unreadCount: 2,
    updatedAt: new Date('2024-01-12T14:30:00'),
  },
  {
    id: 'conv2',
    participants: ['user1', 'user2'],
    projectId: null,
    projectTitle: null,
    lastMessage: {
      content:
        'Hey! Would you like to collaborate on a React Native project? I could use a UI/UX expert.',
      timestamp: new Date('2024-01-11T16:45:00'),
      senderId: 'user1',
    },
    unreadCount: 0,
    updatedAt: new Date('2024-01-11T18:20:00'),
  },
];

export const mockMessages = {
  conv1: [
    {
      id: 'msg1',
      senderId: 'client1',
      content:
        'Hello Elena! I reviewed your proposal for the e-commerce project. Very impressive work history!',
      timestamp: new Date('2024-01-10T09:30:00'),
      type: 'text',
    },
    {
      id: 'msg2',
      senderId: 'user1',
      content:
        "Thank you! I'm excited about this project. I've worked on similar e-commerce platforms before.",
      timestamp: new Date('2024-01-10T10:15:00'),
      type: 'text',
    },
    {
      id: 'msg3',
      senderId: 'user1',
      content:
        "I've prepared some initial wireframes based on your requirements. Would you like me to share them?",
      timestamp: new Date('2024-01-12T11:20:00'),
      type: 'text',
    },
    {
      id: 'msg4',
      senderId: 'client1',
      content:
        'The wireframes look fantastic! Can we schedule a call to discuss the database schema?',
      timestamp: new Date('2024-01-12T14:30:00'),
      type: 'text',
    },
  ],
};

// ================================
// COMMUNITY DATA
// ================================

export const mockCommunityPosts = [
  {
    id: 'post1',
    author: {
      id: 'user1',
      name: 'Elena the Web Mage',
      avatar:
        'https://images.unsplash.com/photo-1494790108755-2616b612b3fd?w=50',
      title: 'Master of Web Sorcery',
    },
    content:
      'Just finished implementing a complex React component library! The feeling when everything clicks into place is magical ✨',
    timestamp: new Date('2024-01-12T10:30:00'),
    likes: 24,
    comments: 8,
    tags: ['react', 'frontend', 'components'],
    type: 'text',
  },
  {
    id: 'post2',
    author: {
      id: 'user2',
      name: 'Sir Marcus the Designer',
      avatar:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50',
      title: 'Journeyman of Visual Arts',
    },
    content:
      "Pro tip: Always start your design process with user research. Beautiful designs that don't solve real problems are just pretty pictures 🎨",
    timestamp: new Date('2024-01-11T15:45:00'),
    likes: 18,
    comments: 5,
    tags: ['design', 'ux', 'tips'],
    type: 'text',
  },
];

// ================================
// EXPORT FUNCTIONS
// ================================

export const mockDataService = {
  // Users
  getUsers: () => Promise.resolve(mockUsers),
  getUserById: id => Promise.resolve(mockUsers.find(u => u.id === id)),

  // Projects
  getProjects: (filters = {}) => {
    let filtered = [...mockProjects];

    if (filters.category && filters.category !== 'All Quests') {
      filtered = filtered.filter(
        p => p.category === filters.category.toLowerCase().replace(' ', '-')
      );
    }

    if (filters.limit) {
      filtered = filtered.slice(0, filters.limit);
    }

    return Promise.resolve(filtered);
  },
  getProjectById: id => Promise.resolve(mockProjects.find(p => p.id === id)),

  // Dashboard
  getDashboardData: () => Promise.resolve(mockDashboardData),

  // Settings
  getSettingsData: () => Promise.resolve(mockSettingsData),
  updateSettings: data => {
    // Simulate API call
    return new Promise(resolve => {
      setTimeout(() => resolve({ success: true, data }), 500);
    });
  },

  // Messages
  getConversations: () => Promise.resolve(mockConversations),
  getMessages: conversationId =>
    Promise.resolve(mockMessages[conversationId] || []),

  // Community
  getCommunityPosts: () => Promise.resolve(mockCommunityPosts),
};
