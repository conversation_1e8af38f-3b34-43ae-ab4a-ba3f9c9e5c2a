#!/usr/bin/env node

/**
 * Environment Variables Generator for VWork Render Deployment
 * Generates ready-to-use environment variable configurations for each service
 */

const fs = require('fs');
const path = require('path');

const log = (message, color = 'reset') => {
  const colors = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    blue: '\x1b[34m',
    yellow: '\x1b[33m',
    cyan: '\x1b[36m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Base configuration templates
const GLOBAL_VARS = {
  NODE_ENV: 'production',
  JWT_SECRET: '[REPLACE_WITH_YOUR_JWT_SECRET]',
  CORS_ORIGINS: 'https://vwork-client.onrender.com'
};

const FIREBASE_VARS = {
  FIREBASE_PROJECT_ID: '[REPLACE_WITH_YOUR_PROJECT_ID]',
  FIREBASE_CLIENT_EMAIL: '[REPLACE_WITH_SERVICE_ACCOUNT_EMAIL]',
  FIREBASE_PRIVATE_KEY: '[REPLACE_WITH_SERVICE_ACCOUNT_PRIVATE_KEY]',
  FIREBASE_DATABASE_URL: '[REPLACE_WITH_DATABASE_URL]'
};

const SERVICE_URLS = {
  AUTH_SERVICE_URL: 'https://vwork-auth-service.onrender.com',
  USER_SERVICE_URL: 'https://vwork-user-service.onrender.com',
  PROJECT_SERVICE_URL: 'https://vwork-project-service.onrender.com',
  JOB_SERVICE_URL: 'https://vwork-job-service.onrender.com',
  CHAT_SERVICE_URL: 'https://vwork-chat-service.onrender.com',
  SEARCH_SERVICE_URL: 'https://vwork-search-service.onrender.com',
  GATEWAY_URL: 'https://vwork-gateway.onrender.com'
};

const SERVICE_CONFIGS = {
  'api-gateway': {
    port: 8080,
    specificVars: {
      ...SERVICE_URLS,
      RATE_LIMIT_WINDOW_MS: '900000',
      RATE_LIMIT_MAX_REQUESTS: '100'
    }
  },
  'auth-service': {
    port: 3001,
    specificVars: {
      USER_SERVICE_URL: SERVICE_URLS.USER_SERVICE_URL,
      SESSION_TIMEOUT: '3600000',
      PASSWORD_SALT_ROUNDS: '12'
    }
  },
  'user-service': {
    port: 3002,
    specificVars: {
      AUTH_SERVICE_URL: SERVICE_URLS.AUTH_SERVICE_URL,
      UPLOAD_MAX_SIZE: '10485760',
      PROFILE_IMAGE_BUCKET: 'vwork-profile-images'
    }
  },
  'project-service': {
    port: 3003,
    specificVars: {
      AUTH_SERVICE_URL: SERVICE_URLS.AUTH_SERVICE_URL,
      USER_SERVICE_URL: SERVICE_URLS.USER_SERVICE_URL,
      MAX_PROJECT_IMAGES: '5',
      PROJECT_CACHE_TTL: '3600'
    }
  },
  'job-service': {
    port: 3004,
    specificVars: {
      AUTH_SERVICE_URL: SERVICE_URLS.AUTH_SERVICE_URL,
      USER_SERVICE_URL: SERVICE_URLS.USER_SERVICE_URL,
      PROJECT_SERVICE_URL: SERVICE_URLS.PROJECT_SERVICE_URL,
      JOB_EXPIRY_DAYS: '30',
      MAX_APPLICATIONS_PER_JOB: '100'
    }
  },
  'chat-service': {
    port: 3005,
    specificVars: {
      AUTH_SERVICE_URL: SERVICE_URLS.AUTH_SERVICE_URL,
      USER_SERVICE_URL: SERVICE_URLS.USER_SERVICE_URL,
      MESSAGE_RETENTION_DAYS: '365',
      MAX_MESSAGE_LENGTH: '1000',
      WEBSOCKET_ENABLED: 'true'
    }
  },
  'payment-service': {
    port: 3006,
    specificVars: {
      AUTH_SERVICE_URL: SERVICE_URLS.AUTH_SERVICE_URL,
      USER_SERVICE_URL: SERVICE_URLS.USER_SERVICE_URL,
      STRIPE_SECRET_KEY: '[REPLACE_WITH_STRIPE_SECRET]',
      PAYMENT_WEBHOOK_SECRET: '[REPLACE_WITH_WEBHOOK_SECRET]'
    }
  },
  'search-service': {
    port: 3009,
    specificVars: {
      ...SERVICE_URLS,
      SEARCH_INDEX_UPDATE_INTERVAL: '300000',
      MAX_SEARCH_RESULTS: '50',
      SEARCH_CACHE_TTL: '1800'
    }
  }
};

const CLIENT_CONFIG = {
  REACT_APP_API_URL: 'https://vwork-gateway.onrender.com/api/v1',
  REACT_APP_FIREBASE_CONFIG: '[REPLACE_WITH_FIREBASE_CONFIG_JSON]',
  REACT_APP_ENVIRONMENT: 'production',
  REACT_APP_VERSION: '1.0.0',
  GENERATE_SOURCEMAP: 'false',
  DISABLE_ESLINT_PLUGIN: 'true'
};

const generateServiceEnvVars = (serviceName, config) => {
  const envVars = {
    ...GLOBAL_VARS,
    PORT: config.port,
    ...FIREBASE_VARS,
    ...config.specificVars
  };

  let envContent = `# Environment Variables for ${serviceName}\n`;
  envContent += `# Copy and paste these into Render service settings\n\n`;

  // Group variables by category
  envContent += `# Basic Configuration\n`;
  envContent += `NODE_ENV=${envVars.NODE_ENV}\n`;
  envContent += `PORT=${envVars.PORT}\n\n`;

  envContent += `# Security\n`;
  envContent += `JWT_SECRET=${envVars.JWT_SECRET}\n`;
  envContent += `CORS_ORIGINS=${envVars.CORS_ORIGINS}\n\n`;

  envContent += `# Firebase Configuration\n`;
  Object.entries(FIREBASE_VARS).forEach(([key, value]) => {
    envContent += `${key}=${value}\n`;
  });
  envContent += '\n';

  // Service-specific variables
  if (Object.keys(config.specificVars).length > 0) {
    envContent += `# Service-Specific Configuration\n`;
    Object.entries(config.specificVars).forEach(([key, value]) => {
      if (!Object.keys(SERVICE_URLS).includes(key) && !Object.keys(FIREBASE_VARS).includes(key)) {
        envContent += `${key}=${value}\n`;
      }
    });
    envContent += '\n';
  }

  // Service URLs (if needed)
  const serviceUrls = Object.entries(config.specificVars).filter(([key]) => 
    Object.keys(SERVICE_URLS).includes(key)
  );
  
  if (serviceUrls.length > 0) {
    envContent += `# Service Dependencies\n`;
    serviceUrls.forEach(([key, value]) => {
      envContent += `${key}=${value}\n`;
    });
    envContent += '\n';
  }

  return envContent;
};

const generateClientEnvVars = () => {
  let envContent = `# Environment Variables for React Client\n`;
  envContent += `# Copy and paste these into Render static site settings\n\n`;

  envContent += `# API Configuration\n`;
  envContent += `REACT_APP_API_URL=${CLIENT_CONFIG.REACT_APP_API_URL}\n`;
  envContent += `REACT_APP_FIREBASE_CONFIG=${CLIENT_CONFIG.REACT_APP_FIREBASE_CONFIG}\n\n`;

  envContent += `# Build Configuration\n`;
  envContent += `REACT_APP_ENVIRONMENT=${CLIENT_CONFIG.REACT_APP_ENVIRONMENT}\n`;
  envContent += `REACT_APP_VERSION=${CLIENT_CONFIG.REACT_APP_VERSION}\n`;
  envContent += `GENERATE_SOURCEMAP=${CLIENT_CONFIG.GENERATE_SOURCEMAP}\n`;
  envContent += `DISABLE_ESLINT_PLUGIN=${CLIENT_CONFIG.DISABLE_ESLINT_PLUGIN}\n\n`;

  return envContent;
};

const generateInstructions = () => {
  let instructions = `# VWork Environment Variables - Setup Instructions\n\n`;
  
  instructions += `## 🔧 Before You Start\n\n`;
  instructions += `Replace the following placeholders with your actual values:\n\n`;
  instructions += `### Firebase Configuration\n`;
  instructions += `- \`[REPLACE_WITH_YOUR_PROJECT_ID]\` - Your Firebase project ID\n`;
  instructions += `- \`[REPLACE_WITH_SERVICE_ACCOUNT_EMAIL]\` - Service account email from Firebase\n`;
  instructions += `- \`[REPLACE_WITH_SERVICE_ACCOUNT_PRIVATE_KEY]\` - Service account private key\n`;
  instructions += `- \`[REPLACE_WITH_DATABASE_URL]\` - Firestore database URL\n`;
  instructions += `- \`[REPLACE_WITH_FIREBASE_CONFIG_JSON]\` - Complete Firebase config object as JSON string\n\n`;
  
  instructions += `### Security\n`;
  instructions += `- \`[REPLACE_WITH_YOUR_JWT_SECRET]\` - Generate a strong random string (min 32 characters)\n\n`;
  
  instructions += `## 📝 How to Use These Files\n\n`;
  instructions += `1. **Generate JWT Secret**:\n`;
  instructions += `   \`\`\`bash\n`;
  instructions += `   node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"\n`;
  instructions += `   \`\`\`\n\n`;
  
  instructions += `2. **Get Firebase Config**:\n`;
  instructions += `   - Go to Firebase Console > Project Settings > Service Accounts\n`;
  instructions += `   - Generate new private key\n`;
  instructions += `   - Copy the JSON content\n\n`;
  
  instructions += `3. **Set Variables in Render**:\n`;
  instructions += `   - Open each service in Render dashboard\n`;
  instructions += `   - Go to Environment tab\n`;
  instructions += `   - Copy variables from respective .env file\n`;
  instructions += `   - Replace placeholders with actual values\n\n`;
  
  instructions += `## ⚠️ Important Notes\n\n`;
  instructions += `- Never commit these files with real values to Git\n`;
  instructions += `- Use strong, unique values for JWT_SECRET\n`;
  instructions += `- Keep Firebase private keys secure\n`;
  instructions += `- Update CORS_ORIGINS if using custom domain\n\n`;
  
  instructions += `## 🔄 Deployment Order\n\n`;
  instructions += `Deploy services in this order to ensure dependencies are available:\n\n`;
  instructions += `1. auth-service\n`;
  instructions += `2. user-service\n`;
  instructions += `3. project-service\n`;
  instructions += `4. job-service\n`;
  instructions += `5. chat-service\n`;
  instructions += `6. search-service\n`;
  instructions += `7. api-gateway\n`;
  instructions += `8. client (React app)\n\n`;
  
  return instructions;
};

const main = () => {
  log('🔧 Generating Environment Variables for VWork Deployment', 'cyan');
  log('=========================================================', 'cyan');

  const envDir = path.join(process.cwd(), 'deployment-env-vars');
  
  // Create directory if it doesn't exist
  if (!fs.existsSync(envDir)) {
    fs.mkdirSync(envDir, { recursive: true });
    log(`📁 Created directory: ${envDir}`, 'green');
  }

  // Generate environment files for each service
  log('\n📝 Generating service environment files...', 'blue');
  
  Object.entries(SERVICE_CONFIGS).forEach(([serviceName, config]) => {
    const envContent = generateServiceEnvVars(serviceName, config);
    const filePath = path.join(envDir, `${serviceName}.env`);
    
    fs.writeFileSync(filePath, envContent);
    log(`✅ Generated: ${serviceName}.env`, 'green');
  });

  // Generate client environment file
  const clientEnvContent = generateClientEnvVars();
  const clientFilePath = path.join(envDir, 'client.env');
  fs.writeFileSync(clientFilePath, clientEnvContent);
  log(`✅ Generated: client.env`, 'green');

  // Generate instructions
  const instructionsContent = generateInstructions();
  const instructionsPath = path.join(envDir, 'SETUP_INSTRUCTIONS.md');
  fs.writeFileSync(instructionsPath, instructionsContent);
  log(`✅ Generated: SETUP_INSTRUCTIONS.md`, 'green');

  // Generate summary
  log('\n📋 Summary:', 'blue');
  log(`   📁 Environment files location: ${envDir}`, 'cyan');
  log(`   📄 Files generated: ${Object.keys(SERVICE_CONFIGS).length + 2}`, 'cyan');
  log(`   📖 Instructions: SETUP_INSTRUCTIONS.md`, 'cyan');

  log('\n🚀 Next Steps:', 'yellow');
  log('1. Review SETUP_INSTRUCTIONS.md', 'yellow');
  log('2. Replace all placeholder values with actual configuration', 'yellow');
  log('3. Copy environment variables to Render service settings', 'yellow');
  log('4. Deploy services in the specified order', 'yellow');

  log('\n⚠️  Important: Never commit the env files with real values to Git!', 'yellow');
  
  // Add to .gitignore
  const gitignorePath = path.join(process.cwd(), '.gitignore');
  let gitignoreContent = '';
  
  if (fs.existsSync(gitignorePath)) {
    gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
  }
  
  if (!gitignoreContent.includes('deployment-env-vars/')) {
    gitignoreContent += '\n# Deployment environment variables\ndeployment-env-vars/\n';
    fs.writeFileSync(gitignorePath, gitignoreContent);
    log('✅ Added deployment-env-vars/ to .gitignore', 'green');
  }

  log('\n🎉 Environment variable generation complete!', 'green');
};

if (require.main === module) {
  main();
}

module.exports = { generateServiceEnvVars, generateClientEnvVars, SERVICE_CONFIGS, CLIENT_CONFIG };
