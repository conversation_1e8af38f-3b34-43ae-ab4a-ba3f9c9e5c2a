import { useEffect, useRef } from 'react';
import { animationController } from '../utils/animations';

/**
 * Custom hook for consistent animation management
 */
export const useAnimation = () => {
  const elementRef = useRef(null);

  const animateIn = (options = {}) => {
    if (elementRef.current) {
      return animationController.fadeIn(elementRef.current, options);
    }
  };

  const animateScale = (options = {}) => {
    if (elementRef.current) {
      return animationController.scaleIn(elementRef.current, options);
    }
  };

  const animateParallax = (options = {}) => {
    if (elementRef.current) {
      return animationController.parallax(elementRef.current, options);
    }
  };

  const animateText = (options = {}) => {
    if (elementRef.current) {
      return animationController.textReveal(elementRef.current, options);
    }
  };

  const animateCard = () => {
    if (elementRef.current) {
      return animationController.cardHover(elementRef.current);
    }
  };

  const animateGrid = (options = {}) => {
    if (elementRef.current) {
      const elements = elementRef.current.querySelectorAll('[data-animate]');
      return animationController.staggerGrid(elements, options);
    }
  };

  useEffect(() => {
    return () => {
      if (elementRef.current) {
        animationController.cleanup(elementRef.current);
      }
    };
  }, []);

  return {
    elementRef,
    animateIn,
    animateScale,
    animateParallax,
    animateText,
    animateCard,
    animateGrid,
    controller: animationController,
  };
};

export default useAnimation; 