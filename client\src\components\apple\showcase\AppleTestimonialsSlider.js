import { useRef, useEffect, useState, useCallback } from 'react';
import { gsap } from 'gsap';
import { useLanguage } from '../../../contexts/LanguageContext';
import {
  StarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';
import { animationController } from '../../../utils/animations';

const AppleTestimonialsSlider = () => {
  const { t } = useLanguage();
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const sliderRef = useRef(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      role: 'CEO, TechStartup Inc.',
      avatar:
        'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&size=100&background=8B5CF6&color=fff',
      rating: 5,
      content: t('testimonial1'),
      project: t('mobileAppDevelopment'),
      duration: t('months', { count: 6 }),
      budget: '$45,000',
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'Marketing Director, GrowthCorp',
      avatar:
        'https://ui-avatars.com/api/?name=Michael+Chen&size=100&background=10B981&color=fff',
      rating: 5,
      content: t('testimonial2'),
      project: t('digitalMarketingCampaign'),
      duration: t('months', { count: 3 }),
      budget: '$25,000',
    },
    {
      id: 3,
      name: 'Sarah Rodriguez',
      role: 'Product Manager, InnovateLab',
      avatar:
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      content: t('testimonial3'),
      project: t('uiUxDesign'),
      duration: t('months', { count: 4 }),
      budget: '$30,000',
    },
    {
      id: 4,
      name: 'David Thompson',
      role: 'CTO, DataFlow Systems',
      avatar:
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      content: t('testimonial4'),
      project: t('fullStackDevelopment'),
      duration: t('months', { count: 8 }),
      budget: '$75,000',
    },
    {
      id: 5,
      name: 'Lisa Park',
      role: 'Founder, CreativeStudio',
      avatar:
        'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face',
      rating: 5,
      content: t('testimonial5'),
      project: t('brandIdentityDesign'),
      duration: t('months', { count: 2 }),
      budget: '$15,000',
    },
  ];

  const nextSlide = useCallback(() => {
    setCurrentIndex(prev => (prev + 1) % testimonials.length);
  }, [testimonials.length]);

  const prevSlide = () => {
    setCurrentIndex(
      prev => (prev - 1 + testimonials.length) % testimonials.length
    );
  };

  const goToSlide = index => {
    setCurrentIndex(index);
  };

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      animationController.fadeIn(titleRef.current, {
        scrollTrigger: {
          trigger: titleRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      });

      // Slider animation
      animationController.fadeIn(sliderRef.current, {
        delay: 0.3,
        scrollTrigger: {
          trigger: sliderRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      });
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  useEffect(() => {
    // Auto-slide every 6 seconds
    const interval = setInterval(() => {
      nextSlide();
    }, 6000);

    return () => clearInterval(interval);
  }, [nextSlide]);

  useEffect(() => {
    // Animate slide transition
    if (sliderRef.current) {
      const testimonialCards =
        sliderRef.current.querySelectorAll('.testimonial-card');

      gsap.to(testimonialCards, {
        x: -currentIndex * 100 + '%',
        duration: 0.8,
        ease: 'power2.out',
      });

      // Fade animation for content
      gsap.fromTo(
        testimonialCards[currentIndex]?.querySelector('.testimonial-content'),
        { opacity: 0, y: 20 },
        { opacity: 1, y: 0, duration: 0.6, delay: 0.2 }
      );
    }
  }, [currentIndex]);

  const renderStars = rating => {
    return Array.from({ length: 5 }, (_, index) => (
      <StarIcon
        key={index}
        className={`w-5 h-5 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <section ref={sectionRef} className='py-20 bg-white dark:bg-gray-900 transition-colors duration-300'>
      <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
        {/* Section Header */}
        <div ref={titleRef} className='text-center mb-16'>
          <h2 className='text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6 transition-colors duration-300'>
            {t('whatOurClients')}
            <span className='block text-blue-600 dark:text-blue-400'>{t('areSaying')}</span>
          </h2>
          <p className='text-lg sm:text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed transition-colors duration-300'>
            {t('dontJustTakeWord')}
          </p>
        </div>

        {/* Testimonials Slider */}
        <div className='relative'>
          {/* Navigation Buttons */}
          <button
            onClick={prevSlide}
            className='absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white dark:bg-gray-800 rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border border-gray-100 dark:border-gray-700'
          >
            <ChevronLeftIcon className='w-6 h-6 text-gray-600 dark:text-gray-400 transition-colors duration-200' />
          </button>

          <button
            onClick={nextSlide}
            className='absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white dark:bg-gray-800 rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border border-gray-100 dark:border-gray-700'
          >
            <ChevronRightIcon className='w-6 h-6 text-gray-600 dark:text-gray-400 transition-colors duration-200' />
          </button>

          {/* Slider Container */}
          <div
            ref={sliderRef}
            className='overflow-hidden rounded-2xl'
          >
            <div className='flex transition-transform duration-800 ease-out'>
              {testimonials.map((testimonial) => (
                <div
                  key={testimonial.id}
                  className='testimonial-card flex-shrink-0 w-full px-8 py-12'
                >
                  <div className='bg-white dark:bg-gray-800 rounded-3xl p-8 shadow-xl border border-gray-100 dark:border-gray-700 transition-all duration-300 hover:shadow-2xl'>
                    {/* Quote Icon */}
                    <div className='mb-6'>
                      <div className='inline-flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full'>
                        <ChatBubbleLeftRightIcon className='w-6 h-6 text-blue-600 dark:text-blue-400' />
                      </div>
                    </div>

                    {/* Content */}
                    <div className='testimonial-content mb-8'>
                      <p className='text-lg text-gray-700 dark:text-gray-300 leading-relaxed mb-6 transition-colors duration-300'>
                        "{testimonial.content}"
                      </p>

                      {/* Project Details */}
                      <div className='grid grid-cols-3 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl'>
                        <div className='text-center'>
                          <div className='text-sm font-medium text-gray-500 dark:text-gray-400 transition-colors duration-300'>
                            {t('project')}
                          </div>
                          <div className='text-sm font-semibold text-gray-900 dark:text-gray-100 transition-colors duration-300'>
                            {testimonial.project}
                          </div>
                        </div>
                        <div className='text-center'>
                          <div className='text-sm font-medium text-gray-500 dark:text-gray-400 transition-colors duration-300'>
                            {t('duration')}
                          </div>
                          <div className='text-sm font-semibold text-gray-900 dark:text-gray-100 transition-colors duration-300'>
                            {testimonial.duration}
                          </div>
                        </div>
                        <div className='text-center'>
                          <div className='text-sm font-medium text-gray-500 dark:text-gray-400 transition-colors duration-300'>
                            {t('budget')}
                          </div>
                          <div className='text-sm font-semibold text-gray-900 dark:text-gray-100 transition-colors duration-300'>
                            {testimonial.budget}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Author */}
                    <div className='flex items-center space-x-4'>
                      <div className='w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center overflow-hidden'>
                        {testimonial.avatar ? (
                          <img
                            src={testimonial.avatar}
                            alt={testimonial.name}
                            className='w-full h-full object-cover'
                          />
                        ) : (
                          <span className='text-lg font-semibold text-gray-600 dark:text-gray-400'>
                            {testimonial.name.charAt(0)}
                          </span>
                        )}
                      </div>
                      <div className='flex-1'>
                        <h4 className='font-semibold text-gray-900 dark:text-gray-100 transition-colors duration-300'>
                          {testimonial.name}
                        </h4>
                        <p className='text-sm text-gray-600 dark:text-gray-400 transition-colors duration-300'>
                          {testimonial.role}
                        </p>
                        <div className='flex items-center mt-1'>
                          {renderStars(testimonial.rating)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Dots Indicator */}
          <div className='flex justify-center mt-8 space-x-2'>
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-blue-600 dark:bg-blue-400 scale-125'
                    : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className='mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center'>
          <div>
            <div className='text-3xl font-bold text-blue-600 mb-2'>4.9/5</div>
            <p className='text-gray-600'>{t('averageRating')}</p>
          </div>
          <div>
            <div className='text-3xl font-bold text-blue-600 mb-2'>10,000+</div>
            <p className='text-gray-600'>{t('happyClients')}</p>
          </div>
          <div>
            <div className='text-3xl font-bold text-blue-600 mb-2'>98%</div>
            <p className='text-gray-600'>{t('projectSuccessRate')}</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AppleTestimonialsSlider;
