#!/usr/bin/env node

/**
 * Unified script to start all VWork services for CI/CD
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Environment-specific settings
const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = process.env.NODE_ENV === 'development';

// Service configurations
const services = [
  {
    name: 'API Gateway',
    path: 'services/api-gateway',
    port: 8080,
    command: 'npm',
    args: isDevelopment ? ['run', 'dev'] : ['start']
  },
  {
    name: 'Auth Service',
    path: 'services/auth-service',
    port: 3001,
    command: 'npm',
    args: isDevelopment ? ['run', 'dev'] : ['start']
  },
  {
    name: 'User Service',
    path: 'services/user-service',
    port: 3002,
    command: 'npm',
    args: isDevelopment ? ['run', 'dev'] : ['start']
  },
  {
    name: 'Project Service',
    path: 'services/project-service',
    port: 3003,
    command: 'npm',
    args: isDevelopment ? ['run', 'dev'] : ['start']
  },
  {
    name: 'Job Service',
    path: 'services/job-service',
    port: 3004,
    command: 'npm',
    args: isDevelopment ? ['run', 'dev'] : ['start']
  },
  {
    name: 'Chat Service',
    path: 'services/chat-service',
    port: 3005,
    command: 'npm',
    args: isDevelopment ? ['run', 'dev'] : ['start']
  },
  {
    name: 'Search Service',
    path: 'services/search-service',
    port: 3009,
    command: 'npm',
    args: isDevelopment ? ['run', 'dev'] : ['start']
  }
];

// Environment-specific settings (moved after services array)
// const isProduction = process.env.NODE_ENV === 'production';
// const isDevelopment = process.env.NODE_ENV === 'development';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
};

class ServiceManager {
  constructor() {
    this.processes = [];
    this.healthCheckInterval = null;
    this.setupSignalHandlers();
  }

  setupSignalHandlers() {
    process.on('SIGINT', () => this.gracefulShutdown());
    process.on('SIGTERM', () => this.gracefulShutdown());
    process.on('uncaughtException', (error) => {
      log(`Uncaught Exception: ${error.message}`, 'red');
      this.gracefulShutdown();
    });
  }

  async checkPortAvailability(port) {
    return new Promise((resolve) => {
      const net = require('net');
      const server = net.createServer();
      
      server.listen(port, () => {
        server.once('close', () => resolve(true));
        server.close();
      });
      
      server.on('error', () => resolve(false));
    });
  }

  async startService(service) {
    return new Promise((resolve, reject) => {
      const servicePath = path.resolve(__dirname, '..', service.path);
      
      // Check if service directory exists
      if (!fs.existsSync(servicePath)) {
        log(`❌ Service directory not found: ${servicePath}`, 'red');
        reject(new Error(`Service directory not found: ${servicePath}`));
        return;
      }

      log(`🚀 Starting ${service.name} on port ${service.port}...`, 'cyan');
      
      const child = spawn(service.command, service.args, {
        cwd: servicePath,
        stdio: 'pipe',
        shell: os.platform() === 'win32',
        detached: !isProduction,
        env: {
          ...process.env,
          PORT: service.port,
          NODE_ENV: process.env.NODE_ENV || 'development'
        }
      });

      let started = false;
      const startTimeout = setTimeout(() => {
        if (!started) {
          log(`⏰ ${service.name} startup timeout`, 'yellow');
          resolve({ service, process: child, status: 'timeout' });
        }
      }, 30000);

      child.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(`[${service.name}] ${output.trim()}`);
        
        // Check if service has started successfully
        if (output.includes(`running on port ${service.port}`) || 
            output.includes('Server running') ||
            output.includes('started') ||
            output.includes('listening')) {
          if (!started) {
            started = true;
            clearTimeout(startTimeout);
            log(`✅ ${service.name} started successfully`, 'green');
            resolve({ service, process: child, status: 'running' });
          }
        }
      });

      child.stderr.on('data', (data) => {
        const error = data.toString();
        console.error(`[${service.name}] ERROR: ${error.trim()}`);
      });

      child.on('error', (error) => {
        clearTimeout(startTimeout);
        log(`❌ Failed to start ${service.name}: ${error.message}`, 'red');
        reject(error);
      });

      child.on('exit', (code, signal) => {
        if (code !== 0 && code !== null) {
          log(`❌ ${service.name} exited with code ${code}`, 'red');
        } else if (signal) {
          log(`🛑 ${service.name} stopped by signal ${signal}`, 'yellow');
        } else {
          log(`🛑 ${service.name} stopped`, 'yellow');
        }
      });
    });
  }

  async healthCheck() {
    const axios = require('axios').default;
    
    for (const processInfo of this.processes) {
      if (processInfo.status === 'running') {
        try {
          const response = await axios.get(
            `http://localhost:${processInfo.service.port}/health`,
            { timeout: 5000 }
          );
          
          if (response.status !== 200) {
            log(`⚠️  ${processInfo.service.name} health check failed`, 'yellow');
          }
        } catch (error) {
          log(`❌ ${processInfo.service.name} is not responding`, 'red');
        }
      }
    }
  }

  startHealthChecks() {
    if (isProduction) {
      this.healthCheckInterval = setInterval(() => {
        this.healthCheck();
      }, 60000); // Check every minute in production
    }
  }

  async gracefulShutdown() {
    log('🛑 Initiating graceful shutdown...', 'yellow');
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // Stop all processes
    for (const processInfo of this.processes) {
      if (processInfo.process && processInfo.process.pid && !processInfo.process.killed) {
        log(`🛑 Stopping ${processInfo.service.name}...`, 'yellow');
        
        try {
          if (os.platform() === 'win32') {
            spawn('taskkill', ['/F', '/T', '/PID', processInfo.process.pid]);
          } else {
            processInfo.process.kill('SIGTERM');
          }
        } catch (error) {
          log(`Could not stop ${processInfo.service.name}: ${error.message}`, 'red');
        }
      }
    }

    setTimeout(() => {
      log('👋 All services stopped. Goodbye!', 'green');
      process.exit(0);
    }, 3000);
  }

  async start() {
    log('🎯 VWork Microservices Startup', 'bright');
    log('================================', 'bright');
    log(`Environment: ${process.env.NODE_ENV || 'development'}`, 'cyan');

    // Check port availability
    log('🔍 Checking port availability...', 'yellow');
    for (const service of services) {
      const available = await this.checkPortAvailability(service.port);
      if (!available) {
        log(`⚠️  Port ${service.port} is already in use (${service.name})`, 'yellow');
      }
    }

    // Start services
    for (const service of services) {
      try {
        const processInfo = await this.startService(service);
        this.processes.push(processInfo);
        
        // Wait between starting services in development
        if (isDevelopment) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      } catch (error) {
        log(`❌ Failed to start ${service.name}: ${error.message}`, 'red');
      }
    }

    // Display service status
    log('\n🎉 Service startup completed!', 'green');
    log('================================', 'bright');
    log('📋 Service Status:', 'cyan');
    
    this.processes.forEach(processInfo => {
      const status = processInfo.status === 'running' ? '✅' : '⚠️';
      log(`   ${status} ${processInfo.service.name}: http://localhost:${processInfo.service.port}`, 'blue');
    });

    log('\n🏥 Health Checks:', 'cyan');
    this.processes.forEach(processInfo => {
      log(`   • ${processInfo.service.name}: http://localhost:${processInfo.service.port}/health`, 'blue');
    });

    log('\n🌐 API Gateway: http://localhost:8080', 'green');
    log('📖 API Docs: http://localhost:8080/api/v1', 'green');

    // Start health checks
    this.startHealthChecks();

    // Keep the script running
    if (!isProduction) {
      log('\n💡 Press Ctrl+C to stop all services', 'cyan');
    }
  }
}

// Run the service manager
if (require.main === module) {
  const manager = new ServiceManager();
  manager.start().catch(error => {
    log(`❌ Startup failed: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = ServiceManager;
