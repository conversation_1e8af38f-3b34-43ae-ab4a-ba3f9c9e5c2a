import React from 'react';
import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';

const PasswordStrengthIndicator = ({ password }) => {
  const getPasswordStrength = (password) => {
    if (!password) return { score: 0, label: '', color: 'gray' };

    let score = 0;
    const checks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      numbers: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };

    score += checks.length ? 1 : 0;
    score += checks.lowercase ? 1 : 0;
    score += checks.uppercase ? 1 : 0;
    score += checks.numbers ? 1 : 0;
    score += checks.special ? 1 : 0;

    if (score <= 1) return { score, label: 'Rất yếu', color: 'red' };
    if (score <= 2) return { score, label: 'Yếu', color: 'orange' };
    if (score <= 3) return { score, label: 'Trung bình', color: 'yellow' };
    if (score <= 4) return { score, label: 'Mạnh', color: 'blue' };
    return { score, label: 'Rất mạnh', color: 'green' };
  };

  const strength = getPasswordStrength(password);

  const getColorClasses = (color) => {
    switch (color) {
      case 'red':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'orange':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'yellow':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'blue':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'green':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getProgressColor = (color) => {
    switch (color) {
      case 'red':
        return 'bg-red-500';
      case 'orange':
        return 'bg-orange-500';
      case 'yellow':
        return 'bg-yellow-500';
      case 'blue':
        return 'bg-blue-500';
      case 'green':
        return 'bg-green-500';
      default:
        return 'bg-gray-300';
    }
  };

  if (!password) return null;

  return (
    <div className='mt-2'>
      <div className='flex items-center justify-between mb-2'>
        <span className='text-xs font-medium text-gray-600'>Độ mạnh mật khẩu:</span>
        <span className={`text-xs font-medium px-2 py-1 rounded-full border ${getColorClasses(strength.color)}`}>
          {strength.label}
        </span>
      </div>
      
      <div className='w-full bg-gray-200 rounded-full h-2'>
        <div
          className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(strength.color)}`}
          style={{ width: `${(strength.score / 5) * 100}%` }}
        />
      </div>

      <div className='mt-3 space-y-1'>
        <div className='flex items-center text-xs'>
          {password.length >= 8 ? (
            <CheckCircleIcon className='w-3 h-3 text-green-500 mr-2' />
          ) : (
            <XCircleIcon className='w-3 h-3 text-red-500 mr-2' />
          )}
          <span className={password.length >= 8 ? 'text-green-600' : 'text-red-600'}>
            Ít nhất 8 ký tự
          </span>
        </div>
        
        <div className='flex items-center text-xs'>
          {/[a-z]/.test(password) ? (
            <CheckCircleIcon className='w-3 h-3 text-green-500 mr-2' />
          ) : (
            <XCircleIcon className='w-3 h-3 text-red-500 mr-2' />
          )}
          <span className={/[a-z]/.test(password) ? 'text-green-600' : 'text-red-600'}>
            Chữ thường
          </span>
        </div>
        
        <div className='flex items-center text-xs'>
          {/[A-Z]/.test(password) ? (
            <CheckCircleIcon className='w-3 h-3 text-green-500 mr-2' />
          ) : (
            <XCircleIcon className='w-3 h-3 text-red-500 mr-2' />
          )}
          <span className={/[A-Z]/.test(password) ? 'text-green-600' : 'text-red-600'}>
            Chữ hoa
          </span>
        </div>
        
        <div className='flex items-center text-xs'>
          {/\d/.test(password) ? (
            <CheckCircleIcon className='w-3 h-3 text-green-500 mr-2' />
          ) : (
            <XCircleIcon className='w-3 h-3 text-red-500 mr-2' />
          )}
          <span className={/\d/.test(password) ? 'text-green-600' : 'text-red-600'}>
            Số
          </span>
        </div>
        
        <div className='flex items-center text-xs'>
          {/[!@#$%^&*(),.?":{}|<>]/.test(password) ? (
            <CheckCircleIcon className='w-3 h-3 text-green-500 mr-2' />
          ) : (
            <XCircleIcon className='w-3 h-3 text-red-500 mr-2' />
          )}
          <span className={/[!@#$%^&*(),.?":{}|<>]/.test(password) ? 'text-green-600' : 'text-red-600'}>
            Ký tự đặc biệt
          </span>
        </div>
      </div>
    </div>
  );
};

export default PasswordStrengthIndicator; 