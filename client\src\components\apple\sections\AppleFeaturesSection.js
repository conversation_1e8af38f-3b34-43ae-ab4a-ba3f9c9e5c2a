import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin';
import { Physics2DPlugin } from 'gsap/Physics2DPlugin';
import { CustomEase } from 'gsap/CustomEase';
import { SplitText } from 'gsap/SplitText';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import { motion } from 'framer-motion';
import { useLanguage } from '../../../contexts/LanguageContext';
import {
  ShieldCheckIcon,
  CurrencyDollarIcon,
  ClockIcon,
  UserGroupIcon,
  StarIcon,
  GlobeAltIcon,
} from '@heroicons/react/24/outline';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, MorphSVGPlugin, Physics2DPlugin, CustomEase, SplitText, MotionPathPlugin);

// Advanced easing curves for premium interactions
const premiumEases = {
  liquid: CustomEase.create("liquid", "M0,0 C0.29,0.01 0.49,1.53 0.59,1.23 C0.69,0.93 1,1 1,1"),
  elastic: CustomEase.create("elastic", "M0,0 C0.25,0 0.4,1.4 0.7,1 C0.85,0.8 1,1 1,1"),
  magnetic: CustomEase.create("magnetic", "M0,0 C0.1,0 0.2,2 0.6,1 C0.8,0 0.9,1 1,1"),
  bounce: CustomEase.create("bounce", "M0,0 C0.14,0 0.242,0.438 0.272,0.561 0.313,0.728 0.354,0.963 0.362,1 0.37,0.985 0.414,0.928 0.455,0.879 0.504,0.822 0.565,0.729 0.621,0.653 0.681,0.573 0.737,0.5 0.785,0.5 0.856,0.5 0.923,0.717 1,1"),
  wave: CustomEase.create("wave", "M0,0 C0.2,0 0.3,1.3 0.5,1 C0.7,0.7 0.8,1.3 1,1")
};

const AppleFeaturesSection = () => {
  const { t } = useLanguage();
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const featuresRef = useRef(null);
  const particleSystemRef = useRef(null);
  const morphingBlobsRef = useRef([]);

  const getColorClasses = (color) => {
    const colorMap = {
      blue: {
        bg: 'from-blue-400/20 to-cyan-500/20',
        icon: 'text-blue-600 dark:text-blue-400',
        glow: 'shadow-blue-500/20',
        accent: '#3B82F6',
        particles: '#60A5FA'
      },
      green: {
        bg: 'from-green-400/20 to-emerald-500/20',
        icon: 'text-green-600 dark:text-green-400',
        glow: 'shadow-green-500/20',
        accent: '#10B981',
        particles: '#34D399'
      },
      orange: {
        bg: 'from-orange-400/20 to-red-500/20',
        icon: 'text-orange-600 dark:text-orange-400',
        glow: 'shadow-orange-500/20',
        accent: '#F97316',
        particles: '#FB923C'
      },
      purple: {
        bg: 'from-purple-400/20 to-pink-500/20',
        icon: 'text-purple-600 dark:text-purple-400',
        glow: 'shadow-purple-500/20',
        accent: '#8B5CF6',
        particles: '#A78BFA'
      },
      yellow: {
        bg: 'from-yellow-400/20 to-orange-500/20',
        icon: 'text-yellow-600 dark:text-yellow-400',
        glow: 'shadow-yellow-500/20',
        accent: '#F59E0B',
        particles: '#FBBF24'
      },
      indigo: {
        bg: 'from-indigo-400/20 to-blue-600/20',
        icon: 'text-indigo-600 dark:text-indigo-400',
        glow: 'shadow-indigo-500/20',
        accent: '#6366F1',
        particles: '#818CF8'
      },
    };
    return colorMap[color] || colorMap.blue;
  };

  // Advanced Particle System with Physics
  const createAdvancedParticleSystem = () => {
    if (!particleSystemRef.current) return;

    const numParticles = 80;
    const particles = [];

    for (let i = 0; i < numParticles; i++) {
      const particle = document.createElement('div');
      const size = Math.random() * 4 + 1;
      particle.className = 'absolute rounded-full pointer-events-none';
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;
      particle.style.left = Math.random() * 100 + '%';
      particle.style.top = Math.random() * 100 + '%';
      
      // Random colors from feature colors
      const colors = ['#60A5FA', '#34D399', '#FB923C', '#A78BFA', '#FBBF24', '#818CF8'];
      particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
      particle.style.opacity = Math.random() * 0.6 + 0.2;
      
      particleSystemRef.current.appendChild(particle);
      particles.push(particle);

      // Physics-based movement
      gsap.set(particle, {
        physics2D: {
          velocity: (Math.random() - 0.5) * 50,
          angle: Math.random() * 360,
          gravity: 30
        }
      });

      // Continuous floating animation
      gsap.to(particle, {
        physics2D: {
          velocity: (Math.random() - 0.5) * 80,
          angle: Math.random() * 360
        },
        duration: 15 + Math.random() * 10,
        repeat: -1,
        yoyo: true,
        ease: "none"
      });

      // Scale pulsing
      gsap.to(particle, {
        scale: 1.5 + Math.random(),
        duration: 3 + Math.random() * 4,
        repeat: -1,
        yoyo: true,
        ease: premiumEases.wave
      });

      // Opacity breathing
      gsap.to(particle, {
        opacity: 0.8,
        duration: 2 + Math.random() * 3,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
      });
    }

    return particles;
  };

  // Morphing Background Shapes
  const createMorphingBackground = () => {
    if (!morphingBlobsRef.current || !Array.isArray(morphingBlobsRef.current)) {
      morphingBlobsRef.current = [];
    }

    const shapes = [];
    const numShapes = 8;

    for (let i = 0; i < numShapes; i++) {
      const shape = document.createElement('div');
      shape.className = 'absolute rounded-full bg-gradient-to-br opacity-10 transition-all duration-1000';
      
      const size = 150 + Math.random() * 200;
      shape.style.width = `${size}px`;
      shape.style.height = `${size}px`;
      shape.style.left = `${Math.random() * 100}%`;
      shape.style.top = `${Math.random() * 100}%`;
      
      const gradients = [
        'from-blue-500/30 to-purple-500/30',
        'from-green-500/30 to-blue-500/30',
        'from-purple-500/30 to-pink-500/30',
        'from-orange-500/30 to-red-500/30',
        'from-yellow-500/30 to-orange-500/30',
        'from-indigo-500/30 to-purple-500/30'
      ];
      shape.className += ` ${gradients[i % gradients.length]}`;
      
      morphingBlobsRef.current.push(shape);
      document.body.appendChild(shape);

      // Morphing animation
      gsap.to(shape, {
        borderRadius: `${30 + Math.random() * 40}% ${30 + Math.random() * 40}% ${30 + Math.random() * 40}% ${30 + Math.random() * 40}%`,
        duration: 8 + Math.random() * 6,
        repeat: -1,
        yoyo: true,
        ease: premiumEases.liquid
      });

      // Position drift
      gsap.to(shape, {
        x: `+=${Math.random() * 200 - 100}`,
        y: `+=${Math.random() * 200 - 100}`,
        scale: 1.2 + Math.random() * 0.8,
        rotation: 360,
        duration: 20 + Math.random() * 15,
        repeat: -1,
        yoyo: true,
        ease: premiumEases.wave
      });
    }

    return shapes;
  };

  // Advanced Magnetic Hover Effect with Physics
  const createMagneticPhysicsHover = (element, config = {}) => {
    if (!element) return;

    const strength = config.strength || 0.3;
    const rotationStrength = config.rotationStrength || 0.15;
    const scaleAmount = config.scaleAmount || 1.05;

    const handleMouseMove = (e) => {
      const rect = element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      const deltaX = (e.clientX - centerX) * strength;
      const deltaY = (e.clientY - centerY) * strength;
      
      // Main magnetic effect
      gsap.to(element, {
        x: deltaX,
        y: deltaY,
        rotationX: deltaY * rotationStrength,
        rotationY: deltaX * rotationStrength,
        scale: scaleAmount,
        duration: 0.3,
        ease: premiumEases.magnetic
      });

      // Glow effect
      const glowElement = element.querySelector('.glow-effect');
      if (glowElement) {
        gsap.to(glowElement, {
          opacity: 1,
          scale: 1.1,
          duration: 0.3
        });
      }

      // Particle explosion on hover
      const particleContainer = element.querySelector('.hover-particles');
      if (particleContainer) {
        createHoverParticles(particleContainer, e.clientX - rect.left, e.clientY - rect.top);
      }
    };
    
    const handleMouseLeave = () => {
      gsap.to(element, {
        x: 0,
        y: 0,
        rotationX: 0,
        rotationY: 0,
        scale: 1,
        duration: 0.8,
        ease: premiumEases.elastic
      });

      const glowElement = element.querySelector('.glow-effect');
      if (glowElement) {
        gsap.to(glowElement, {
          opacity: 0,
          scale: 1,
          duration: 0.5
        });
      }
    };
    
    element.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      element.removeEventListener('mousemove', handleMouseMove);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  };

  // Create hover particles
  const createHoverParticles = (container, x, y) => {
    const numParticles = 12;
    
    for (let i = 0; i < numParticles; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-2 h-2 bg-blue-400 rounded-full pointer-events-none';
      particle.style.left = `${x}px`;
      particle.style.top = `${y}px`;
      
      container.appendChild(particle);
      
      const angle = (360 / numParticles) * i;
      const distance = 50 + Math.random() * 30;
      
      gsap.to(particle, {
        x: Math.cos(angle * Math.PI / 180) * distance,
        y: Math.sin(angle * Math.PI / 180) * distance,
        opacity: 0,
        scale: 0,
        duration: 0.8,
        ease: premiumEases.bounce,
        onComplete: () => {
          container.removeChild(particle);
        }
      });
    }
  };

  // Advanced title animation with split text and physics
  const createAdvancedTitleAnimation = () => {
    if (!titleRef.current) return;

    const titleElement = titleRef.current.querySelector('h2');
    const subtitleElement = titleRef.current.querySelector('p');
    
    if (titleElement) {
      const titleSplit = new SplitText(titleElement, { type: "words,chars" });
      
      gsap.fromTo(titleSplit.chars, {
        opacity: 0,
        y: 100,
        rotationX: -90,
        transformOrigin: "center bottom"
      }, {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration: 1.2,
        stagger: 0.02,
        ease: premiumEases.bounce,
        scrollTrigger: {
          trigger: titleElement,
          start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      });

      // Add floating animation after initial animation
      gsap.to(titleSplit.chars, {
        y: "+=3",
        duration: 2.5,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        stagger: 0.1,
        delay: 1.5
      });
    }

    if (subtitleElement) {
      const subtitleSplit = new SplitText(subtitleElement, { type: "words" });
      
      gsap.fromTo(subtitleSplit.words, {
        opacity: 0,
        y: 30,
        scale: 0.8
      }, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        stagger: 0.08,
        ease: premiumEases.elastic,
        delay: 0.5,
        scrollTrigger: {
          trigger: subtitleElement,
          start: 'top 80%',
          toggleActions: 'play none none reverse'
        }
      });
    }
  };

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Initialize advanced systems
      createAdvancedParticleSystem();
      createMorphingBackground();
      createAdvancedTitleAnimation();

      // Features animation with enhanced effects
      if (featuresRef.current && featuresRef.current.children) {
        const featureElements = Array.from(featuresRef.current.children);
        
        // Staggered entrance animation
        gsap.fromTo(featureElements, {
          opacity: 0,
          y: 100,
          scale: 0.8,
          rotationY: 45
        }, {
          opacity: 1,
          y: 0,
          scale: 1,
          rotationY: 0,
          duration: 1.5,
          stagger: 0.15,
          ease: premiumEases.elastic,
          scrollTrigger: {
            trigger: featuresRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse',
          },
        });

        // Add magnetic hover effects to each feature card
        featureElements.forEach((element) => {
          createMagneticPhysicsHover(element, {
            strength: 0.2,
            rotationStrength: 0.1,
            scaleAmount: 1.03
          });

          // Add interactive particle system to each card
          const particleContainer = document.createElement('div');
          particleContainer.className = 'hover-particles absolute inset-0 overflow-hidden pointer-events-none';
          element.appendChild(particleContainer);
        });
      }
    }, sectionRef);

    return () => ctx.revert();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const features = [
    {
      icon: ShieldCheckIcon,
      title: t('secureAndTrusted'),
      description: t('secureDesc'),
      color: 'blue',
    },
    {
      icon: CurrencyDollarIcon,
      title: t('fairPricing'),
      description: t('fairPricingDesc'),
      color: 'green',
    },
    {
      icon: ClockIcon,
      title: t('fastDelivery'),
      description: t('fastDeliveryDesc'),
      color: 'orange',
    },
    {
      icon: UserGroupIcon,
      title: t('expertNetwork'),
      description: t('expertNetworkDesc'),
      color: 'purple',
    },
    {
      icon: StarIcon,
      title: t('qualityGuaranteed'),
      description: t('qualityGuaranteedDesc'),
      color: 'yellow',
    },
    {
      icon: GlobeAltIcon,
      title: t('globalReach'),
      description: t('globalReachDesc'),
      color: 'indigo',
    },
  ];

  return (
    <>
      {/* Enhanced CSS for advanced interactions */}
      <style>{`
        @keyframes particle-float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
          }
          50% {
            transform: translateY(-15px) rotate(180deg);
          }
        }
        
        @keyframes morphing-glow {
          0%, 100% {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
          }
          50% {
            box-shadow: 0 0 40px rgba(139, 92, 246, 0.5);
          }
        }
        
        @keyframes liquid-morph {
          0%, 100% {
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
          }
          25% {
            border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
          }
          50% {
            border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
          }
          75% {
            border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
          }
        }
        
        .interactive-card {
          transform-style: preserve-3d;
          perspective: 1000px;
        }
        
        .glow-effect {
          animation: morphing-glow 4s ease-in-out infinite;
        }
        
        .particle-system {
          animation: particle-float 8s ease-in-out infinite;
        }
        
        .morphing-shape {
          animation: liquid-morph 12s ease-in-out infinite;
        }
        
        .magnetic-hover {
          transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
      `}</style>

      <section 
        ref={sectionRef} 
        className='relative py-20 bg-gradient-to-br from-white via-blue-50/20 to-purple-50/20 dark:from-gray-900 dark:via-gray-800/30 dark:to-gray-900 transition-colors duration-300 overflow-hidden'
      >
        {/* Advanced Particle System */}
        <div 
          ref={particleSystemRef}
          className='absolute inset-0 overflow-hidden pointer-events-none particle-system'
        />

        {/* Morphing Background */}
        <div 
          ref={morphingBlobsRef}
          className='absolute inset-0 overflow-hidden'
        />

        {/* Enhanced Background Gradient Layers */}
        <div className='absolute inset-0 opacity-40'>
          <div className='absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-400/15 to-purple-400/15 rounded-full blur-3xl morphing-shape' />
          <div className='absolute bottom-0 right-1/4 w-80 h-80 bg-gradient-to-br from-green-400/15 to-blue-400/15 rounded-full blur-3xl morphing-shape' style={{ animationDelay: '4s' }} />
          <div className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-purple-400/15 to-pink-400/15 rounded-full blur-3xl morphing-shape' style={{ animationDelay: '8s' }} />
        </div>

        <div className='relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          {/* Enhanced Section Header */}
          <div ref={titleRef} className='text-center mb-20'>
            <h2 className='text-4xl sm:text-5xl lg:text-6xl font-black text-gray-900 dark:text-gray-100 mb-8 transition-colors duration-300'>
              {t('whyChooseVWork')}
            </h2>
            <p className='text-xl sm:text-2xl lg:text-3xl text-gray-600 dark:text-gray-400 max-w-4xl mx-auto leading-relaxed transition-colors duration-300'>
              {t('trustedPlatformDesc')}
            </p>
          </div>

          {/* Enhanced Features Grid */}
          <div
            ref={featuresRef}
            className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12'
          >
            {features.map((feature, index) => {
              const colorConfig = getColorClasses(feature.color);
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className='group interactive-card relative'
                >
                  {/* Glow Effect */}
                  <div className={`glow-effect absolute inset-0 bg-gradient-to-br ${colorConfig.bg} rounded-3xl blur-xl opacity-0 transition-all duration-500`} />
                  
                  {/* Main Card */}
                  <div className='relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-8 border border-gray-200/50 dark:border-gray-700/50 hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-500 shadow-xl hover:shadow-2xl magnetic-hover overflow-hidden'>
                    
                    {/* Animated Background Pattern */}
                    <div className='absolute inset-0 opacity-5'>
                      <div className={`w-full h-full bg-gradient-to-br ${colorConfig.bg} morphing-shape`} />
                    </div>

                    {/* Icon with Enhanced Physics */}
                    <div className='mb-8 relative z-10'>
                      <div className={`inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br ${colorConfig.bg} rounded-2xl transition-all duration-500 shadow-lg group-hover:shadow-xl transform group-hover:scale-110 group-hover:-rotate-6 relative overflow-hidden`}>
                        <feature.icon className={`h-10 w-10 ${colorConfig.icon} transition-all duration-500 transform group-hover:scale-110 z-10 relative`} />
                        
                        {/* Icon background particles */}
                        <div className='absolute inset-0'>
                          <div className='absolute top-1 right-1 w-2 h-2 bg-white/40 rounded-full animate-ping' />
                          <div className='absolute bottom-1 left-1 w-1 h-1 bg-white/60 rounded-full animate-ping' style={{ animationDelay: '0.5s' }} />
                        </div>
                      </div>
                      
                      {/* Orbiting particles */}
                      <div className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24'>
                        <div className='absolute top-0 left-1/2 w-1 h-1 bg-blue-400/60 rounded-full animate-ping origin-center' style={{ animation: 'particle-float 3s linear infinite' }} />
                        <div className='absolute bottom-0 right-1/2 w-1 h-1 bg-purple-400/60 rounded-full animate-ping origin-center' style={{ animation: 'particle-float 3s linear infinite reverse', animationDelay: '1.5s' }} />
                      </div>
                    </div>

                    {/* Enhanced Content */}
                    <div className='relative z-10'>
                      <h3 className='text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4 transition-colors duration-300 group-hover:text-blue-600 dark:group-hover:text-blue-400'>
                        {feature.title}
                      </h3>
                      <p className='text-base sm:text-lg text-gray-600 dark:text-gray-400 leading-relaxed transition-colors duration-300 group-hover:text-gray-800 dark:group-hover:text-gray-200'>
                        {feature.description}
                      </p>
                    </div>

                    {/* Interactive Progress Bar */}
                    <div className='mt-6 relative z-10'>
                      <div className='w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden'>
                        <div className={`h-full bg-gradient-to-r ${colorConfig.bg.replace('/20', '/60')} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-1000 origin-left`} />
                      </div>
                    </div>

                    {/* Corner decorative elements */}
                    <div className='absolute top-4 right-4 w-8 h-8 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full morphing-shape opacity-50' />
                    <div className='absolute bottom-4 left-4 w-6 h-6 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full morphing-shape opacity-50' style={{ animationDelay: '2s' }} />
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>
    </>
  );
};

export default AppleFeaturesSection;
