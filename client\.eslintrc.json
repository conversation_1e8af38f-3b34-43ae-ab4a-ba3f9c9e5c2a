{"env": {"browser": true, "es2021": true, "node": true, "jest": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "react-hooks"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "react/no-unescaped-entities": "off", "no-console": "off", "no-debugger": "warn", "no-unused-vars": "warn", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "no-alert": "warn"}, "settings": {"react": {"version": "detect"}}, "ignorePatterns": ["build/**", "node_modules/**", "public/**", "*.config.js"]}