import React from 'react';
import {
  UserIcon,
  BriefcaseIcon,
  ShieldCheckIcon,
  SwordIcon,
} from '@heroicons/react/24/outline';
import { USER_TYPES } from '../../contexts/AuthContext';

const UserTypeSelector = ({ selectedType, onSelect, className = '' }) => {
  const userTypes = [
    {
      type: USER_TYPES.CLIENT,
      title: "I'm a Noble Patron",
      description: 'I have quests and need to hire skilled craftsmen',
      icon: UserIcon,
      emblem: '👑',
    },
    {
      type: USER_TYPES.FREELANCER,
      title: "I'm a Guild Craftsman",
      description: 'I seek adventures and patrons that match my arts',
      icon: BriefcaseIcon,
      emblem: '⚔️',
    },
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      <div className='text-center mb-6'>
        <h3 className='font-cinzel-decorative text-lg font-bold text-medieval-brown-800'>
          Choose Your Path in the Guild
        </h3>
        <p className='font-cinzel text-sm text-medieval-brown-600 mt-2'>
          Select your role in our medieval marketplace
        </p>
      </div>

      <div className='space-y-4'>
        {userTypes.map(({ type, title, description, icon: Icon, emblem }) => (
          <div
            key={type}
            onClick={() => onSelect(type)}
            className={`
              relative cursor-pointer rounded-lg border-2 p-5 transition-all duration-300 card-medieval-hover
              ${
                selectedType === type
                  ? 'border-medieval-gold-500 bg-medieval-gold-50'
                  : 'border-medieval-brown-300 hover:border-medieval-gold-400'
              }
            `}
            style={{
              background:
                selectedType === type
                  ? 'linear-gradient(45deg, rgba(212, 160, 23, 0.1) 0%, rgba(244, 228, 188, 0.8) 100%)'
                  : 'var(--medieval-gradient-parchment)',
            }}
          >
            <div className='flex items-start space-x-4'>
              <div
                className={`
                flex-shrink-0 w-12 h-12 flex items-center justify-center rounded-full border-2 transition-all duration-300
                ${
                  selectedType === type
                    ? 'bg-medieval-gold-500 border-medieval-gold-500 text-medieval-parchment guild-emblem'
                    : 'bg-medieval-brown-100 border-medieval-brown-300 text-medieval-brown-600'
                }
              `}
              >
                <Icon className='w-6 h-6' />
              </div>

              <div className='flex-1 min-w-0'>
                <div className='flex items-center space-x-2 mb-2'>
                  <span className='text-lg' role='img' aria-label='role emblem'>
                    {emblem}
                  </span>
                  <h4
                    className={`
                    font-cinzel font-semibold text-base
                    ${
                      selectedType === type
                        ? 'text-medieval-brown-800'
                        : 'text-medieval-brown-700'
                    }
                  `}
                  >
                    {title}
                  </h4>
                </div>
                <p
                  className={`
                  font-cinzel text-sm leading-relaxed
                  ${
                    selectedType === type
                      ? 'text-medieval-brown-700'
                      : 'text-medieval-brown-600'
                  }
                `}
                >
                  {description}
                </p>
              </div>

              <div
                className={`
                flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-300
                ${
                  selectedType === type
                    ? 'border-medieval-gold-500 bg-medieval-gold-500'
                    : 'border-medieval-brown-400'
                }
              `}
              >
                {selectedType === type && (
                  <ShieldCheckIcon className='w-4 h-4 text-medieval-parchment' />
                )}
              </div>
            </div>

            {/* Medieval decorative corners */}
            {selectedType === type && (
              <>
                <div className='absolute top-2 left-2 w-3 h-3 border-l-2 border-t-2 border-medieval-gold-500 opacity-60' />
                <div className='absolute top-2 right-2 w-3 h-3 border-r-2 border-t-2 border-medieval-gold-500 opacity-60' />
                <div className='absolute bottom-2 left-2 w-3 h-3 border-l-2 border-b-2 border-medieval-gold-500 opacity-60' />
                <div className='absolute bottom-2 right-2 w-3 h-3 border-r-2 border-b-2 border-medieval-gold-500 opacity-60' />
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default UserTypeSelector;
