import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { gsap } from 'gsap';
import { useAuth, USER_TYPES } from '../contexts/AuthContext';
import { ApplePageWrapper } from '../components/apple';
import {
  BuildingOfficeIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

const ClientSetupPage = () => {
  const navigate = useNavigate();
  const { user, updateUserProfile } = useAuth();

  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  const [formData, setFormData] = useState({
    companyName: '',
    industry: '',
    companySize: '',
    website: '',
    description: '',
    projectTypes: [],
    budgetRange: '',
  });

  const [errors, setErrors] = useState({});

  const containerRef = useRef(null);
  const titleRef = useRef(null);
  const formRef = useRef(null);

  // Redirect if not client
  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    if (user.userType !== USER_TYPES.CLIENT) {
      navigate('/dashboard');
      return;
    }

    // Check if profile is already complete
    if (user.profile?.isComplete === true) {
      console.log('✅ ClientSetupPage: Profile already complete, redirecting to dashboard');
      navigate('/dashboard', { replace: true });
      return;
    }
  }, [user, navigate]);

  // GSAP Animations
  useEffect(() => {
    const tl = gsap.timeline();

    tl.fromTo(
      titleRef.current,
      { opacity: 0, y: -30 },
      { opacity: 1, y: 0, duration: 0.8, ease: 'power3.out' }
    ).fromTo(
      formRef.current,
      { opacity: 0, y: 30 },
      { opacity: 1, y: 0, duration: 0.8, ease: 'power3.out' },
      '-=0.4'
    );
  }, [currentStep]);

  const industries = [
    { value: 'technology', label: 'Technology' },
    { value: 'healthcare', label: 'Healthcare' },
    { value: 'finance', label: 'Finance & Banking' },
    { value: 'ecommerce', label: 'E-commerce' },
    { value: 'education', label: 'Education' },
    { value: 'real-estate', label: 'Real Estate' },
    { value: 'marketing', label: 'Marketing & Advertising' },
    { value: 'manufacturing', label: 'Manufacturing' },
    { value: 'food-beverage', label: 'Food & Beverage' },
    { value: 'other', label: 'Khác' },
  ];

  const companySizes = [
    { value: 'startup', label: 'Startup (1-10 nhân viên)' },
    { value: 'small', label: 'Công ty nhỏ (11-50 nhân viên)' },
    { value: 'medium', label: 'Công ty vừa (51-200 nhân viên)' },
    { value: 'large', label: 'Công ty lớn (200+ nhân viên)' },
    { value: 'individual', label: 'Cá nhân' },
  ];

  const projectTypes = [
    'Website Development',
    'Mobile App Development',
    'UI/UX Design',
    'Logo & Branding',
    'Content Writing',
    'Digital Marketing',
    'SEO',
    'Social Media Management',
    'E-commerce Development',
    'Data Analysis',
  ];

  const budgetRanges = [
    { value: 'under-500', label: 'Dưới 500 USD' },
    { value: '500-2000', label: '500 - 2,000 USD' },
    { value: '2000-5000', label: '2,000 - 5,000 USD' },
    { value: '5000-10000', label: '5,000 - 10,000 USD' },
    { value: 'over-10000', label: 'Trên 10,000 USD' },
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const toggleProjectType = type => {
    setFormData(prev => ({
      ...prev,
      projectTypes: prev.projectTypes.includes(type)
        ? prev.projectTypes.filter(t => t !== type)
        : [...prev.projectTypes, type].slice(0, 5), // Limit to 5 project types
    }));
  };

  const validateStep = step => {
    const newErrors = {};

    switch (step) {
      case 1:
        if (!formData.companyName.trim())
          newErrors.companyName = 'Vui lòng nhập tên công ty/tổ chức';
        if (!formData.industry) newErrors.industry = 'Vui lòng chọn lĩnh vực';
        if (!formData.companySize)
          newErrors.companySize = 'Vui lòng chọn quy mô công ty';
        break;
      case 2:
        if (
          !formData.description.trim() ||
          formData.description.trim().length < 30
        ) {
          newErrors.description = 'Mô tả cần ít nhất 30 ký tự';
        }
        if (formData.projectTypes.length === 0) {
          newErrors.projectTypes = 'Vui lòng chọn ít nhất 1 loại dự án';
        }
        break;
      case 3:
        if (!formData.budgetRange)
          newErrors.budgetRange = 'Vui lòng chọn ngân sách dự kiến';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep) && currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    try {
      setLoading(true);
      setErrors({}); // Clear previous errors

      const profileData = {
        profile: {
          ...user.profile,
          ...formData,
          isComplete: true,
          completedAt: new Date().toISOString(),
        },
      };

      const result = await updateUserProfile(profileData);

      if (result.success) {
        // console.log('Thông tin công ty đã được lưu thành công!');
        navigate('/dashboard');
      } else {
        setErrors({ submit: result.error });
      }
    } catch (error) {
      console.error('Client setup error:', error);
      setErrors({ submit: error.message || 'Đã xảy ra lỗi khi lưu thông tin. Vui lòng thử lại.' });
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-8'>
              <div className='guild-emblem w-16 h-16 mx-auto mb-4 flex items-center justify-center'>
                <BuildingOfficeIcon className='h-10 w-10 text-medieval-parchment' />
              </div>
              <h3 className='font-cinzel-decorative text-xl font-bold text-medieval-brown-800 mb-4'>
                🏰 Your Business Information
              </h3>
              <p className='font-cinzel text-medieval-brown-600 mb-6'>
                Help travellers understand your business needs
              </p>
            </div>

            <div>
              <label className='block font-cinzel text-sm font-medium text-medieval-brown-700 mb-3'>
                Business Name
              </label>
              <input
                type='text'
                value={formData.companyName}
                onChange={e => handleInputChange('companyName', e.target.value)}
                placeholder='Example: House of Innovation'
                className={`form-input-medieval w-full px-4 py-3 font-cinzel ${
                  errors.companyName ? 'border-medieval-red-600' : ''
                }`}
              />
              {errors.companyName ? (
                <p className='text-medieval-red-700 font-cinzel text-sm mt-1'>
                  {errors.companyName}
                </p>
              ) : null}
            </div>

            <div>
              <label className='block font-cinzel text-sm font-medium text-medieval-brown-700 mb-3'>
                Industry
              </label>
              <select
                value={formData.industry}
                onChange={e => handleInputChange('industry', e.target.value)}
                className={`form-input-medieval w-full px-4 py-3 font-cinzel ${
                  errors.industry ? 'border-medieval-red-600' : ''
                }`}
              >
                <option value=''>Choose your industry...</option>
                {industries.map(industry => (
                  <option key={industry.value} value={industry.value}>
                    {industry.label}
                  </option>
                ))}
              </select>
              {errors.industry ? (
                <p className='text-medieval-red-700 font-cinzel text-sm mt-1'>
                  {errors.industry}
                </p>
              ) : null}
            </div>

            <div>
              <label className='block font-cinzel text-sm font-medium text-medieval-brown-700 mb-3'>
                House Size
              </label>
              <div className='space-y-3'>
                {companySizes.map(size => (
                  <div
                    key={size.value}
                    onClick={() => handleInputChange('companySize', size.value)}
                    className={`card-medieval-hover p-4 cursor-pointer transition-all duration-300 ${
                      formData.companySize === size.value
                        ? 'border-medieval-gold-500 bg-medieval-gold-50'
                        : 'border-medieval-brown-300 hover:border-medieval-gold-400'
                    }`}
                    style={{
                      background:
                        formData.companySize === size.value
                          ? 'linear-gradient(45deg, rgba(212, 160, 23, 0.1) 0%, rgba(244, 228, 188, 0.8) 100%)'
                          : 'var(--medieval-gradient-parchment)',
                    }}
                  >
                    <div className='flex items-center justify-between'>
                      <span
                        className={`font-cinzel font-semibold ${
                          formData.companySize === size.value
                            ? 'text-medieval-brown-800'
                            : 'text-medieval-brown-700'
                        }`}
                      >
                        {size.label}
                      </span>
                      <div
                        className={`
                        w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-300
                        ${
                          formData.companySize === size.value
                            ? 'border-medieval-gold-500 bg-medieval-gold-500'
                            : 'border-medieval-brown-400'
                        }
                      `}
                      >
                        {formData.companySize === size.value && (
                          <CheckCircleIcon className='w-4 h-4 text-medieval-parchment' />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              {errors.companySize ? (
                <p className='text-medieval-red-700 font-cinzel text-sm mt-1'>
                  {errors.companySize}
                </p>
              ) : null}
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
                Website (tùy chọn)
              </label>
              <input
                type='url'
                value={formData.website}
                onChange={e => handleInputChange('website', e.target.value)}
                placeholder='https://yourcompany.com'
                className='w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white'
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-8'>
              <UserGroupIcon className='mx-auto h-16 w-16 text-blue-500 mb-4' />
              <h3 className='text-xl font-semibold text-gray-900 dark:text-white'>
                Mô tả về công ty và nhu cầu dự án
              </h3>
              <p className='text-gray-600 dark:text-gray-400 mt-2'>
                Giúp freelancer hiểu rõ về công ty và loại dự án bạn quan tâm
              </p>
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
                Mô tả về công ty
              </label>
              <textarea
                value={formData.description}
                onChange={e => handleInputChange('description', e.target.value)}
                placeholder='Ví dụ: Chúng tôi là một startup công nghệ chuyên phát triển các giải pháp phần mềm cho doanh nghiệp vừa và nhỏ. Chúng tôi tập trung vào việc tạo ra những sản phẩm có tính ứng dụng cao...'
                rows={5}
                className={`
                  w-full p-4 border rounded-lg resize-none
                  focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                  dark:bg-gray-800 dark:border-gray-600 dark:text-white
                  ${errors.description ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'}
                `}
              />
              <div className='flex justify-between mt-2'>
                <span className='text-sm text-gray-500'>
                  Tối thiểu 30 ký tự
                </span>
                <span className='text-sm text-gray-500'>
                  {formData.description.length}/500
                </span>
              </div>
              {errors.description ? (
                <p className='text-red-600 dark:text-red-400 text-sm mt-1'>
                  {errors.description}
                </p>
              ) : null}
            </div>

            <div>
              <label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
                Loại dự án bạn quan tâm (chọn tối đa 5)
              </label>
              <div className='grid grid-cols-2 gap-2'>
                {projectTypes.map(type => (
                  <button
                    key={type}
                    onClick={() => toggleProjectType(type)}
                    disabled={
                      !formData.projectTypes.includes(type) &&
                      formData.projectTypes.length >= 5
                    }
                    className={`
                      p-3 text-sm rounded-lg border transition-all duration-200 text-left
                      ${
                        formData.projectTypes.includes(type)
                          ? 'bg-blue-100 border-blue-500 text-blue-700 dark:bg-blue-900/30 dark:border-blue-400 dark:text-blue-300'
                          : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                      }
                      ${!formData.projectTypes.includes(type) && formData.projectTypes.length >= 5 ? 'opacity-50 cursor-not-allowed' : ''}
                    `}
                  >
                    {type}
                  </button>
                ))}
              </div>
              <div className='mt-2 text-sm text-gray-500'>
                Đã chọn: {formData.projectTypes.length}/5
              </div>
              {errors.projectTypes ? (
                <p className='text-red-600 dark:text-red-400 text-sm mt-1'>
                  {errors.projectTypes}
                </p>
              ) : null}
            </div>
          </div>
        );

      case 3:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-8'>
              <CurrencyDollarIcon className='mx-auto h-16 w-16 text-blue-500 mb-4' />
              <h3 className='text-xl font-semibold text-gray-900 dark:text-white'>
                Ngân sách dự kiến cho dự án
              </h3>
              <p className='text-gray-600 dark:text-gray-400 mt-2'>
                Thông tin này giúp freelancer đưa ra đề xuất phù hợp
              </p>
            </div>

            <div className='space-y-3'>
              {budgetRanges.map(budget => (
                <div
                  key={budget.value}
                  onClick={() => handleInputChange('budgetRange', budget.value)}
                  className={`
                    cursor-pointer rounded-lg border-2 p-4 transition-all duration-200
                    ${
                      formData.budgetRange === budget.value
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }
                  `}
                >
                  <div className='flex items-center justify-between'>
                    <span
                      className={`font-medium ${
                        formData.budgetRange === budget.value
                          ? 'text-blue-900 dark:text-blue-100'
                          : 'text-gray-900 dark:text-white'
                      }`}
                    >
                      {budget.label}
                    </span>
                    <div
                      className={`
                      w-5 h-5 rounded-full border-2 flex items-center justify-center
                      ${
                        formData.budgetRange === budget.value
                          ? 'border-blue-500'
                          : 'border-gray-300 dark:border-gray-600'
                      }
                    `}
                    >
                      {formData.budgetRange === budget.value && (
                        <div className='w-2.5 h-2.5 rounded-full bg-blue-500' />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {errors.budgetRange ? (
              <p className='text-red-600 dark:text-red-400 text-sm'>
                {errors.budgetRange}
              </p>
            ) : null}

            <div className='bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg'>
              <p className='text-sm text-blue-800 dark:text-blue-200'>
                💡 <strong>Lưu ý:</strong> Ngân sách này chỉ mang tính tham
                khảo. Bạn có thể thương lượng chi tiết với freelancer khi bắt
                đầu dự án.
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (!user) return null;

  return (
    <ApplePageWrapper
      ref={containerRef}
      className='py-12 px-4 sm:px-6 lg:px-8'
      variant='page'
    >
      <div className='max-w-2xl mx-auto'>
        {/* Apple-style Header */}
        <div ref={titleRef} className='text-center mb-12'>
          <div className='w-16 h-16 mx-auto mb-6 bg-blue-500 rounded-2xl flex items-center justify-center shadow-lg'>
            <BuildingOfficeIcon className='h-8 w-8 text-white' />
          </div>
          <h1 className='text-3xl md:text-4xl font-bold text-gray-900 mb-4'>
            Set Up Your Business Profile
          </h1>
          <p className='text-lg text-gray-600 mb-8 leading-relaxed'>
            Complete your business profile to connect with skilled freelancers
          </p>
        </div>

        {/* 🏰 Medieval Progress Bar */}
        <div className='mb-8'>
          <div className='flex justify-between items-center mb-3'>
            <span className='font-cinzel font-medium text-medieval-brown-700'>
              Royal Step {currentStep} of {totalSteps}
            </span>
            <span className='font-cinzel text-sm text-medieval-brown-600'>
              {Math.round((currentStep / totalSteps) * 100)}% Complete
            </span>
          </div>
          <div className='w-full bg-medieval-brown-200 rounded-full h-3 border border-medieval-brown-300'>
            <div
              className='bg-gradient-to-r from-medieval-gold-500 to-medieval-gold-600 h-full rounded-full transition-all duration-500 ease-out border border-medieval-gold-700'
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>

        {/* 🏰 Medieval Form */}
        <div ref={formRef} className='card-medieval p-8 mb-8'>
          {renderStep()}
        </div>

        {/* 🏰 Medieval Navigation Buttons */}
        <div className='flex justify-between'>
          <button
            onClick={prevStep}
            disabled={currentStep === 1}
            className={`btn-medieval-secondary px-6 py-3 font-cinzel transition-all duration-200 ${
              currentStep === 1
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:scale-105'
            }`}
          >
            ← Previous Step
          </button>

          {currentStep < totalSteps ? (
            <button
              onClick={nextStep}
              className='btn-medieval px-6 py-3 font-cinzel hover:scale-105 transition-transform duration-200'
            >
              Continue →
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={loading}
              className='btn-medieval px-8 py-3 font-cinzel text-lg flex items-center space-x-2 hover:scale-105 transition-transform duration-200'
            >
              {loading ? (
                <>
                  <div className='w-5 h-5 border-2 border-medieval-parchment border-t-transparent rounded-full animate-spin' />
                  <span>Establishing House...</span>
                </>
              ) : (
                <>
                  <CheckCircleIcon className='w-5 h-5' />
                  <span>👑 Establish House</span>
                </>
              )}
            </button>
          )}
        </div>

        {/* Error Message */}
        {errors.submit && (
          <div className='mt-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4'>
            <p className='text-sm text-red-600 dark:text-red-400 text-center'>
              {errors.submit}
            </p>
          </div>
        )}
      </div>
    </ApplePageWrapper>
  );
};

export default ClientSetupPage;
