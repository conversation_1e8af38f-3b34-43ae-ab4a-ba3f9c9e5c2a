/**
 * PWA Service for VWork Platform
 * Handles service worker registration and PWA functionality
 */

class PWAService {
  constructor() {
    this.isSupported = 'serviceWorker' in navigator;
    this.registration = null;
    this.isOnline = navigator.onLine;
    this.installPrompt = null;
    
    this.init();
  }

  // Initialize PWA service
  async init() {
    if (!this.isSupported) {
      // Service Workers not supported - silent return in development
      return;
    }

    // Register service worker
    await this.registerServiceWorker();
    
    // Setup online/offline detection
    this.setupNetworkDetection();
    
    // Setup install prompt
    this.setupInstallPrompt();
    
    // Setup push notifications
    this.setupPushNotifications();
  }

  // Register service worker
  async registerServiceWorker() {
    try {
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      console.log('✅ Service Worker registered:', this.registration);

      // Handle updates
      this.registration.addEventListener('updatefound', () => {
        const newWorker = this.registration.installing;
        
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // New version available
            this.showUpdateAvailable();
          }
        });
      });

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', (event) => {
        this.handleServiceWorkerMessage(event);
      });

    } catch (error) {
      console.error('❌ Service Worker registration failed:', error);
    }
  }

  // Setup network detection
  setupNetworkDetection() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.notifyNetworkChange(true);
      console.log('🌐 Back online');
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.notifyNetworkChange(false);
      console.log('📴 Gone offline');
    });
  }

  // Setup install prompt
  setupInstallPrompt() {
    window.addEventListener('beforeinstallprompt', (event) => {
      event.preventDefault();
      this.installPrompt = event;
      this.showInstallBanner();
    });

    window.addEventListener('appinstalled', () => {
      console.log('✅ PWA installed');
      this.installPrompt = null;
      this.hideInstallBanner();
    });
  }

  // Setup push notifications
  async setupPushNotifications() {
    if (!('Notification' in window) || !('PushManager' in window)) {
      console.warn('⚠️ Push notifications not supported');
      return;
    }

    // Check current permission
    if (Notification.permission === 'granted') {
      await this.subscribeToPush();
    }
  }

  // Show update available notification
  showUpdateAvailable() {
    const updateBanner = document.createElement('div');
    updateBanner.id = 'update-banner';
    updateBanner.className = 'fixed top-0 left-0 right-0 bg-blue-600 text-white p-4 z-50 flex items-center justify-between';
    updateBanner.innerHTML = `
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <span>New version available!</span>
      </div>
      <button id="update-btn" class="bg-white text-blue-600 px-4 py-2 rounded font-medium hover:bg-gray-100 transition-colors">
        Update
      </button>
    `;

    document.body.appendChild(updateBanner);

    document.getElementById('update-btn').addEventListener('click', () => {
      this.applyUpdate();
    });

    // Auto-hide after 10 seconds
    setTimeout(() => {
      if (updateBanner.parentNode) {
        updateBanner.remove();
      }
    }, 10000);
  }

  // Apply service worker update
  applyUpdate() {
    if (this.registration && this.registration.waiting) {
      this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    }
  }

  // Show install banner
  showInstallBanner() {
    const installBanner = document.createElement('div');
    installBanner.id = 'install-banner';
    installBanner.className = 'fixed bottom-4 left-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50 flex items-center justify-between';
    installBanner.innerHTML = `
      <div class="flex items-center">
        <img src="/favicon.ico" alt="VWork" class="w-8 h-8 mr-3">
        <div>
          <div class="font-medium text-gray-900">Install VWork</div>
          <div class="text-sm text-gray-600">Get the full app experience</div>
        </div>
      </div>
      <div class="flex space-x-2">
        <button id="install-dismiss" class="text-gray-500 hover:text-gray-700">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
        </button>
        <button id="install-btn" class="bg-blue-600 text-white px-4 py-2 rounded font-medium hover:bg-blue-700 transition-colors">
          Install
        </button>
      </div>
    `;

    document.body.appendChild(installBanner);

    document.getElementById('install-btn').addEventListener('click', () => {
      this.promptInstall();
    });

    document.getElementById('install-dismiss').addEventListener('click', () => {
      installBanner.remove();
    });
  }

  // Hide install banner
  hideInstallBanner() {
    const banner = document.getElementById('install-banner');
    if (banner) {
      banner.remove();
    }
  }

  // Prompt app installation
  async promptInstall() {
    if (!this.installPrompt) return;

    try {
      const result = await this.installPrompt.prompt();
      console.log('Install prompt result:', result);
      
      this.installPrompt = null;
      this.hideInstallBanner();
    } catch (error) {
      console.error('Install prompt error:', error);
    }
  }

  // Request notification permission
  async requestNotificationPermission() {
    if (!('Notification' in window)) {
      console.warn('⚠️ Notifications not supported');
      return false;
    }

    const permission = await Notification.requestPermission();
    
    if (permission === 'granted') {
      await this.subscribeToPush();
      return true;
    }
    
    return false;
  }

  // Subscribe to push notifications
  async subscribeToPush() {
    if (!this.registration) return;

    try {
      const subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(process.env.REACT_APP_VAPID_PUBLIC_KEY || '')
      });

      console.log('✅ Push subscription:', subscription);
      
      // Send subscription to server
      await this.sendSubscriptionToServer(subscription);
      
    } catch (error) {
      console.error('❌ Push subscription failed:', error);
    }
  }

  // Send subscription to server
  async sendSubscriptionToServer(subscription) {
    try {
      const response = await fetch('/api/v1/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscription)
      });

      if (response.ok) {
        console.log('✅ Subscription sent to server');
      }
    } catch (error) {
      console.error('❌ Failed to send subscription to server:', error);
    }
  }

  // Convert VAPID key
  urlBase64ToUint8Array(base64String) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  // Handle service worker messages
  handleServiceWorkerMessage(event) {
    const { data } = event;
    
    if (data.type === 'CACHE_UPDATED') {
      console.log('📦 Cache updated:', data.cacheName);
    }
    
    if (data.type === 'OFFLINE_FALLBACK') {
      this.showOfflineMessage();
    }
  }

  // Show offline message
  showOfflineMessage() {
    const offlineMessage = document.createElement('div');
    offlineMessage.className = 'fixed top-4 right-4 bg-yellow-500 text-white p-4 rounded-lg shadow-lg z-50';
    offlineMessage.innerHTML = `
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>You're offline. Some features may be limited.</span>
      </div>
    `;

    document.body.appendChild(offlineMessage);

    setTimeout(() => {
      if (offlineMessage.parentNode) {
        offlineMessage.remove();
      }
    }, 5000);
  }

  // Notify network change
  notifyNetworkChange(isOnline) {
    window.dispatchEvent(new CustomEvent('networkchange', {
      detail: { isOnline }
    }));
  }

  // Get cache statistics
  async getCacheStats() {
    if (!this.registration) return null;

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel();
      
      messageChannel.port1.onmessage = (event) => {
        resolve(event.data);
      };
      
      this.registration.active.postMessage(
        { type: 'GET_CACHE_STATS' },
        [messageChannel.port2]
      );
    });
  }

  // Check if app is installed
  isInstalled() {
    return window.matchMedia('(display-mode: standalone)').matches ||
           window.navigator.standalone === true;
  }

  // Get network status
  getNetworkStatus() {
    return {
      isOnline: this.isOnline,
      connection: navigator.connection || navigator.mozConnection || navigator.webkitConnection
    };
  }
}

// Create singleton instance
const pwaService = new PWAService();

export default pwaService;
