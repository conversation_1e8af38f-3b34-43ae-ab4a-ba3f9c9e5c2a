rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow reading user profiles for other authenticated users (for freelancer listings)
    match /users/{userId} {
      allow read: if request.auth != null;
    }
    
    // Projects - clients can create, anyone can read
    match /projects/{projectId} {
      allow create: if request.auth != null;
      allow read: if request.auth != null;
      allow update, delete: if request.auth != null && 
        (request.auth.uid == resource.data.clientId || 
         request.auth.uid == resource.data.freelancerId);
    }
    
    // Bids - freelancers can create, project participants can read
    match /projects/{projectId}/bids/{bidId} {
      allow create: if request.auth != null;
      allow read, update: if request.auth != null;
    }
    
    // Messages - only participants can read/write
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.participants;
    }
    
    match /conversations/{conversationId}/messages/{messageId} {
      allow read, write: if request.auth != null;
    }
    
    // Community posts - authenticated users can read/write
    match /community/{postId} {
      allow read, write: if request.auth != null;
    }
    
    // Notifications - users can read their own notifications
    match /notifications/{notificationId} {
      allow read, update: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null;
    }
  }
} 