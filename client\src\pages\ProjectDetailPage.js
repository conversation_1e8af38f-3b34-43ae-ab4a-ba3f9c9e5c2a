import React from 'react';
import { useParams } from 'react-router-dom';

const ProjectDetailPage = () => {
  const { id } = useParams();

  return (
    <div
      className='min-h-screen medieval-selection'
      style={{
        background: `
          linear-gradient(135deg, rgba(74, 55, 40, 0.8) 0%, rgba(139, 30, 30, 0.6) 25%, rgba(212, 160, 23, 0.7) 50%, rgba(26, 46, 74, 0.8) 75%, rgba(74, 55, 40, 0.8) 100%),
          url("data:image/svg+xml,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='quest-detail-parchment' x='0' y='0' width='60' height='60' patternUnits='userSpaceOnUse'%3E%3Cpath d='M30 5 L35 20 L50 20 L38 30 L43 45 L30 35 L17 45 L22 30 L10 20 L25 20 Z' fill='%23D4A017' opacity='0.15'/%3E%3Cpath d='M10 10 L15 5 L20 10 L15 15 Z' fill='%23D4A017' opacity='0.1'/%3E%3Cpath d='M45 50 L50 45 L55 50 L50 55 Z' fill='%23D4A017' opacity='0.1'/%3E%3Ccircle cx='20' cy='40' r='3' stroke='%23D4A017' stroke-width='1' fill='none' opacity='0.1'/%3E%3Ccircle cx='50' cy='20' r='2' fill='%23D4A017' opacity='0.1'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100' height='100' fill='url(%23quest-detail-parchment)'/%3E%3C/svg%3E")
        `,
      }}
    >
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='text-center mb-12'>
          <h1 className='font-cinzel-decorative text-4xl md:text-5xl font-bold text-medieval-brown-800 mb-4'>
            📜 Quest Details
          </h1>
          <p className='font-cinzel text-xl text-medieval-brown-600'>
            Quest ID: {id} - Details coming soon
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProjectDetailPage;
