import React, { useRef, useEffect, useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { SplitText } from 'gsap/SplitText';
import { ScrambleTextPlugin } from 'gsap/ScrambleTextPlugin';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';
import { Physics2DPlugin } from 'gsap/Physics2DPlugin';
import { CustomEase } from 'gsap/CustomEase';
import { motion } from 'framer-motion';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useAuth } from '../../../contexts/AuthContext';
import {
  ArrowRightIcon,
  SparklesIcon,
  UserGroupIcon,
  BriefcaseIcon,
  TrophyIcon,
  PlayIcon,
} from '@heroicons/react/24/outline';

// Register GSAP plugins
gsap.registerPlugin(<PERSON><PERSON>Trigger, SplitText, <PERSON>rambleTextPlugin, MotionPathPlugin, Physics2DPlugin, CustomEase);

// Custom eases for premium animations
const premiumEases = {
  elastic: CustomEase.create("elastic", "M0,0 C0.25,0 0.4,1.4 0.7,1 C0.85,0.8 1,1 1,1"),
  bounce: CustomEase.create("bounce", "M0,0 C0.14,0 0.242,0.438 0.272,0.561 0.313,0.728 0.354,0.963 0.362,1 0.37,0.985 0.414,0.928 0.455,0.879 0.504,0.822 0.565,0.729 0.621,0.653 0.681,0.573 0.737,0.5 0.785,0.5 0.856,0.5 0.923,0.717 1,1"),
  liquid: CustomEase.create("liquid", "M0,0 C0.29,0.01 0.49,1.53 0.59,1.23 C0.69,0.93 1,1 1,1")
};

const AppleHeroSection = () => {
  const { t } = useLanguage();
  const { isAuthenticated } = useAuth();

  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const ctaRef = useRef(null);
  const statsRef = useRef(null);
  const floatingElementsRef = useRef(null);
  const mainTextRef = useRef(null);
  const marketplaceTextRef = useRef(null);
  const dynamicTextRef = useRef(null);
  const particleCanvasRef = useRef(null);
  const magneticElementsRef = useRef([]);
  const parallaxLayersRef = useRef([]);
  
  // Dynamic text for advanced animations
  const dynamicTexts = [
    t('findYourDreamJob'),
    t('hireTopTalent'),
    t('buildYourCareer'),
    t('growYourBusiness'),
    t('perfectMatches'),
    t('creativeSolutions'),
  ];

  // Advanced Magnetic Effect
  const createMagneticEffect = (element) => {
    if (!element) return;
    
    const handleMouseMove = (e) => {
      const rect = element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      const deltaX = (e.clientX - centerX) * 0.15;
      const deltaY = (e.clientY - centerY) * 0.15;
      
      gsap.to(element, {
        x: deltaX,
        y: deltaY,
        duration: 0.3,
        ease: "power2.out"
      });
    };
    
    const handleMouseLeave = () => {
      gsap.to(element, {
        x: 0,
        y: 0,
        duration: 0.5,
        ease: premiumEases.elastic
      });
    };
    
    element.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      element.removeEventListener('mousemove', handleMouseMove);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  };

  // Advanced Text Animation with SplitText and Scramble
  const createAdvancedTextAnimation = () => {
    if (!mainTextRef.current || !marketplaceTextRef.current) return;

    // Split text into characters for advanced animation
    const mainSplit = new SplitText(mainTextRef.current, { type: "chars,words" });
    const marketplaceSplit = new SplitText(marketplaceTextRef.current, { type: "chars,words" });
    
    // Set initial state
    gsap.set([mainSplit.chars, marketplaceSplit.chars], {
      opacity: 0,
      y: 100,
      rotationX: -90,
      transformOrigin: "center bottom"
    });

    // Create master timeline
    const masterTL = gsap.timeline();

    // Animate main text with 3D effect
    masterTL.to(mainSplit.chars, {
      opacity: 1,
      y: 0,
      rotationX: 0,
      duration: 1.2,
      stagger: {
        amount: 0.8,
        from: "start"
      },
      ease: premiumEases.bounce
    })
    // Animate marketplace text with scramble effect
    .to(marketplaceSplit.chars, {
      opacity: 1,
      y: 0,
      rotationX: 0,
      duration: 1.0,
      stagger: {
        amount: 0.6,
        from: "center"
      },
      ease: premiumEases.elastic
    }, "-=0.5")
    // Add scramble effect to marketplace text
    .to(marketplaceTextRef.current, {
      scrambleText: {
        text: t('marketplace'),
        chars: "XO",
        speed: 0.3
      },
      duration: 1.5
    }, "-=0.8");

    return masterTL;
  };

  // Advanced Particle System with Physics
  const createParticleSystem = () => {
    if (!particleCanvasRef.current) return;

    const particles = [];
    const numParticles = 50;
    
    for (let i = 0; i < numParticles; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-1 h-1 bg-blue-400/30 rounded-full';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.top = Math.random() * 100 + '%';
      particleCanvasRef.current.appendChild(particle);
      particles.push(particle);
    }

    // Animate particles with physics
    particles.forEach((particle, index) => {
      gsap.set(particle, {
        physics2D: {
          velocity: (Math.random() - 0.5) * 100,
          angle: Math.random() * 360,
          gravity: 50
        }
      });
      
      gsap.to(particle, {
        physics2D: {
          velocity: (Math.random() - 0.5) * 150,
          angle: Math.random() * 360
        },
        duration: 10 + Math.random() * 10,
        repeat: -1,
        yoyo: true,
        ease: "none"
      });
      
      // Add floating animation
      gsap.to(particle, {
        scale: 1.5,
        opacity: 0.8,
        duration: 2 + Math.random() * 3,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        delay: Math.random() * 2
      });
    });
  };

  // Motion Path Animation for Floating Elements
  const createMotionPathAnimation = () => {
    if (!floatingElementsRef.current) return;

    const elements = Array.from(floatingElementsRef.current.children);
    
    elements.forEach((element, index) => {
      // Create complex motion path
      const path = `M${Math.random() * 100},${Math.random() * 100} 
                    Q${Math.random() * 200},${Math.random() * 200} 
                    ${Math.random() * 300},${Math.random() * 300}
                    T${Math.random() * 400},${Math.random() * 400}`;
      
      gsap.to(element, {
        motionPath: {
          path: path,
          autoRotate: true,
          alignOrigin: [0.5, 0.5]
        },
        duration: 15 + index * 5,
        repeat: -1,
        ease: "none"
      });
      
      // Add scale animation
      gsap.to(element, {
        scale: 1.3,
        duration: 3 + Math.random() * 2,
        repeat: -1,
        yoyo: true,
        ease: premiumEases.liquid
      });
    });
  };

  // Advanced Typing Effect with Scramble
  const createAdvancedTypingEffect = () => {
    if (!dynamicTextRef.current) return;

    let currentIndex = 0;
    
    const animateText = () => {
      const currentText = dynamicTexts[currentIndex];
      
      // Scramble out current text
      gsap.to(dynamicTextRef.current, {
        scrambleText: {
          text: "",
          chars: "XO01",
          speed: 0.5
        },
        duration: 0.5,
        onComplete: () => {
          // Scramble in new text
          gsap.to(dynamicTextRef.current, {
            scrambleText: {
              text: currentText,
              chars: "XO01",
              speed: 0.3
            },
            duration: 1.5,
            onComplete: () => {
              setTimeout(() => {
                currentIndex = (currentIndex + 1) % dynamicTexts.length;
                animateText();
              }, 2000);
            }
          });
        }
      });
    };
    
    setTimeout(animateText, 1500);
  };

  // Multi-layer Parallax with Depth
  const createAdvancedParallax = () => {
    parallaxLayersRef.current.forEach((layer, index) => {
      if (!layer) return;
      
      const depth = (index + 1) * 0.1;
      const scale = 1 + depth;
      
      gsap.to(layer, {
        yPercent: -50 * depth,
        scale: scale,
        rotation: depth * 5,
        scrollTrigger: {
          trigger: heroRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });
    });
  };

  // Main animation initialization
  useEffect(() => {
    const ctx = gsap.context(() => {
      // Initialize all advanced animations
      const textTL = createAdvancedTextAnimation();
      createParticleSystem();
      createMotionPathAnimation();
      createAdvancedTypingEffect();
      createAdvancedParallax();
      
      // Add magnetic effects to CTA buttons
      magneticElementsRef.current.forEach(element => {
        createMagneticEffect(element);
      });
      
      // Master entrance animation
      const masterTL = gsap.timeline();
      
      masterTL
        .from(heroRef.current, {
          opacity: 0,
          duration: 0.1
        })
        .add(textTL, 0.5)
        .from(subtitleRef.current, {
          opacity: 0,
          y: 30,
          duration: 1.2,
          ease: premiumEases.elastic
        }, "-=0.5")
        .from(ctaRef.current?.children || [], {
          opacity: 0,
          y: 40,
          scale: 0.8,
          duration: 1.0,
          stagger: 0.2,
          ease: premiumEases.bounce
        }, "-=0.3")
        .from(statsRef.current?.children || [], {
          opacity: 0,
          y: 50,
          rotationY: 45,
          duration: 1.2,
          stagger: 0.1,
          ease: premiumEases.elastic
        }, "-=0.2");
        
    }, heroRef);

    return () => ctx.revert();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const stats = [
    { icon: UserGroupIcon, value: '2M+', label: t('activeUsers') },
    { icon: BriefcaseIcon, value: '500K+', label: t('projectsCompleted') },
    { icon: TrophyIcon, value: '98%', label: t('successRate') },
    { icon: SparklesIcon, value: '4.9/5', label: t('averageRating') },
  ];

  return (
    <>
      {/* Enhanced CSS with 3D transforms */}
      <style>{`
        @keyframes liquid-gradient {
          0%, 100% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
        }
        
        .liquid-bg {
          background: linear-gradient(-45deg, #3B82F6, #8B5CF6, #06B6D4, #10B981);
          background-size: 400% 400%;
          animation: liquid-gradient 8s ease infinite;
        }
        
        .text-3d {
          transform-style: preserve-3d;
          perspective: 1000px;
        }
        
        .magnetic-element {
          transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .floating-particle {
          will-change: transform;
          animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
      `}</style>

      <section
        ref={heroRef}
        className='relative pt-20 pb-16 sm:pt-24 sm:pb-20 lg:pt-32 lg:pb-28 overflow-hidden min-h-screen flex items-center'
      >
        {/* Advanced Multi-layer Background */}
        <div className='absolute inset-0'>
          <div 
            ref={el => parallaxLayersRef.current[0] = el}
            className='absolute inset-0 liquid-bg opacity-5'
          />
          <div 
            ref={el => parallaxLayersRef.current[1] = el}
            className='absolute inset-0 bg-gradient-radial from-blue-500/10 via-transparent to-purple-500/10'
          />
          <div 
            ref={el => parallaxLayersRef.current[2] = el}
            className='absolute inset-0 bg-gradient-conic from-blue-400/5 via-purple-400/5 to-cyan-400/5'
          />
        </div>

        {/* Advanced Particle Canvas */}
        <div 
          ref={particleCanvasRef}
          className='absolute inset-0 pointer-events-none overflow-hidden'
        />

        {/* Enhanced Floating Elements with Physics */}
        <div
          ref={floatingElementsRef}
          className='absolute inset-0 pointer-events-none'
        >
          <div className='absolute top-20 left-10 w-12 h-12 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full backdrop-blur-sm border border-blue-400/30 floating-particle'>
            <div className='w-full h-full bg-gradient-to-br from-blue-500/30 to-cyan-500/30 rounded-full animate-pulse' />
          </div>
          <div className='absolute top-40 right-20 w-8 h-8 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-lg backdrop-blur-sm border border-purple-400/30 floating-particle'>
            <div className='w-full h-full bg-gradient-to-br from-purple-500/30 to-pink-500/30 rounded-lg animate-pulse' />
          </div>
          <div className='absolute bottom-40 left-20 w-16 h-16 bg-gradient-to-br from-green-400/20 to-emerald-400/20 rounded-xl backdrop-blur-sm border border-green-400/30 floating-particle'>
            <div className='w-full h-full bg-gradient-to-br from-green-500/30 to-emerald-500/30 rounded-xl animate-pulse' />
          </div>
        </div>

        <div className='relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          <div className='text-center'>
            {/* Advanced 3D Text Animation */}
            <div ref={titleRef} className='mb-8 text-3d'>
              <h1 className='text-4xl sm:text-5xl lg:text-7xl font-bold tracking-tight mb-4'>
                <div className='flex flex-wrap items-center justify-center gap-x-4 gap-y-2'>
                  <span
                    ref={mainTextRef}
                    className='text-gray-900 dark:text-gray-100 text-3d transition-colors duration-300'
                  >
                    {t('worldsWork')}
                  </span>
                  <span
                    ref={marketplaceTextRef}
                    className='liquid-bg bg-clip-text text-transparent font-extrabold text-3d relative z-10'
                    style={{
                      background: 'linear-gradient(-45deg, #3B82F6, #8B5CF6, #06B6D4, #10B981)',
                      backgroundSize: '400% 400%',
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      color: 'transparent',
                      animation: 'liquid-gradient 8s ease infinite'
                    }}
                  >
                    {t('marketplace')}
                  </span>
                </div>
              </h1>
              <div className='text-2xl sm:text-3xl lg:text-5xl font-semibold text-gray-700 dark:text-gray-300 mt-8 h-20 flex items-center justify-center transition-colors duration-300'>
                <span 
                  ref={dynamicTextRef}
                  className='bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent font-bold tracking-wide text-3d'
                >
                  {dynamicTexts[0]}
                </span>
                <span className='animate-pulse text-blue-600 dark:text-blue-400 ml-2 text-4xl'>
                  |
                </span>
              </div>
            </div>

            {/* Enhanced Subtitle */}
            <div ref={subtitleRef} className='mb-12'>
              <p className='text-xl sm:text-2xl lg:text-3xl text-gray-600 dark:text-gray-400 max-w-4xl mx-auto leading-relaxed transition-colors duration-300'>
                {t('connectSubtitle')}
              </p>
            </div>

            {/* Magnetic CTA Buttons */}
            <div ref={ctaRef} className='mb-20'>
              <div className='flex flex-col sm:flex-row gap-6 justify-center items-center'>
                {!isAuthenticated ? (
                  <>
                    <Link
                      ref={el => magneticElementsRef.current[0] = el}
                      to='/auth?mode=signup'
                      className='group magnetic-element inline-flex items-center px-10 py-5 text-xl font-bold text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:scale-105'
                    >
                      <span>{t('getStarted')}</span>
                      <ArrowRightIcon className='ml-3 h-6 w-6 group-hover:translate-x-2 transition-transform duration-300' />
                    </Link>

                    <button 
                      ref={el => magneticElementsRef.current[1] = el}
                      className='group magnetic-element inline-flex items-center px-10 py-5 text-xl font-semibold text-gray-700 dark:text-gray-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-gray-700 rounded-2xl border-2 border-gray-200 dark:border-gray-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105'
                    >
                      <PlayIcon className='mr-3 h-6 w-6' />
                      <span>{t('watchDemo')}</span>
                    </button>
                  </>
                ) : (
                  <>
                    <Link
                      ref={el => magneticElementsRef.current[0] = el}
                      to='/projects'
                      className='group magnetic-element inline-flex items-center px-10 py-5 text-xl font-bold text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:scale-105'
                    >
                      <span>{t('findWork')}</span>
                      <ArrowRightIcon className='ml-3 h-6 w-6 group-hover:translate-x-2 transition-transform duration-300' />
                    </Link>

                    <Link
                      ref={el => magneticElementsRef.current[1] = el}
                      to='/freelancers'
                      className='group magnetic-element inline-flex items-center px-10 py-5 text-xl font-semibold text-gray-700 dark:text-gray-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm hover:bg-white dark:hover:bg-gray-700 rounded-2xl border-2 border-gray-200 dark:border-gray-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105'
                    >
                      <UserGroupIcon className='mr-3 h-6 w-6' />
                      <span>{t('findTalent')}</span>
                    </Link>

                    <Link
                      ref={el => magneticElementsRef.current[2] = el}
                      to='/jobs/create'
                      className='group magnetic-element inline-flex items-center px-10 py-5 text-xl font-bold text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:scale-105'
                    >
                      <BriefcaseIcon className='mr-3 h-6 w-6' />
                      <span>{t('postJob')}</span>
                    </Link>
                  </>
                )}
              </div>
            </div>

            {/* Enhanced 3D Stats Section */}
            <div
              ref={statsRef}
              className='grid grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto'
            >
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 50, rotationY: 45 }}
                  animate={{ opacity: 1, y: 0, rotationY: 0 }}
                  transition={{ duration: 1.2, delay: index * 0.1, ease: "backOut" }}
                  className='group text-center transform-gpu'
                >
                  <div className='relative'>
                    <div className='inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-2xl mb-6 group-hover:from-blue-50 dark:group-hover:from-blue-900/30 group-hover:to-blue-100 dark:group-hover:to-blue-900/50 transition-all duration-500 shadow-lg group-hover:shadow-xl transform group-hover:scale-110 group-hover:-rotate-3'>
                      <stat.icon className='h-8 w-8 text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-all duration-500 transform group-hover:scale-110' />
                    </div>
                    <div className='text-3xl sm:text-4xl font-black text-gray-900 dark:text-gray-100 mb-2 transition-colors duration-300 group-hover:text-blue-600 dark:group-hover:text-blue-400'>
                      {stat.value}
                    </div>
                    <div className='text-base font-medium text-gray-600 dark:text-gray-400 transition-colors duration-300 group-hover:text-gray-800 dark:group-hover:text-gray-200'>
                      {stat.label}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default AppleHeroSection;

