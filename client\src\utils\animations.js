import { gsap, ScrollTrigger } from './gsapConfig';

/**
 * Animation Utilities for VWork Freelancer Marketplace
 * Apple-inspired animations with GSAP
 */

export class AnimationController {
  constructor() {
    this.isReducedMotion = window.matchMedia(
      '(prefers-reduced-motion: reduce)'
    ).matches;
    this.isMobile = window.innerWidth < 768;

    // Initialize motion preferences
    this.initializeMotionSettings();
  }

  // Initialize motion settings based on user preferences
  initializeMotionSettings() {
    if (this.isReducedMotion) {
      gsap.globalTimeline.timeScale(0.1);
    }
  }

  // Utility method to check if element is in viewport
  isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <=
        (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }

  // Apple-style fade in animation
  fadeIn(elements, options = {}) {
    if (!elements) return null;

    // Validate elements exist in DOM
    const validElements = Array.isArray(elements)
      ? elements.filter(el => el && el.nodeType === 1)
      : (elements && elements.nodeType === 1) ? elements : null;

    if (!validElements || (Array.isArray(validElements) && validElements.length === 0)) {
      console.warn('GSAP fadeIn: No valid elements found');
      return null;
    }

    const defaults = {
      duration: 0.8,
      delay: 0,
      stagger: 0.1,
      y: 30,
      ease: 'power2.out',
    };
    const config = { ...defaults, ...options };

    return gsap.fromTo(
      validElements,
      {
        opacity: 0,
        y: config.y,
        scale: 0.95,
      },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: config.duration,
        delay: config.delay,
        stagger: config.stagger,
        ease: config.ease,
      }
    );
  }

  // Apple-style scale animation
  scaleIn(elements, options = {}) {
    if (!elements) return null;

    const defaults = {
      duration: 0.6,
      delay: 0,
      stagger: 0.05,
      ease: 'back.out(1.7)',
    };
    const config = { ...defaults, ...options };

    return gsap.fromTo(
      elements,
      {
        scale: 0,
        opacity: 0,
      },
      {
        scale: 1,
        opacity: 1,
        duration: config.duration,
        delay: config.delay,
        stagger: config.stagger,
        ease: config.ease,
      }
    );
  }

  // Apple-style slide in animation
  slideIn(elements, options = {}) {
    if (!elements) return null;

    const defaults = {
      duration: 0.8,
      delay: 0,
      stagger: 0.1,
      direction: 'up', // 'up', 'down', 'left', 'right'
      distance: 50,
      ease: 'power2.out',
    };
    const config = { ...defaults, ...options };

    // Calculate initial position based on direction
    let fromProps = { opacity: 0 };
    let toProps = { opacity: 1 };

    switch (config.direction) {
      case 'up':
        fromProps.y = config.distance;
        toProps.y = 0;
        break;
      case 'down':
        fromProps.y = -config.distance;
        toProps.y = 0;
        break;
      case 'left':
        fromProps.x = config.distance;
        toProps.x = 0;
        break;
      case 'right':
        fromProps.x = -config.distance;
        toProps.x = 0;
        break;
      default:
        fromProps.y = config.distance;
        toProps.y = 0;
    }

    return gsap.fromTo(
      elements,
      fromProps,
      {
        ...toProps,
        duration: config.duration,
        delay: config.delay,
        stagger: config.stagger,
        ease: config.ease,
      }
    );
  }

  // Smooth parallax effect
  parallax(element, options = {}) {
    if (!element) return null;

    const defaults = {
      yPercent: -50,
      ease: 'none',
    };
    const config = { ...defaults, ...options };

    return gsap.to(element, {
      yPercent: config.yPercent,
      ease: config.ease,
      scrollTrigger: {
        trigger: element,
        start: 'top bottom',
        end: 'bottom top',
        scrub: true,
      },
    });
  }

  // Text reveal animation (simulating SplitText)
  textReveal(element, options = {}) {
    if (!element?.textContent) return null;

    const defaults = {
      duration: 1,
      stagger: 0.05,
      ease: 'power2.out',
    };
    const config = { ...defaults, ...options };

    // Split text into spans for each character
    const text = element.textContent;
    const chars = text
      .split('')
      .map(char =>
        char === ' ' ? '<span>&nbsp;</span>' : `<span>${char}</span>`
      )
      .join('');

    element.innerHTML = chars;
    const charElements = element.querySelectorAll('span');

    return gsap.fromTo(
      charElements,
      {
        opacity: 0,
        y: 50,
        rotationX: -90,
      },
      {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration: config.duration,
        stagger: config.stagger,
        ease: config.ease,
      }
    );
  }

  // Card hover animation
  cardHover(card) {
    const tl = gsap.timeline({ paused: true });

    tl.to(card, {
      y: -10,
      scale: 1.02,
      boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
      duration: 0.3,
      ease: 'power2.out',
    });

    card.addEventListener('mouseenter', () => tl.play());
    card.addEventListener('mouseleave', () => tl.reverse());

    return tl;
  }

  // 3D tilt effect for cards
  cardTilt(card) {
    card.addEventListener('mousemove', e => {
      if (this.isMobile) return;

      const rect = card.getBoundingClientRect();
      const x = e.clientX - rect.left - rect.width / 2;
      const y = e.clientY - rect.top - rect.height / 2;

      gsap.to(card, {
        rotationY: x / 10,
        rotationX: -y / 10,
        duration: 0.5,
        transformPerspective: 1000,
        ease: 'power2.out',
      });
    });

    card.addEventListener('mouseleave', () => {
      gsap.to(card, {
        rotationY: 0,
        rotationX: 0,
        duration: 0.5,
        ease: 'power2.out',
      });
    });
  }

  // Staggered grid animation
  staggerGrid(elements, options = {}) {
    if (!elements) return null;

    const defaults = {
      duration: 0.6,
      stagger: {
        grid: 'auto',
        from: 'start',
        amount: 0.5,
      },
      ease: 'power2.out',
    };
    const config = { ...defaults, ...options };

    return gsap.fromTo(
      elements,
      {
        opacity: 0,
        y: 50,
        scale: 0.8,
      },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: config.duration,
        stagger: config.stagger,
        ease: config.ease,
      }
    );
  }

  // Smooth scroll to element
  scrollTo(target, options = {}) {
    const defaults = {
      duration: 1,
      ease: 'power2.inOut',
      offsetY: -100,
    };
    const config = { ...defaults, ...options };

    return gsap.to(window, {
      duration: config.duration,
      scrollTo: {
        y: target,
        offsetY: config.offsetY,
      },
      ease: config.ease,
    });
  }

  // Loading animation
  loading(element) {
    const tl = gsap.timeline({ repeat: -1 });

    tl.to(element, {
      rotation: 360,
      duration: 1,
      ease: 'none',
    });

    return tl;
  }

  // Counter animation
  counter(element, endValue, options = {}) {
    const defaults = {
      duration: 2,
      ease: 'power2.out',
    };
    const config = { ...defaults, ...options };

    const obj = { value: 0 };

    return gsap.to(obj, {
      value: endValue,
      duration: config.duration,
      ease: config.ease,
      onUpdate: () => {
        // Check if element still exists before updating
        if (element && element.textContent !== undefined) {
          element.textContent = Math.round(obj.value);
        }
      },
    });
  }

  // Enhanced cleanup method
  cleanup() {
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    gsap.globalTimeline.clear();
    gsap.killTweensOf('*');
  }

  // Safe cleanup for component unmount - avoid affecting other animations
  static safeCleanup(targetElements = null) {
    if (targetElements) {
      // Clean up specific elements only
      if (Array.isArray(targetElements)) {
        targetElements.forEach(element => {
          if (element) {
            gsap.killTweensOf(element);
            gsap.set(element, { clearProps: 'all' });
          }
        });
      } else {
        gsap.killTweensOf(targetElements);
        gsap.set(targetElements, { clearProps: 'all' });
      }
    }
  }

  // Global cleanup for emergency situations only
  static globalCleanup() {
    console.warn('🚨 Global cleanup called - this may affect other animations');
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    gsap.globalTimeline.clear();
    // Avoid global killTweensOf('*') as it affects all animations
    // gsap.killTweensOf('*');
    // gsap.set('*', { clearProps: 'all' });
  }
}

// Export singleton instance
export const animationController = new AnimationController();

// Export individual animation functions for convenience
export const {
  fadeIn,
  scaleIn,
  slideIn,
  parallax,
  textReveal,
  cardHover,
  cardTilt,
  staggerGrid,
  scrollTo,
  loading,
  counter,
} = animationController;
