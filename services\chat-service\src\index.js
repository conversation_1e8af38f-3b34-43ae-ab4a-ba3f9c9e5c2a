const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

// Import local utilities
const { responseMiddleware } = require('./utils');

const app = express();
const PORT = process.env.PORT || 3005;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token']
}));
app.use(morgan('combined'));
app.use(express.json());
app.use(responseMiddleware);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'Chat Service',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Chat routes
app.get('/messages', (req, res) => {
  console.log('💬 Get messages request');
  res.json({
    success: true,
    message: 'Messages retrieved successfully',
    data: []
  });
});

app.post('/messages', (req, res) => {
  console.log('📨 Send message request:', req.body);
  res.json({
    success: true,
    message: 'Message sent successfully',
    data: { id: Date.now(), ...req.body }
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Chat Service running on port ${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
