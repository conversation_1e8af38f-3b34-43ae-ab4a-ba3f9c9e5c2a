#!/usr/bin/env node

/**
 * Render Environment Setup Script
 * Helps configure environment variables for Render deployment
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`);
};

// Environment variables template
const envTemplate = {
  auth: {
    NODE_ENV: 'production',
    PORT: '3001',
    FIREBASE_PROJECT_ID: 'your-firebase-project-id',
    FIREBASE_SERVICE_ACCOUNT_KEY: '{"type":"service_account",...}',
    CORS_ORIGINS: 'https://vwork-client.onrender.com,https://vwork-gateway.onrender.com'
  },
  user: {
    NODE_ENV: 'production',
    PORT: '3002',
    AUTH_SERVICE_URL: 'https://vwork-auth-service.onrender.com',
    CORS_ORIGINS: 'https://vwork-client.onrender.com,https://vwork-gateway.onrender.com'
  },
  project: {
    NODE_ENV: 'production',
    PORT: '3003',
    AUTH_SERVICE_URL: 'https://vwork-auth-service.onrender.com',
    CORS_ORIGINS: 'https://vwork-client.onrender.com,https://vwork-gateway.onrender.com'
  },
  job: {
    NODE_ENV: 'production',
    PORT: '3004',
    AUTH_SERVICE_URL: 'https://vwork-auth-service.onrender.com',
    CORS_ORIGINS: 'https://vwork-client.onrender.com,https://vwork-gateway.onrender.com'
  },
  chat: {
    NODE_ENV: 'production',
    PORT: '3005',
    AUTH_SERVICE_URL: 'https://vwork-auth-service.onrender.com',
    CORS_ORIGINS: 'https://vwork-client.onrender.com,https://vwork-gateway.onrender.com'
  },
  gateway: {
    NODE_ENV: 'production',
    PORT: '10000',
    AUTH_SERVICE_URL: 'https://vwork-auth-service.onrender.com',
    USER_SERVICE_URL: 'https://vwork-user-service.onrender.com',
    PROJECT_SERVICE_URL: 'https://vwork-project-service.onrender.com',
    JOB_SERVICE_URL: 'https://vwork-job-service.onrender.com',
    CHAT_SERVICE_URL: 'https://vwork-chat-service.onrender.com',
    SEARCH_SERVICE_URL: 'https://vwork-search-service.onrender.com',
    CORS_ORIGIN: 'https://vwork-client.onrender.com'
  }
};

// Generate environment setup instructions
const generateEnvSetup = () => {
  log('🔧 Render Environment Setup Guide', 'bright');
  log('================================', 'bright');
  
  Object.entries(envTemplate).forEach(([service, vars]) => {
    log(`\n📋 ${service.toUpperCase()} SERVICE:`, 'cyan');
    log('='.repeat(service.length + 9), 'cyan');
    
    Object.entries(vars).forEach(([key, value]) => {
      log(`${key}=${value}`, 'blue');
    });
  });
  
  log('\n💡 Setup Instructions:', 'yellow');
  log('====================', 'yellow');
  log('1. Go to Render Dashboard', 'cyan');
  log('2. Select each service', 'cyan');
  log('3. Go to Environment tab', 'cyan');
  log('4. Add each variable listed above', 'cyan');
  log('5. For Firebase credentials:', 'cyan');
  log('   - Get service account key from Firebase Console', 'cyan');
  log('   - Copy the entire JSON as FIREBASE_SERVICE_ACCOUNT_KEY', 'cyan');
  log('   - Set FIREBASE_PROJECT_ID to your project ID', 'cyan');
};

// Generate .env files for local testing
const generateLocalEnvFiles = () => {
  log('\n📁 Generating local .env files...', 'cyan');
  
  Object.entries(envTemplate).forEach(([service, vars]) => {
    const servicePath = path.join(process.cwd(), 'services', `${service}-service`);
    const envPath = path.join(servicePath, '.env');
    
    // Create service directory if it doesn't exist
    if (!fs.existsSync(servicePath)) {
      fs.mkdirSync(servicePath, { recursive: true });
    }
    
    // Generate .env content
    const envContent = Object.entries(vars)
      .map(([key, value]) => `${key}=${value}`)
      .join('\n');
    
    fs.writeFileSync(envPath, envContent);
    log(`✅ Created ${servicePath}/.env`, 'green');
  });
  
  log('\n✅ Local .env files generated successfully', 'green');
};

// Main function
const main = () => {
  const args = process.argv.slice(2);
  
  if (args.includes('--local')) {
    generateLocalEnvFiles();
  } else {
    generateEnvSetup();
  }
  
  log('\n🎯 Next Steps:', 'bright');
  log('==============', 'bright');
  log('1. Deploy services to Render', 'cyan');
  log('2. Configure environment variables', 'cyan');
  log('3. Test health endpoints', 'cyan');
  log('4. Deploy client application', 'cyan');
};

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { envTemplate, generateEnvSetup, generateLocalEnvFiles }; 