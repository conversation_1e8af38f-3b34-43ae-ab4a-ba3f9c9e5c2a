import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { gsap } from 'gsap';
import {
  ChatBubbleLeftRightIcon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  PaperAirplaneIcon,
  BriefcaseIcon,
  DocumentTextIcon,
  TrophyIcon,
  RocketLaunchIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';
import './QuickChatWidget.css';
import { apiService } from '../../services/api';
import { useLanguage } from '../../contexts/LanguageContext';

const QuickChatWidget = () => {
  const { t } = useLanguage();
  const [isExpanded, setIsExpanded] = useState(false);
  const [showChatWindow, setShowChatWindow] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState([
    {
      id: 1,
      content:
        'Xin chào! T<PERSON><PERSON> l<PERSON> VWork Assistant, tr<PERSON> lý AI của nền tảng VWork. Tô<PERSON> có thể giúp bạn tìm hiểu về dự án, freelancer và hướng dẫn sử dụng nền tảng. Bạn cần hỗ trợ gì không?',
      isBot: true,
      timestamp: new Date().toISOString(),
      role: 'model',
    },
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Refs for GSAP animations
  const mainButtonRef = useRef(null);
  const circlesContainerRef = useRef(null);
  const chatWindowRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (showChatWindow && !isMinimized && inputRef.current) {
      inputRef.current.focus();
    }
  }, [showChatWindow, isMinimized]);

  // Enhanced GSAP Animation functions
  const expandCircles = () => {
    if (isExpanded) return;

    setIsExpanded(true);

    // Wait for next frame to ensure DOM is updated
    requestAnimationFrame(() => {
      if (!circlesContainerRef.current) return;

      // Create timeline for coordinated animations
      const tl = gsap.timeline();

      // Animate main button with beautiful rotation and scale
      tl.to(mainButtonRef.current, {
        scale: 0.9,
        rotation: 225,
        duration: 0.5,
        ease: 'elastic.out(1, 0.6)'
      })

      // Show and animate circles with enhanced stagger and physics
      .set(circlesContainerRef.current.children, {
        scale: 0,
        opacity: 0,
        rotation: -360
      })
      .to(circlesContainerRef.current.children, {
        scale: 1,
        opacity: 1,
        rotation: 0,
        duration: 0.8,
        stagger: {
          amount: 0.4,
          from: 'start',
          ease: 'power2.out'
        },
        ease: 'elastic.out(1, 0.5)',
        delay: 0.1
      }, '-=0.3')

      // Add subtle floating animation to circles
      .to(circlesContainerRef.current.children, {
        y: '+=3',
        duration: 2,
        ease: 'sine.inOut',
        yoyo: true,
        repeat: -1,
        stagger: 0.2
      }, '-=0.2');
    });
  };

  const collapseCircles = () => {
    if (!isExpanded) return;

    // Check if circles container exists
    if (!circlesContainerRef.current) {
      setIsExpanded(false);
      setShowChatWindow(false);
      return;
    }

    // Create timeline for smooth collapse
    const tl = gsap.timeline({
      onComplete: () => {
        setIsExpanded(false);
        setShowChatWindow(false);
      }
    });

    // Kill floating animations
    gsap.killTweensOf(circlesContainerRef.current.children);

    // Animate circles out with physics
    tl.to(circlesContainerRef.current.children, {
      scale: 0,
      opacity: 0,
      rotation: 180,
      duration: 0.4,
      stagger: {
        amount: 0.2,
        from: 'end'
      },
      ease: 'power2.in'
    })

    // Animate main button back to normal
    .to(mainButtonRef.current, {
      scale: 1,
      rotation: 0,
      duration: 0.4,
      ease: 'back.out(1.7)'
    }, '-=0.2');
  };

  const openChatWindow = () => {
    setShowChatWindow(true);

    // Wait for next frame to ensure DOM is updated
    requestAnimationFrame(() => {
      if (!chatWindowRef.current) return;

      // Enhanced chat window animation
      const tl = gsap.timeline();

      tl.fromTo(chatWindowRef.current,
        {
          scale: 0.7,
          opacity: 0,
          y: 40,
          rotationX: -15
        },
        {
          scale: 1,
          opacity: 1,
          y: 0,
          rotationX: 0,
          duration: 0.6,
          ease: 'back.out(1.7)'
        }
      );

      // Animate header elements if they exist
      const header = chatWindowRef.current.querySelector('.chat-header');
      if (header) {
        tl.fromTo(header,
          { y: -20, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.3, ease: 'power2.out' },
          '-=0.3'
        );
      }

      // Animate messages container if it exists
      const messages = chatWindowRef.current.querySelector('.chat-messages');
      if (messages) {
        tl.fromTo(messages,
          { y: 20, opacity: 0 },
          { y: 0, opacity: 1, duration: 0.3, ease: 'power2.out' },
          '-=0.2'
        );
      }
    });
  };

  const closeChatWindow = () => {
    if (!chatWindowRef.current) {
      setShowChatWindow(false);
      return;
    }

    const tl = gsap.timeline({
      onComplete: () => setShowChatWindow(false)
    });

    tl.to(chatWindowRef.current, {
      scale: 0.7,
      opacity: 0,
      y: 40,
      rotationX: 15,
      duration: 0.4,
      ease: 'power2.in'
    });
  };

  const minimizeWidget = () => {
    setIsMinimized(!isMinimized);
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      content: inputMessage,
      isBot: false,
      timestamp: new Date().toISOString(),
      role: 'user',
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      console.log('🤖 Sending message:', inputMessage);

      // Use apiService instead of direct fetch to ensure correct URL
      const result = await apiService.chatbot.sendMessage(inputMessage);
      console.log('🤖 Received result:', result);
      console.log('🤖 Result type:', typeof result);
      console.log('🤖 Result keys:', Object.keys(result || {}));

      const data = result;

      if (data && data.success) {
        console.log('✅ Bot response success');
        console.log('🤖 Bot response data:', data.data);
        console.log('🕐 Timestamp from backend:', data.data?.timestamp);
        console.log('💬 Message from backend:', data.data?.message);

        // Fix: data.data is nested, need to access the inner data
        const responseData = data.data?.data || data.data;
        console.log('🔧 Fixed response data:', responseData);
        console.log('🔧 Fixed message:', responseData?.message);
        console.log('🔧 Fixed timestamp:', responseData?.timestamp);

        const botMessage = {
          id: Date.now() + 1,
          content: responseData?.message || 'No message received',
          isBot: true,
          timestamp: responseData?.timestamp || new Date().toISOString(),
          role: 'model',
        };
        setMessages(prev => [...prev, botMessage]);
      } else {
        console.error('❌ Bot response failed:', data);
        console.error('❌ Error message:', data?.error);
        throw new Error(data?.error || 'Something went wrong');
      }
    } catch (error) {
      console.error('Chat error:', error);
      const errorMessage = {
        id: Date.now() + 1,
        content: 'Xin lỗi, đã có lỗi xảy ra. Vui lòng thử lại sau.',
        isBot: true,
        timestamp: new Date().toISOString(),
        role: 'model',
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = e => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatTime = timestamp => {
    try {
      if (!timestamp) return '';

      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        console.warn('Invalid timestamp:', timestamp);
        return new Date().toLocaleTimeString('vi-VN', {
          hour: '2-digit',
          minute: '2-digit',
        });
      }

      return date.toLocaleTimeString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      console.error('Error formatting time:', error);
      return '';
    }
  };

  // Enhanced floating circles with hover effects
  const handleCircleHover = (element, isHovering) => {
    if (isHovering) {
      gsap.to(element, {
        scale: 1.2,
        y: '-=8',
        duration: 0.3,
        ease: 'power2.out'
      });

      // Add glow effect
      gsap.to(element, {
        boxShadow: '0 16px 40px rgba(0, 0, 0, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
        duration: 0.3
      });
    } else {
      gsap.to(element, {
        scale: 1,
        y: '+=8',
        duration: 0.3,
        ease: 'power2.out'
      });

      gsap.to(element, {
        boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
        duration: 0.3
      });
    }
  };

  // Floating circles positioned vertically above main button with ultra-wide spacing
  const floatingCircles = [
    {
      id: 'chat',
      title: t('chatAI') || 'Chat AI',
      icon: ChatBubbleLeftRightIcon,
      color: 'blue',
      action: openChatWindow,
      position: { x: 0, y: -100 } // Closest to main button (just above)
    },
    {
      id: 'create-project',
      title: t('createProject') || 'Tạo Dự Án',
      icon: DocumentTextIcon,
      color: 'green',
      link: '/projects/create',
      position: { x: 0, y: -220 } // Second from main button
    },
    {
      id: 'post-job',
      title: t('postJob') || 'Đăng Việc Làm',
      icon: BriefcaseIcon,
      color: 'purple',
      link: '/jobs/create',
      position: { x: 0, y: -340 } // Middle
    },
    {
      id: 'create-contest',
      title: t('createContest') || 'Tạo Cuộc Thi',
      icon: TrophyIcon,
      color: 'orange',
      link: '/contests/create',
      position: { x: 0, y: -460 } // Second from top
    },
    {
      id: 'get-started',
      title: t('getStarted') || 'Bắt Đầu',
      icon: RocketLaunchIcon,
      color: 'pink',
      link: '/auth?mode=signup',
      position: { x: 0, y: -580 } // Furthest from main button (top)
    }
  ];

  return (
    <div className='floating-widget-container'>
      {/* Main Floating Button */}
      <button
        ref={mainButtonRef}
        className={`main-floating-btn ${isExpanded ? 'expanded' : ''}`}
        onClick={isExpanded ? collapseCircles : expandCircles}
      >
        <div className='btn-content'>
          {isExpanded ? (
            <XMarkIcon className='w-8 h-8' />
          ) : (
            <SparklesIcon className='w-8 h-8' />
          )}
        </div>
        <div className='btn-glow'></div>
      </button>

      {/* Floating Circles */}
      {isExpanded && (
        <div ref={circlesContainerRef} className='floating-circles'>
          {floatingCircles.map((circle) => {
            const IconComponent = circle.icon;
            const CircleComponent = circle.link ? Link : 'button';

            return (
              <CircleComponent
                key={circle.id}
                to={circle.link}
                className={`floating-circle circle-${circle.color}`}
                style={{
                  '--x': `${circle.position.x}px`,
                  '--y': `${circle.position.y}px`,
                  zIndex: 1001 // Ensure visibility
                }}
                onClick={e => {
                  e.stopPropagation();
                  if (circle.action) {
                    circle.action();
                  } else if (circle.link) {
                    collapseCircles();
                  }
                }}
                onMouseEnter={e => handleCircleHover(e.currentTarget, true)}
                onMouseLeave={e => handleCircleHover(e.currentTarget, false)}
                title={circle.title}
              >
                <IconComponent className='w-6 h-6' />
                <span className='circle-tooltip'>{circle.title}</span>
              </CircleComponent>
            );
          })}
        </div>
      )}

      {/* Chat Window */}
      {showChatWindow && (
        <div
          ref={chatWindowRef}
          className={`modern-chat-window ${isMinimized ? 'minimized' : ''}`}
        >
          {/* Header */}
          <div className='chat-header'>
            <div className='header-info'>
              <div className='bot-avatar'>
                <SparklesIcon className='w-5 h-5' />
              </div>
              <div>
                <h4>VWork AI Assistant</h4>
                <span className='status'>● Đang hoạt động</span>
              </div>
            </div>
            <div className='header-actions'>
              <button
                className='minimize-btn'
                onClick={minimizeWidget}
                title={isMinimized ? 'Mở rộng' : 'Thu gọn'}
              >
                {isMinimized ? (
                  <ChevronUpIcon className='w-4 h-4' />
                ) : (
                  <ChevronDownIcon className='w-4 h-4' />
                )}
              </button>
              <button
                className='close-btn'
                onClick={closeChatWindow}
                title='Đóng chat'
              >
                <XMarkIcon className='w-4 h-4' />
              </button>
            </div>
          </div>

          {/* Chat Content */}
          {!isMinimized && (
            <>
              {/* Chat Messages */}
              <div className='chat-messages'>
                {messages.map(message => (
                  <div
                    key={message.id}
                    className={`message ${
                      message.isBot ? 'bot-message' : 'user-message'
                    }`}
                  >
                    <div className='message-content'>
                      <p>{message.content}</p>
                      <span className='message-time'>
                        {formatTime(message.timestamp)}
                      </span>
                    </div>
                  </div>
                ))}
                {isLoading && (
                  <div className='message bot-message'>
                    <div className='message-content'>
                      <div className='typing-indicator'>
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* Chat Input */}
              <div className='chat-input'>
                <div className='input-container'>
                  <textarea
                    ref={inputRef}
                    value={inputMessage}
                    onChange={e => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder='Nhập tin nhắn với AI...'
                    rows='1'
                    disabled={isLoading}
                  />
                  <button
                    onClick={sendMessage}
                    disabled={!inputMessage.trim() || isLoading}
                    className='send-btn'
                  >
                    <PaperAirplaneIcon className='w-5 h-5' />
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default QuickChatWidget;
