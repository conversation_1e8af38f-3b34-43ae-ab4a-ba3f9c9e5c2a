services:
  - type: web
    runtime: node
    name: vwork-auth-service
    region: oregon
    branch: main
    rootDir: services/auth-service
    buildCommand: npm ci
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3001
      - key: FIREBASE_PROJECT_ID
        sync: false
      - key: FIREBASE_SERVICE_ACCOUNT_KEY
        sync: false
      - key: CORS_ORIGINS
        value: https://vwork-client.onrender.com,https://vwork-gateway.onrender.com
    plan: free
