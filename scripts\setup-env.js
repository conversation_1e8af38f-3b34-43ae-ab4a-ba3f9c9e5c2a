#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const ENV_CONTENT = `# VWork Environment Variables

# Gemini AI API Key (Required for chatbot functionality)
GEMINI_API_KEY=AIzaSyArT1pqCqRd1EhBeiGWBT6Wl7WwCYNp5SU

# Firebase Configuration (VWork Project)
REACT_APP_FIREBASE_API_KEY=AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
REACT_APP_FIREBASE_AUTH_DOMAIN=vwork-786c3.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=vwork-786c3
REACT_APP_FIREBASE_STORAGE_BUCKET=vwork-786c3.firebasestorage.app
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=1050922072615
REACT_APP_FIREBASE_APP_ID=1:1050922072615:web:dfeae89c9ba66c77aeec02
FIREBASE_PROJECT_ID=vwork-786c3

# Server Configuration
NODE_ENV=development
PORT=5000

# Application Info
GUILD_NAME=VWork Platform
GUILD_MOTTO=Modern Freelancer Marketplace - Connecting Talent with Opportunities

# CORS Configuration (for development)
CORS_ORIGIN=http://localhost:3000
`;

async function setupEnvironment() {
  const rootDir = path.resolve(__dirname, '..');
  const envPath = path.join(rootDir, '.env');
  
  console.log('🔧 Setting up VWork environment...\n');
  
  try {
    // Check if .env already exists
    if (fs.existsSync(envPath)) {
      console.log('✅ .env file already exists!');
      console.log('📁 Location:', envPath);
      console.log('📋 Skipping .env creation - using existing file');
      console.log('🔒 Existing .env file is protected from overwriting');
      return;
    }
    
    // Create .env file only if it doesn't exist
    fs.writeFileSync(envPath, ENV_CONTENT);
    
    console.log('✅ .env file created successfully!');
    console.log('📁 Location:', envPath);
    console.log('\n🤖 Gemini AI API Key configured for chatbot functionality');
    console.log('🔥 Firebase configuration loaded with VWork project settings');
    console.log('⚙️  Server configuration set for development');
    console.log('\n🚀 You can now run the development server:');
    console.log('   npm start');
    
  } catch (error) {
    console.error('❌ Error creating .env file:', error.message);
    process.exit(1);
  }
}

// Run the setup
setupEnvironment().catch(error => {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
}); 