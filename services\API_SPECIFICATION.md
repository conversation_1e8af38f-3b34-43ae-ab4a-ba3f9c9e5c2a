# VWork Platform API Specification

## Base URL
- Development: `http://localhost:8080/api/v1`
- Production: `https://api.vwork.com/api/v1`

## Authentication
All protected endpoints require Firebase ID token in Authorization header:
```
Authorization: Bearer <firebase_id_token>
```

## API Endpoints

### 1. Authentication Service (Port 3001)

#### POST /auth/register
Register new user with Firebase token
```json
{
  "firebaseToken": "string",
  "userType": "freelancer|client", 
  "name": "string",
  "guildSpecialization": "string"
}
```

#### POST /auth/login
Login with Firebase token
```json
{
  "firebaseToken": "string"
}
```

#### GET /auth/me
Get current user info (protected)

#### POST /auth/logout
Logout user (protected)

### 2. User Service (Port 3002)

#### GET /users/:id
Get user profile by ID

#### PUT /users/:id
Update user profile (protected)
```json
{
  "profile": {
    "bio": "string",
    "avatar": "string",
    "location": {
      "country": "string",
      "city": "string", 
      "timezone": "string"
    },
    "website": "string",
    "phoneNumber": "string",
    "skills": ["string"],
    "hourlyRate": "number",
    "availability": "string",
    "isComplete": "boolean"
  }
}
```

#### GET /freelancers
Get freelancers list with filters
Query params: `skills`, `location`, `hourlyRate`, `availability`, `page`, `limit`

### 3. Project Service (Port 3003)

#### GET /projects
Get projects list with filters
Query params: `category`, `budget`, `skills`, `status`, `page`, `limit`

#### GET /projects/:id
Get project details by ID

#### POST /projects
Create new project (protected)
```json
{
  "title": "string",
  "description": "string", 
  "category": "string",
  "budget": {
    "type": "fixed|hourly",
    "amount": "number",
    "currency": "string"
  },
  "skills": ["string"],
  "deadline": "string",
  "attachments": ["string"]
}
```

#### PUT /projects/:id
Update project (protected)

#### DELETE /projects/:id
Delete project (protected)

#### POST /projects/:id/bids
Submit bid for project (protected)

#### GET /projects/:id/bids
Get project bids (protected)

### 4. Job Service (Port 3004)

#### GET /jobs
Get jobs list with filters
Query params: `category`, `type`, `location`, `salary`, `skills`, `page`, `limit`

#### GET /jobs/:id
Get job details by ID

#### POST /jobs
Create new job (protected)
```json
{
  "title": "string",
  "description": "string",
  "company": "string",
  "location": "string", 
  "type": "full-time|part-time|contract|freelance",
  "category": "string",
  "salary": {
    "min": "number",
    "max": "number", 
    "currency": "string",
    "period": "hour|month|year"
  },
  "skills": ["string"],
  "requirements": ["string"],
  "benefits": ["string"]
}
```

#### PUT /jobs/:id
Update job (protected)

#### DELETE /jobs/:id
Delete job (protected)

#### POST /jobs/:id/apply
Apply for job (protected)
```json
{
  "coverLetter": "string",
  "resume": "string",
  "portfolio": ["string"]
}
```

#### GET /jobs/:id/applications
Get job applications (protected)

### 5. Chat Service (Port 3005)

#### GET /messages/conversations
Get user conversations (protected)

#### GET /messages/:conversationId
Get messages in conversation (protected)

#### POST /messages/:conversationId
Send message (protected)
```json
{
  "content": "string",
  "type": "text|file|image"
}
```

#### POST /chatbot/chat
Send message to chatbot
```json
{
  "message": "string"
}
```

#### GET /chatbot/history
Get chatbot conversation history (protected)

### 6. Community Service (Port 3006)

#### GET /community/posts
Get community posts with filters
Query params: `category`, `tags`, `author`, `page`, `limit`

#### POST /community/posts
Create community post (protected)
```json
{
  "title": "string",
  "content": "string",
  "category": "string",
  "tags": ["string"],
  "attachments": ["string"]
}
```

#### GET /community/posts/:id
Get post details

#### PUT /community/posts/:id
Update post (protected)

#### DELETE /community/posts/:id
Delete post (protected)

#### POST /community/posts/:id/comments
Add comment to post (protected)

#### POST /community/posts/:id/like
Like/unlike post (protected)

### 7. Upload Service (Port 3007)

#### POST /upload
Upload file (protected)
Form data: `file`, `type` (avatar|resume|portfolio|attachment)

### 8. Notification Service (Port 3008)

#### GET /notifications
Get user notifications (protected)

#### PUT /notifications/:id/read
Mark notification as read (protected)

#### PUT /notifications/read-all
Mark all notifications as read (protected)

### 9. Search Service (Port 3009)

#### GET /search
Global search across projects, jobs, freelancers
Query params: `q`, `type`, `filters`, `page`, `limit`

## Response Format

### Success Response
```json
{
  "success": true,
  "data": {},
  "message": "string",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": "string",
  "message": "string",
  "code": "ERROR_CODE"
}
```

## Status Codes
- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 422: Validation Error
- 500: Internal Server Error
