#!/usr/bin/env node

/**
 * VWork Microservice Deployment Script
 * Supports deploying individual services to different environments
 * Usage: node scripts/deploy-microservice.js [service-name] [environment] [options]
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Service configuration
const SERVICES = {
  'api-gateway': {
    path: './services/api-gateway',
    name: 'API Gateway',
    port: 8080,
    healthCheck: '/health',
    envVars: ['PORT', 'NODE_ENV']
  },
  'auth-service': {
    path: './services/auth-service',
    name: 'Authentication Service',
    port: 3001,
    healthCheck: '/health',
    envVars: ['PORT', 'NODE_ENV', 'FIREBASE_PROJECT_ID', 'FIREBASE_PRIVATE_KEY']
  },
  'user-service': {
    path: './services/user-service',
    name: 'User Service',
    port: 3002,
    healthCheck: '/health',
    envVars: ['PORT', 'NODE_ENV']
  },
  'project-service': {
    path: './services/project-service',
    name: 'Project Service',
    port: 3003,
    healthCheck: '/health',
    envVars: ['PORT', 'NODE_ENV']
  },
  'job-service': {
    path: './services/job-service',
    name: 'Job Service',
    port: 3004,
    healthCheck: '/health',
    envVars: ['PORT', 'NODE_ENV']
  },
  'chat-service': {
    path: './services/chat-service',
    name: 'Chat Service',
    port: 3005,
    healthCheck: '/health',
    envVars: ['PORT', 'NODE_ENV']
  },
  'payment-service': {
    path: './services/payment-service',
    name: 'Payment Service',
    port: 3006,
    healthCheck: '/health',
    envVars: ['PORT', 'NODE_ENV']
  }
};

// Environment configurations
const ENVIRONMENTS = {
  'local': {
    name: 'Local Development',
    baseUrl: 'http://localhost',
    buildCommand: 'npm run build',
    startCommand: 'npm start'
  },
  'staging': {
    name: 'Staging',
    baseUrl: 'https://staging.vwork.app',
    buildCommand: 'npm ci --omit=dev',
    startCommand: 'npm start'
  },
  'production': {
    name: 'Production',
    baseUrl: 'https://vwork.app',
    buildCommand: 'npm ci --omit=dev',
    startCommand: 'npm start'
  },
  'render': {
    name: 'Render.com',
    baseUrl: 'https://vwork.onrender.com',
    buildCommand: 'npm ci --omit=dev',
    startCommand: 'npm start'
  }
};

class MicroserviceDeployer {
  constructor() {
    this.args = process.argv.slice(2);
    this.serviceName = this.args[0];
    this.environment = this.args[1] || 'local';
    this.dryRun = this.args.includes('--dry-run');
    this.verbose = this.args.includes('--verbose');
    this.skipBuild = this.args.includes('--skip-build');
    this.skipHealthCheck = this.args.includes('--skip-health-check');
  }

  log(message, color = 'reset') {
    const timestamp = new Date().toISOString().substring(11, 19);
    console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
  }

  error(message) {
    this.log(`❌ ERROR: ${message}`, 'red');
  }

  success(message) {
    this.log(`✅ ${message}`, 'green');
  }

  warn(message) {
    this.log(`⚠️  ${message}`, 'yellow');
  }

  info(message) {
    this.log(`ℹ️  ${message}`, 'blue');
  }

  async execCommand(command, cwd = process.cwd()) {
    if (this.dryRun) {
      this.info(`[DRY RUN] Would execute: ${command} in ${cwd}`);
      return { stdout: '', stderr: '' };
    }

    return new Promise((resolve, reject) => {
      if (this.verbose) {
        this.info(`Executing: ${command} in ${cwd}`);
      }

      const child = spawn(command, { 
        shell: true, 
        cwd,
        stdio: this.verbose ? 'inherit' : 'pipe'
      });

      let stdout = '';
      let stderr = '';

      if (!this.verbose) {
        child.stdout?.on('data', (data) => {
          stdout += data.toString();
        });

        child.stderr?.on('data', (data) => {
          stderr += data.toString();
        });
      }

      child.on('close', (code) => {
        if (code === 0) {
          resolve({ stdout, stderr });
        } else {
          reject(new Error(`Command failed with code ${code}: ${stderr || stdout}`));
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  validateEnvironment() {
    const service = SERVICES[this.serviceName];
    const env = ENVIRONMENTS[this.environment];

    if (!service) {
      throw new Error(`Unknown service: ${this.serviceName}. Available: ${Object.keys(SERVICES).join(', ')}`);
    }

    if (!env) {
      throw new Error(`Unknown environment: ${this.environment}. Available: ${Object.keys(ENVIRONMENTS).join(', ')}`);
    }

    if (!fs.existsSync(service.path)) {
      throw new Error(`Service directory not found: ${service.path}`);
    }

    return { service, env };
  }

  checkEnvironmentVariables(service) {
    this.info('Checking environment variables...');
    
    const missing = [];
    for (const envVar of service.envVars) {
      if (!process.env[envVar]) {
        missing.push(envVar);
      }
    }

    if (missing.length > 0) {
      this.warn(`Missing environment variables: ${missing.join(', ')}`);
      if (this.environment === 'production') {
        throw new Error('Missing required environment variables for production deployment');
      }
    } else {
      this.success('All required environment variables are set');
    }
  }

  async buildService(service, env) {
    if (this.skipBuild) {
      this.info('Skipping build step');
      return;
    }

    this.log(`🔨 Building ${service.name} for ${env.name}...`, 'cyan');
    
    try {
      await this.execCommand(env.buildCommand, service.path);
      this.success(`${service.name} built successfully`);
    } catch (error) {
      throw new Error(`Build failed: ${error.message}`);
    }
  }

  async deployService(service, env) {
    this.log(`🚀 Deploying ${service.name} to ${env.name}...`, 'cyan');

    // Set environment variables
    process.env.NODE_ENV = this.environment === 'local' ? 'development' : 'production';
    process.env.PORT = service.port.toString();

    if (this.environment === 'render') {
      await this.deployToRender(service);
    } else {
      await this.deployLocal(service, env);
    }
  }

  async deployLocal(service, env) {
    this.info(`Starting ${service.name} locally on port ${service.port}...`);
    
    if (this.dryRun) {
      this.info(`[DRY RUN] Would start service with: ${env.startCommand}`);
      return;
    }

    // For local deployment, we just start the service
    try {
      await this.execCommand(env.startCommand, service.path);
    } catch (error) {
      throw new Error(`Failed to start service: ${error.message}`);
    }
  }

  async deployToRender(service) {
    this.info('Deploying to Render.com...');
    
    // Check if render.yaml exists
    const renderConfigPath = path.join(service.path, 'render.yaml');
    if (!fs.existsSync(renderConfigPath)) {
      this.warn(`No render.yaml found for ${service.name}, creating one...`);
      await this.createRenderConfig(service);
    }

    // For Render deployment, we would typically use git push or Render API
    this.info('Please push your changes to git repository for Render deployment');
    this.info(`Service will be available at: ${service.name.toLowerCase().replace(/\s+/g, '-')}.onrender.com`);
  }

  async createRenderConfig(service) {
    const renderConfig = `services:
  - type: web
    name: ${service.name.toLowerCase().replace(/\s+/g, '-')}
    env: node
    plan: free
    buildCommand: npm ci --omit=dev
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: ${service.port}
`;

    const configPath = path.join(service.path, 'render.yaml');
    if (!this.dryRun) {
      fs.writeFileSync(configPath, renderConfig);
    }
    this.success(`Created render.yaml for ${service.name}`);
  }

  async healthCheck(service, env) {
    if (this.skipHealthCheck || this.dryRun) {
      this.info('Skipping health check');
      return;
    }

    this.info('Performing health check...');
    
    const healthUrl = `${env.baseUrl}:${service.port}${service.healthCheck}`;
    
    try {
      // Simple health check - in a real scenario, you'd use axios or fetch
      this.info(`Health check URL: ${healthUrl}`);
      this.success('Health check passed (simulated)');
    } catch (error) {
      this.warn(`Health check failed: ${error.message}`);
    }
  }

  async run() {
    try {
      this.log('🚀 VWork Microservice Deployer', 'bright');
      
      const { service, env } = this.validateEnvironment();
      
      this.log(`Service: ${service.name}`, 'blue');
      this.log(`Environment: ${env.name}`, 'blue');
      this.log(`Dry run: ${this.dryRun}`, 'blue');
      console.log();

      // Check environment variables
      this.checkEnvironmentVariables(service);

      // Build service
      await this.buildService(service, env);

      // Deploy service
      await this.deployService(service, env);

      // Health check
      await this.healthCheck(service, env);

      this.success(`${service.name} deployed successfully to ${env.name}!`);

    } catch (error) {
      this.error(`Deployment failed: ${error.message}`);
      process.exit(1);
    }
  }
}

// Show help if requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
VWork Microservice Deployer

Usage: node scripts/deploy-microservice.js [service] [environment] [options]

Services:
  ${Object.keys(SERVICES).join('\n  ')}

Environments:
  ${Object.keys(ENVIRONMENTS).join('\n  ')}

Options:
  --dry-run           Show what would be done without executing
  --verbose           Show detailed output
  --skip-build        Skip the build step
  --skip-health-check Skip health check after deployment
  --help, -h          Show this help

Examples:
  node scripts/deploy-microservice.js auth-service local
  node scripts/deploy-microservice.js api-gateway production --verbose
  node scripts/deploy-microservice.js user-service render --dry-run
`);
  process.exit(0);
}

// Run the deployer
const deployer = new MicroserviceDeployer();
deployer.run();
