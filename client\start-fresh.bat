@echo off
echo Clearing React development cache...

REM Clear npm cache
echo Clearing npm cache...
npm cache clean --force

REM Clear node_modules cache
echo Clearing node_modules cache...
if exist node_modules\.cache (
    rmdir /s /q node_modules\.cache
    echo Node modules cache cleared
)

REM Clear React scripts cache
echo Clearing React scripts cache...
if exist node_modules\.cache (
    rmdir /s /q node_modules\.cache
)

REM Clear browser cache programmatically
echo Cache clearing completed!

REM Start development server
echo Starting development server with fresh cache...
npm start
