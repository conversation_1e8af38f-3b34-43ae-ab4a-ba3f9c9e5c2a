@echo off
setlocal enabledelayedexpansion

:: VWork Production Build Script for Windows
:: This script builds the entire VWork platform for production deployment

echo 🚀 Building VWork Platform for Production...
echo ==============================================

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ and try again.
    exit /b 1
)

:: Set production environment
set NODE_ENV=production
set GENERATE_SOURCEMAP=false

:: Create build directory
echo 📁 Creating build directory...
if exist ".\build" rmdir /s /q ".\build"
mkdir ".\build"
mkdir ".\build\services"
mkdir ".\build\client"

:: Build shared utilities first
echo 🔧 Building shared utilities...
cd ".\services\shared"
npm ci --production --silent
if %errorlevel% neq 0 (
    echo ❌ Failed to install shared dependencies
    exit /b 1
)
cd ..\..

:: Build services
echo 🏗️ Building services...

set services=api-gateway auth-service user-service project-service job-service chat-service search-service

for %%s in (%services%) do (
    echo 🔨 Building %%s...
    if exist ".\services\%%s" (
        cd ".\services\%%s"
        npm ci --production --silent
        if %errorlevel% neq 0 (
            echo ❌ Failed to build %%s
            cd ..\..
            exit /b 1
        )
        
        :: Run build script if exists
        npm run build 2>nul
        
        cd ..\..
        echo ✅ %%s build completed
    ) else (
        echo ⚠️ %%s directory not found
    )
)

:: Build frontend
echo 🎨 Building React frontend...
cd ".\client"

:: Remove debug/test files for production
echo 🧹 Removing debug/test files...
for /r "src" %%f in (*Debug*.js *Test*.js *.test.js *.spec.js) do (
    if exist "%%f" del "%%f" 2>nul
)

:: Install dependencies and build
npm ci --production --silent
if %errorlevel% neq 0 (
    echo ❌ Failed to install frontend dependencies
    exit /b 1
)

npm run build
if %errorlevel% neq 0 (
    echo ❌ Failed to build frontend
    exit /b 1
)

:: Copy build to main build directory
xcopy /E /I /Y "build\*" "..\build\client\"
cd ..

echo ✅ Frontend build completed

:: Copy service files to build directory
echo 📋 Copying service files to build directory...
for %%s in (%services%) do (
    if exist ".\services\%%s" (
        mkdir ".\build\services\%%s" 2>nul
        
        :: Copy necessary files
        if exist ".\services\%%s\src" xcopy /E /I /Y ".\services\%%s\src" ".\build\services\%%s\src\"
        if exist ".\services\%%s\package.json" copy ".\services\%%s\package.json" ".\build\services\%%s\"
        if exist ".\services\%%s\node_modules" xcopy /E /I /Y ".\services\%%s\node_modules" ".\build\services\%%s\node_modules\"
    )
)

:: Copy shared utilities
xcopy /E /I /Y ".\services\shared" ".\build\services\shared\"

:: Copy configuration files
echo ⚙️ Copying configuration files...
if exist "package.json" copy "package.json" ".\build\"
if exist "README.md" copy "README.md" ".\build\"
if exist "scripts" xcopy /E /I /Y "scripts" ".\build\scripts\"

:: Create production start script
echo 🚀 Creating production start script...
(
echo @echo off
echo echo 🚀 Starting VWork Platform ^(Production Mode^)
echo set NODE_ENV=production
echo node scripts\unified-start.js
) > ".\build\start-production.bat"

:: Create Docker support files
echo 🐳 Creating Docker support files...

:: Main Dockerfile
(
echo FROM node:18-alpine
echo.
echo WORKDIR /app
echo.
echo # Copy package files
echo COPY package*.json ./
echo COPY services/shared/package*.json ./services/shared/
echo.
echo # Install dependencies
echo RUN npm ci --production --silent
echo.
echo # Copy application files
echo COPY . .
echo.
echo # Create non-root user
echo RUN addgroup -g 1001 -S nodejs
echo RUN adduser -S vwork -u 1001
echo.
echo # Change ownership
echo RUN chown -R vwork:nodejs /app
echo USER vwork
echo.
echo # Expose ports
echo EXPOSE 8080 3001 3002 3003 3004 3005 3009
echo.
echo # Health check
echo HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
echo   CMD curl -f http://localhost:8080/health ^|^| exit 1
echo.
echo # Start the application
echo CMD ["node", "scripts/unified-start.js"]
) > ".\build\Dockerfile"

:: Generate build info
echo 📊 Generating build information...
for /f "tokens=*" %%i in ('node -v') do set NODE_VERSION=%%i
for /f "tokens=2 delims= " %%i in ('date /t') do set BUILD_DATE=%%i
for /f "tokens=*" %%i in ('time /t') do set BUILD_TIME=%%i

(
echo {
echo   "buildDate": "%BUILD_DATE% %BUILD_TIME%",
echo   "nodeVersion": "%NODE_VERSION%",
echo   "platform": "Windows",
echo   "arch": "%PROCESSOR_ARCHITECTURE%",
echo   "gitCommit": "unknown",
echo   "gitBranch": "unknown",
echo   "version": "1.0.0"
echo }
) > ".\build\build-info.json"

echo.
echo ✅ Production build completed successfully!
echo ==============================================
echo 📁 Build location: .\build
echo 📋 Build info: .\build\build-info.json
echo.
echo 🚀 To start the production server:
echo    cd build ^&^& start-production.bat
echo.
echo 🐳 To run with Docker:
echo    cd build ^&^& docker-compose -f docker-compose.prod.yml up -d
echo.
echo 📋 Next steps:
echo    1. Copy the .\build directory to your production server
echo    2. Set up your environment variables
echo    3. Configure SSL certificates
echo    4. Run the production server

pause
