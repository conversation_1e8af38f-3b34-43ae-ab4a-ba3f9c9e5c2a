import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, AppleFooter } from '../apple';
import QuickChatWidget from '../common/QuickChatWidget';

const Layout = ({ children }) => {
  console.log('🏗️ Layout component rendered!');
  
  return (
    <div className='min-h-screen flex flex-col transition-colors duration-300 bg-white dark:bg-gray-900'>
      <AppleHeader />
      <main className='flex-1 pt-16 lg:pt-20'>{children}</main>
      <AppleFooter />
      <QuickChatWidget />
    </div>
  );
};

export default Layout;
