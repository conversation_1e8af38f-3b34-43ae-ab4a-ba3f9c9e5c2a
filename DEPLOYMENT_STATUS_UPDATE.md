# 🔧 Cập nhật Frontend Environment Variables

Sau khi API Gateway deploy xong, bạn cần cập nhật Frontend:

## Vào Frontend Settings trên Render:

1. **Truy cập**: https://dashboard.render.com
2. **Chọn service**: `Frontend` 
3. **Vào tab**: Environment
4. **Cập nhật variable**: `REACT_APP_API_URL`

## Thay đổi từ:
```
REACT_APP_API_URL=https://vwork-api-gateway.onrender.com
```

## Thành URL thực tế của API Gateway:
```
REACT_APP_API_URL=https://vwork-api-gateway-[random-id].onrender.com
```

## Sau đó:
1. **Save Changes**
2. **Trigger Deploy** (Manual Deploy)

---

## 🔍 Debug Services bị Cancel:

### Payment Service:
1. Vào **vwork-payment-service** > **Logs**
2. Tìm lỗi trong build/start logs
3. Fix lỗi và redeploy

### User Service:
1. Vào **vwork-user-service** > **Logs**  
2. Tìm lỗi trong build/start logs
3. Fix lỗi và redeploy

## 📝 Environment Variables cần thiết cho từng service:

### API Gateway:
```
NODE_ENV=production
PORT=10000
CORS_ORIGIN=https://[frontend-url].onrender.com
AUTH_SERVICE_URL=https://vwork-auth-service-[id].onrender.com
USER_SERVICE_URL=https://vwork-user-service-[id].onrender.com
PROJECT_SERVICE_URL=https://vwork-project-service-[id].onrender.com
CHAT_SERVICE_URL=https://vwork-chat-service-[id].onrender.com
PAYMENT_SERVICE_URL=https://vwork-payment-service-[id].onrender.com
JWT_SECRET=vwork-super-secret-jwt-key-2024
FIREBASE_PROJECT_ID=vwork-786c3
```

### Tất cả Backend Services:
```
NODE_ENV=production
PORT=10000
FIREBASE_PROJECT_ID=vwork-786c3
JWT_SECRET=vwork-super-secret-jwt-key-2024
```
