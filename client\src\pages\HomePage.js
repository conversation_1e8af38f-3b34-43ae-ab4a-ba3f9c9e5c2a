import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import {
  // Sections
  AppleHeroSection,
  AppleFeaturesSection,
  AppleStatsSection,
  AppleHowItWorks,
  AppleCTASection,
  // Showcase
  AppleFeaturedJobsCarousel,
  AppleFreelancersShowcase,
  AppleTestimonialsSlider,
  AppleClientLogos,
  AppleProjectGallery,
  // Interactive
  AppleCategoriesGrid,
  AppleInteractiveSearch,
  // Cards
  AppleRecentActivity,
} from '../components/apple';

const HomePage = () => {
  const { t } = useLanguage();

  // Apple-style homepage with comprehensive sections
  return (
    <div className='min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300'>
      {/* Hero Section - Main landing area */}
      <AppleHeroSection />

      {/* Interactive Search - Help users find what they need */}
      <AppleInteractiveSearch />

      {/* Features Section - Core platform benefits */}
      <AppleFeaturesSection />

      {/* Statistics - Build trust with numbers */}
      <AppleStatsSection />

      {/* Categories Grid - Browse by category */}
      <AppleCategoriesGrid />

      {/* Featured Jobs - Highlight opportunities */}
      <AppleFeaturedJobsCarousel />

      {/* Top Freelancers - Showcase talent */}
      <AppleFreelancersShowcase />

      {/* Project Gallery - Featured work */}
      <AppleProjectGallery />

      {/* How It Works - Explain the process */}
      <AppleHowItWorks />

      {/* Recent Activity - Live feed */}
      <AppleRecentActivity />

      {/* Client Logos - Trust indicators */}
      <AppleClientLogos />

      {/* Testimonials - Social proof */}
      <AppleTestimonialsSlider />

      {/* Call to Action - Final conversion */}
      <AppleCTASection />
    </div>
  );
};

export default HomePage;
