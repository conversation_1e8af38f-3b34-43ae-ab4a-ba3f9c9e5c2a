# VWork - Modern Freelancer Marketplace

## 🚀 Deploy on Render.com

### Build Settings for Render:
- **Build Command:** `npm install && npm run build`
- **Publish Directory:** `build`
- **Node Version:** `18.x`

### Environment Variables:
```
NODE_ENV=production
GENERATE_SOURCEMAP=false
REACT_APP_FIREBASE_API_KEY=your_api_key_here
REACT_APP_FIREBASE_AUTH_DOMAIN=your_auth_domain_here
REACT_APP_FIREBASE_PROJECT_ID=your_project_id_here
REACT_APP_FIREBASE_STORAGE_BUCKET=your_storage_bucket_here
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id_here
REACT_APP_FIREBASE_APP_ID=your_app_id_here
```

## 🔥 Firebase Authentication

Ứng dụng sử dụng Firebase Authentication với các tính năng:

### User Types
- **Client**: <PERSON><PERSON><PERSON><PERSON> tuyể<PERSON> dụng, đăng dự án
- **Freelancer**: <PERSON><PERSON><PERSON><PERSON> làm việc tự do, tìm dự án

### Auth Features
- ✅ Email/Password Registration & Login
- ✅ Google OAuth Authentication
- ✅ Password Reset via Email
- ✅ Protected Routes
- ✅ User Profile Management
- ✅ Role-based Access (Client/Freelancer)

### 🔧 Quick Setup

1. **Create Environment File** (ở root project):
```bash
# Tạo file .env trong thư mục gốc (Vwork/)
# VD: D:\Project\Vwork\.env

# Firebase Configuration (VWork Project)
REACT_APP_FIREBASE_API_KEY=AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
REACT_APP_FIREBASE_AUTH_DOMAIN=vwork-786c3.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=vwork-786c3
REACT_APP_FIREBASE_STORAGE_BUCKET=vwork-786c3.firebasestorage.app
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=1050922072615
REACT_APP_FIREBASE_APP_ID=1:1050922072615:web:dfeae89c9ba66c77aeec02

# Environment
NODE_ENV=development
```

2. **Start Development Server**:
```bash
cd client
npm install
npm start
```

## 📱 Features
- ✅ Responsive Design
- ✅ Dark/Light Mode
- ✅ Multi-language (Vietnamese/English)
- ✅ Modern UI with Tailwind CSS
- ✅ GSAP Animations
- ✅ Firebase Authentication System
- ✅ User Type Selection (Client/Freelancer)
- ✅ Form Validation
- ✅ Loading States & Error Handling

## 🛠 Development

```bash
# Install dependencies
npm install

# Start development server
npm start

# Build for production
npm run build

# Preview production build
npm run preview
```

## 📦 Tech Stack
- React 18
- Firebase (Auth + Firestore)
- Tailwind CSS
- GSAP
- React Router
- Heroicons
- React Hook Form
- React Hot Toast 