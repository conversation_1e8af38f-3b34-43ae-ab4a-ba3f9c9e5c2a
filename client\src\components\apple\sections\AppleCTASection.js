import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { animationController } from '../../../utils/animations';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useAuth } from '../../../contexts/AuthContext';
import {
  ArrowRightIcon,
  SparklesIcon,
  RocketLaunchIcon,
  UserPlusIcon,
  BriefcaseIcon,
} from '@heroicons/react/24/outline';

const AppleCTASection = () => {
  const { t } = useLanguage();
  const { user, isAuthenticated } = useAuth();
  const sectionRef = useRef(null);
  const contentRef = useRef(null);
  const buttonsRef = useRef(null);
  const particlesRef = useRef(null);
  const blur1Ref = useRef(null);
  const blur2Ref = useRef(null);
  const blur3Ref = useRef(null);

  useEffect(() => {
    let eventListeners = [];
    const ctx = gsap.context(() => {
      // Content animation
      animationController.fadeIn(contentRef.current, {
        scrollTrigger: {
          trigger: contentRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      });

      // Buttons animation
      if (buttonsRef.current) {
        animationController.scaleIn(buttonsRef.current.children, {
          delay: 0.3,
          stagger: 0.1,
          scrollTrigger: {
            trigger: buttonsRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse',
          },
        });
      }

      // Floating particles animation
      if (particlesRef.current && particlesRef.current.children) {
        const particles = Array.from(particlesRef.current.children);
        
        particles.forEach((particle, index) => {
          gsap.to(particle, {
            y: `+=${20 + index * 5}`,
            x: `+=${10 + index * 3}`,
            rotation: index % 2 === 0 ? 360 : -360,
            duration: 8 + index * 2,
            repeat: -1,
            yoyo: true,
            ease: 'sine.inOut',
            delay: index * 0.5,
          });
        });
      }

      // Background blur animations
      if (blur1Ref.current) {
        gsap.to(blur1Ref.current, {
          x: 50,
          y: 30,
          scale: 1.2,
          duration: 10,
          repeat: -1,
          yoyo: true,
          ease: 'sine.inOut',
        });
      }

      if (blur2Ref.current) {
        gsap.to(blur2Ref.current, {
          x: -30,
          y: 50,
          scale: 0.8,
          duration: 12,
          repeat: -1,
          yoyo: true,
          ease: 'sine.inOut',
          delay: 2,
        });
      }

      if (blur3Ref.current) {
        gsap.to(blur3Ref.current, {
          x: 20,
          y: -40,
          scale: 1.1,
          duration: 14,
          repeat: -1,
          yoyo: true,
          ease: 'sine.inOut',
          delay: 4,
        });
      }
    }, sectionRef);

    return () => {
      ctx.revert();
      eventListeners.forEach(({ element, event, handler }) => {
        element?.removeEventListener(event, handler);
      });
    };
  }, []);

  return (
    <section
      ref={sectionRef}
      className='py-20 bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700 relative overflow-hidden'
    >
      {/* Background Pattern */}
      <div className='absolute inset-0 opacity-10'>
        <div ref={blur1Ref} className='absolute top-0 left-1/4 w-96 h-96 bg-white rounded-full blur-3xl' />
        <div ref={blur2Ref} className='absolute bottom-0 right-1/4 w-80 h-80 bg-white rounded-full blur-3xl' />
        <div ref={blur3Ref} className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white rounded-full blur-3xl' />
      </div>

      {/* Floating Particles */}
      <div ref={particlesRef} className='absolute inset-0 pointer-events-none'>
        <div className='absolute top-20 left-10 w-4 h-4 bg-white/20 rounded-full' />
        <div className='absolute top-40 right-20 w-6 h-6 bg-white/15 rounded-full' />
        <div className='absolute bottom-40 left-20 w-3 h-3 bg-white/25 rounded-full' />
        <div className='absolute top-60 left-1/3 w-5 h-5 bg-white/10 rounded-full' />
        <div className='absolute bottom-60 right-1/3 w-4 h-4 bg-white/20 rounded-full' />
        <div className='absolute top-80 right-10 w-2 h-2 bg-white/30 rounded-full' />
      </div>

      <div className='relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
        <div ref={contentRef} className='text-center'>
          {/* Main Heading */}
          <div className='mb-8'>
            <h2 className='text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6'>
              {t('readyToGetStarted2')}
              <span className='block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent'>
                {t('started')}
              </span>
            </h2>
            <p className='text-xl sm:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed'>
              {t('joinMillionsFreelancers')}
            </p>
          </div>

          {/* CTA Buttons */}
          <div ref={buttonsRef} className='flex flex-col sm:flex-row gap-6 justify-center items-center mb-12'>
            <a
              href='/auth?mode=signup'
              className='group inline-flex items-center px-8 py-4 text-lg font-semibold text-blue-700 bg-white hover:bg-gray-50 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1'
            >
              <UserPlusIcon className='mr-3 h-6 w-6' />
              <span>{t('joinAsFreelancer')}</span>
              <ArrowRightIcon className='ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200' />
            </a>

            <a
              href='/auth?mode=signup'
              className='group inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-blue-800 hover:bg-blue-900 rounded-xl border-2 border-blue-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1'
            >
              <BriefcaseIcon className='mr-3 h-6 w-6' />
              <span>{t('hireFreelancers')}</span>
              <ArrowRightIcon className='ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200' />
            </a>
          </div>

          {/* Features Grid */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto'>
            <div className='text-center'>
              <div className='inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-2xl mb-4'>
                <SparklesIcon className='h-8 w-8 text-white' />
              </div>
              <h3 className='text-xl font-semibold text-white mb-2'>{t('instantMatching')}</h3>
              <p className='text-blue-100'>{t('instantMatchingDesc')}</p>
            </div>

            <div className='text-center'>
              <div className='inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-2xl mb-4'>
                <RocketLaunchIcon className='h-8 w-8 text-white' />
              </div>
              <h3 className='text-xl font-semibold text-white mb-2'>{t('fastPayments')}</h3>
              <p className='text-blue-100'>{t('fastPaymentsDesc')}</p>
            </div>

            <div className='text-center'>
              <div className='inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-2xl mb-4'>
                <BriefcaseIcon className='h-8 w-8 text-white' />
              </div>
              <h3 className='text-xl font-semibold text-white mb-2'>{t('qualityWork')}</h3>
              <p className='text-blue-100'>{t('qualityWorkDesc')}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AppleCTASection;
