#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚀 VWork Render Deployment Checker');
console.log('=====================================\n');

// Kiểm tra cấu trúc thư mục
const requiredServices = [
  'api-gateway',
  'auth-service', 
  'user-service',
  'project-service',
  'chat-service',
  'payment-service'
];

const servicesDir = path.join(__dirname, 'services');
const clientDir = path.join(__dirname, 'client');

console.log('📁 Kiểm tra cấu trúc thư mục...');

// Kiểm tra thư mục services
if (!fs.existsSync(servicesDir)) {
  console.error('❌ Thư mục services không tồn tại!');
  process.exit(1);
}

// Kiểm tra từng service
let allServicesExist = true;
for (const service of requiredServices) {
  const servicePath = path.join(servicesDir, service);
  if (!fs.existsSync(servicePath)) {
    console.error(`❌ Service ${service} không tồn tại tại ${servicePath}`);
    allServicesExist = false;
  } else {
    console.log(`✅ ${service} - OK`);
  }
}

if (!allServicesExist) {
  console.error('\n❌ Một số services bị thiếu. Vui lòng tạo đầy đủ các services trước khi deploy.');
  process.exit(1);
}

// Kiểm tra thư mục client
if (!fs.existsSync(clientDir)) {
  console.error('❌ Thư mục client không tồn tại!');
  process.exit(1);
}

console.log('✅ Thư mục client - OK');

// Kiểm tra file render.yaml
const renderYamlPath = path.join(__dirname, 'render.yaml');
if (!fs.existsSync(renderYamlPath)) {
  console.error('❌ File render.yaml không tồn tại!');
  process.exit(1);
}

console.log('✅ File render.yaml - OK');

// Kiểm tra package.json của từng service
console.log('\n📦 Kiểm tra package.json của các services...');

for (const service of requiredServices) {
  const packageJsonPath = path.join(servicesDir, service, 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    console.error(`❌ package.json không tồn tại trong ${service}`);
    allServicesExist = false;
  } else {
    try {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      if (!packageJson.scripts || !packageJson.scripts.start) {
        console.error(`❌ ${service} thiếu script start`);
        allServicesExist = false;
      } else {
        console.log(`✅ ${service} package.json - OK`);
      }
    } catch (error) {
      console.error(`❌ Lỗi đọc package.json của ${service}:`, error.message);
      allServicesExist = false;
    }
  }
}

// Kiểm tra file index.js của từng service
console.log('\n🔧 Kiểm tra file index.js của các services...');

for (const service of requiredServices) {
  const indexJsPath = path.join(servicesDir, service, 'src', 'index.js');
  if (!fs.existsSync(indexJsPath)) {
    console.error(`❌ src/index.js không tồn tại trong ${service}`);
    allServicesExist = false;
  } else {
    const content = fs.readFileSync(indexJsPath, 'utf8');
    if (!content.includes('/health')) {
      console.error(`❌ ${service} thiếu endpoint /health`);
      allServicesExist = false;
    } else {
      console.log(`✅ ${service} index.js - OK`);
    }
  }
}

// Kiểm tra package.json của client
const clientPackageJsonPath = path.join(clientDir, 'package.json');
if (!fs.existsSync(clientPackageJsonPath)) {
  console.error('❌ package.json không tồn tại trong client');
  process.exit(1);
}

try {
  const clientPackageJson = JSON.parse(fs.readFileSync(clientPackageJsonPath, 'utf8'));
  if (!clientPackageJson.scripts || !clientPackageJson.scripts.build) {
    console.error('❌ Client thiếu script build');
    process.exit(1);
  } else {
    console.log('✅ Client package.json - OK');
  }
} catch (error) {
  console.error('❌ Lỗi đọc package.json của client:', error.message);
  process.exit(1);
}

console.log('\n🎉 Tất cả kiểm tra đã hoàn thành!');
console.log('\n📋 Hướng dẫn deploy:');
console.log('1. Push code lên GitHub repository');
console.log('2. Đăng nhập vào Render Dashboard: https://dashboard.render.com');
console.log('3. Click "New" → "Blueprint"');
console.log('4. Connect GitHub repository');
console.log('5. Render sẽ tự động detect file render.yaml và tạo tất cả services');
console.log('6. Cấu hình environment variables trong Render Dashboard');
console.log('7. Deploy và test các endpoints');

console.log('\n🔗 URLs sau khi deploy:');
console.log('- API Gateway: https://vwork-api-gateway.onrender.com');
console.log('- Auth Service: https://vwork-auth-service.onrender.com');
console.log('- User Service: https://vwork-user-service.onrender.com');
console.log('- Project Service: https://vwork-project-service.onrender.com');
console.log('- Chat Service: https://vwork-chat-service.onrender.com');
console.log('- Payment Service: https://vwork-payment-service.onrender.com');
console.log('- Frontend: https://vwork-frontend.onrender.com');

console.log('\n⚠️  Lưu ý quan trọng:');
console.log('- Đảm bảo tất cả environment variables được cấu hình đúng');
console.log('- Test health check endpoints: /health');
console.log('- Monitor logs trong Render Dashboard');
console.log('- Backup database và configuration');

if (!allServicesExist) {
  console.error('\n❌ Có lỗi trong cấu hình. Vui lòng sửa trước khi deploy.');
  process.exit(1);
} else {
  console.log('\n✅ Sẵn sàng deploy lên Render!');
} 