import { createContext, useContext, useEffect, useState, useMemo, useCallback } from 'react';
import PropTypes from 'prop-types';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Translations
const translations = {
  en: {
    // Navigation
    home: 'Home',
    projects: 'Projects',
    freelancers: 'Find Freelancers',
    jobs: 'Jobs',
    contests: 'Contests',
    dashboard: 'Dashboard',
    messages: 'Messages',
    support: 'Support',
    community: 'Community',
    settings: 'Settings',
    signIn: 'Sign In',
    signUp: 'Sign Up',
    signOut: 'Sign Out',
    vwork: 'VWork',
    vworkAssistant: 'VWork Assistant',

    // Homepage
    heroTitle: 'Find the Perfect Freelancer for Your Project',
    heroSubtitle:
      'Connect with top-rated professionals and get your work done with confidence',
    searchPlaceholder: 'What service are you looking for?',
    getStarted: 'Get Started',
    learnMore: 'Learn More',
    add: 'Add',
    createProject: 'Create Project',
    postJob: 'Post Job',
    createContest: 'Create Contest',
    chatAI: 'Chat AI',

    // Categories
    webDevelopment: 'Web Development',
    mobileApps: 'Mobile Apps',
    design: 'Design & Creative',
    writing: 'Writing & Translation',
    marketing: 'Digital Marketing',
    business: 'Business & Consulting',

    // Features
    securePayments: 'Secure Payments',
    qualityWork: 'Quality Guaranteed',
    support24_7: '24/7 Support',
    easyToUse: 'Easy to Use',

    // Projects
    postProject: 'Post a Project',
    viewDetails: 'View Details',
    placeBid: 'Place Bid',
    budget: 'Budget',
    deadline: 'Deadline',
    proposals: 'Proposals',
    viewAllJobs: 'View All Jobs',

    // Common
    loading: 'Loading...',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    submit: 'Submit',
    next: 'Next',
    previous: 'Previous',
    more: 'More',
    user: 'User',
    searchProjectsFreelancers: 'Search projects, freelancers...',

    // Auth
    welcomeBack: 'Welcome Back',
    createYourAccount: 'Create Your Account',
    signInToAccount: 'Sign in to your account',
    joinWorldsLargest: 'Join the world\'s largest freelance marketplace',
    iWantTo: 'I want to...',
    findWork: 'Find Work',
    findTalent: 'Find Talent',
    hireTalent: 'Hire Talent',
    firstName: 'First Name',
    lastName: 'Last Name',
    emailAddress: 'Email Address',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    createAccount: 'Create Account',
    dontHaveAccount: "Don't have an account?",
    alreadyHaveAccount: 'Already have an account?',
    forgotPassword: 'Forgot Password?',
    emailRequired: 'Email is required',
    emailInvalid: 'Please enter a valid email',
    passwordRequired: 'Password is required',
    passwordMinLength: 'Password must be at least 6 characters',
    firstNameRequired: 'First name is required',
    lastNameRequired: 'Last name is required',
    passwordsNotMatch: 'Passwords do not match',
    
    // Additional missing keys
    watchDemo: 'Watch Demo',
    showPassword: 'Show Password',
    hidePassword: 'Hide Password',
    toggleDarkMode: 'Toggle Dark Mode',
    loadingDashboard: 'Loading Dashboard...',
    gatheringData: 'Gathering Data...',
    webDevelopmentCategory: 'Web Development',
    mobileDevelopmentCategory: 'Mobile Development',
    designCategory: 'Design',
    writingCategory: 'Writing',
    marketingCategory: 'Marketing',
    dataScience: 'Data Science',
    budgetRange: 'Budget Range',
    under500: 'Under $500',
    budget500to1000: '$500 - $1,000',
    budget1000to5000: '$1,000 - $5,000',
    over5000: 'Over $5,000',
    projectDuration: 'Project Duration',
    lessThanWeek: 'Less than a week',
    oneToFourWeeks: '1-4 weeks',
    oneToThreeMonths: '1-3 months',
    moreThanThreeMonths: 'More than 3 months',
    category: 'Category',
    allContests: 'All Contests',
    readyToShowcaseTalent: 'Ready to showcase your talent?',
    joinThousandsDesigners: 'Join thousands of designers and developers',
    browseActiveContests: 'Browse Active Contests',
    startYourOwnContest: 'Start Your Own Contest',
    
    // Additional missing keys for pages
    findTalentedFreelancers: 'Find Talented Freelancers',
    connectWithProfessionals: 'Connect with Professionals',
    searchFreelancersPlaceholder: 'Search for freelancers...',
    filters: 'Filters',
    freelancersFound: 'freelancers found',
    highestRated: 'Highest Rated',
    projectsCount: 'Projects',
    loadMoreFreelancers: 'Load More Freelancers',
    allCategories: 'All Categories',
    enterLocation: 'Enter location',
    allLevels: 'All Levels',
    clearFilters: 'Clear Filters',
    designContests: 'Design Contests',
    competeWinPrizes: 'Compete & Win Prizes',
    activeContests: 'Active Contests',
    totalPrizes: 'Total Prizes',
    participants: 'Participants',
    winners: 'Winners',
    communityHubTitle: 'Community Hub',
    connectLearnGrow: 'Connect, Learn & Grow',
    membersCount: 'Members',
    postsCount: 'Posts',
    commentsCount: 'Comments',
    onlineNow: 'Online Now',
    newPost: 'New Post',
    sortBy: 'Sort by',
    mostRecent: 'Most Recent',
    allPosts: 'All Posts',
    trending: 'Trending',
    timeAgo: 'time ago',
    hoursAgo: 'hours ago',
    daysAgo: 'days ago',
    loadMorePosts: 'Load More Posts',


    // Homepage sections
    worldsWork: 'World\'s Work',
    marketplace: 'Marketplace',
    findYourDreamJob: 'Find Your Dream Job',
    hireTopTalent: 'Hire Top Talent', 
    buildYourCareer: 'Build Your Career',
    growYourBusiness: 'Grow Your Business',
    perfectMatches: 'Perfect Matches',
    creativeSolutions: 'Creative Solutions',
    cursor: '|',
    connectSubtitle: 'Connect everyone',
    findJobs: 'Find Jobs',
    jobsDescription: 'Discover thousands of job opportunities from top companies',
    
    // Search section
    findPerfectMatch: 'Find Perfect Match',
    searchThousandsOfTalented: 'Search thousands of talented professionals',
    searchSkillsOrJobs: 'Search skills or jobs',
    location: 'Location',
    hourlyRate: 'Hourly Rate',
    experienceLevel: 'Experience Level',
    availability: 'Availability',
    moreFilters: 'More Filters',
    searchNow: 'Search Now',
    
    // Popular searches
    popularSearches: 'Popular Searches',
    reactDeveloper: 'React Developer',
    uiUxDesigner: 'UI/UX Designer',
    contentWriter: 'Content Writer',
    digitalMarketing: 'Digital Marketing',
    mobileAppDeveloper: 'Mobile App Developer',
    dataScientist: 'Data Scientist',
    
    // Popular locations
    popularLocations: 'Popular Locations',
    
    // Why choose section
    whyChooseVWork: 'Why Choose VWork',
    trustedPlatformDesc: 'Trusted platform for quality work',
    secureAndTrusted: 'Secure and Trusted',
    secureDesc: 'Your payments and data are secure',
    fairPricing: 'Fair Pricing',
    fairPricingDesc: 'Transparent pricing with no hidden fees',
    fastDelivery: 'Fast Delivery',
    fastDeliveryDesc: 'Get your projects done quickly',
    expertNetwork: 'Expert Network',
    expertNetworkDesc: 'Access to verified professionals',
    qualityGuaranteed: 'Quality Guaranteed',
    qualityGuaranteedDesc: 'We ensure high-quality deliverables',
    globalReach: 'Global Reach',
    globalReachDesc: 'Work with talent from around the world',
    
    // Numbers section
    numbersSpeak: 'Numbers Speak',
    trustedByThousands: 'Trusted by thousands of users',
    activeFreelancers: 'Active Freelancers',
    skilledProfessionals: 'Skilled Professionals',
    successfullyDelivered: 'Successfully Delivered',
    totalEarnings: 'Total Earnings',
    paidToFreelancers: 'Paid to Freelancers',
    clientSatisfaction: 'Client Satisfaction',
    projectSuccess: 'Project Success',
    countries: 'Countries',
    
    // Browse categories
    browseByCategory: 'Browse by Category',
    findPerfectFreelancer: 'Find the perfect freelancer',
    developmentIT: 'Development & IT',
    developmentITDesc: 'Software development and IT services',
    designCreative: 'Design & Creative',
    designCreativeDesc: 'Graphic design and creative services',
    salesMarketing: 'Sales & Marketing',
    salesMarketingDesc: 'Marketing and sales expertise',
    writingTranslation: 'Writing & Translation',
    writingTranslationDesc: 'Content writing and translation',
    videoPhotography: 'Video & Photography',
    videoPhotographyDesc: 'Video production and photography',
    dataAnalytics: 'Data & Analytics',
    dataAnalyticsDesc: 'Data analysis and business intelligence',
    musicAudio: 'Music & Audio',
    musicAudioDesc: 'Audio production and music services',
    customerService: 'Customer Service',
    customerServiceDesc: 'Customer support and service',
    viewAllCategories: 'View All Categories',
    
    // Featured jobs
    featuredJobs: 'Featured Jobs',
    discoverTopOpportunities: 'Discover top opportunities',
    urgent: 'Urgent',
    applyNow: 'Apply Now',
    
    // Top freelancers
    topFreelancers: 'Top Freelancers',
    workWithBestTalent: 'Work with the best talent',
    topRated: 'Top Rated',
    available: 'Available',
    response: 'Response',
    hire: 'Hire',
    message: 'Message',
    viewAllFreelancers: 'View All Freelancers',
    
    // Featured projects
    featuredProjects: 'Featured Projects',
    projectsText: 'Projects',
    exploreOutstandingWork: 'Explore outstanding work',
    allProjects: 'All Projects',
    webDesign: 'Web Design',
    branding: 'Branding',
    illustration: 'Illustration',
    byFreelancer: 'by freelancer',
    viewAllProjects: 'View All Projects',
    
    // How it works
    howItWorks: 'How It Works',
    simpleStepsToSuccess: 'Simple steps to success',
    postYourProject: 'Post Your Project',
    postProjectDesc: 'Describe your project requirements',
    describeProjectReq: 'Describe project requirements',
    setBudgetTimeline: 'Set budget and timeline',
    choosePreferredSkills: 'Choose preferred skills',
    reviewProposals: 'Review Proposals',
    reviewProposalsDesc: 'Review and select the best proposals',
    receiveProposalsFromQualified: 'Receive proposals from qualified freelancers',
    reviewPortfoliosPastWork: 'Review portfolios and past work',
    interviewTopCandidates: 'Interview top candidates',
    workPaySafely: 'Work & Pay Safely',
    workPaySafelyDesc: 'Secure payment and project management',
    useEscrowProtection: 'Use escrow protection',
    trackProgressMilestones: 'Track progress with milestones',
    payOnlyWhenSatisfied: 'Pay only when satisfied',
    leaveReview: 'Leave Review',
    leaveReviewDesc: 'Rate and review completed work',
    rateFreelancerWork: 'Rate freelancer work',
    provideFeedback: 'Provide feedback',
    buildLongTermRelationships: 'Build long-term relationships',
    
    // Recent activity
    recentActivity: 'Recent Activity',
    stayUpdatedWithLatest: 'Stay updated with the latest activity',
    joinedAs: 'joined as',
    newJobPosted: 'New job posted',
    completedProject: 'completed project',
    leftReview: 'left review',
    loadMoreActivity: 'Load More Activity',
    
    // Testimonials
    whatOurClients: 'What Our Clients',
    areSaying: 'Are Saying',
    dontJustTakeWord: 'Don\'t just take our word for it',
    testimonial1: 'VWork helped us find the perfect development team. The quality of work exceeded our expectations and the project timeline was met.',
    testimonial2: 'Excellent platform for finding high-quality freelancers. We\'ve completed many successful projects through VWork.',
    testimonial3: 'User-friendly interface and professional workflow. VWork is our top choice for all our projects.',
    testimonial4: 'Dedicated support team and skilled freelancers. We\'re very satisfied with VWork\'s service.',
    testimonial5: 'VWork connected us with talented designers. The results exceeded expectations and were delivered on time.',
    project: 'Project',
    duration: 'Duration',
    months: 'months',
    mobileAppDevelopment: 'Mobile App Development',
    digitalMarketingCampaign: 'Digital Marketing Campaign',
    uiUxDesign: 'UI/UX Design',
    fullStackDevelopment: 'Full Stack Development',
    brandIdentityDesign: 'Brand Identity Design',
    happyClients: 'Happy Clients',
    projectSuccessRate: 'Project Success Rate',
    
    // CTA section
    readyToGetStarted2: 'Ready to Get Started?',
    started: 'Get Started',
    joinMillionsFreelancers: 'Join millions of freelancers and clients',
    joinAsFreelancer: 'Join as Freelancer',
    hireFreelancers: 'Hire Freelancers',
    instantMatching: 'Instant Matching',
    instantMatchingDesc: 'Find the right talent instantly',
    fastPayments: 'Fast Payments',
    fastPaymentsDesc: 'Secure and fast payment processing',
    qualityWorkDesc: 'High-quality work guaranteed',
    
    // Footer
    footerDescription: 'The leading platform connecting freelancers and clients',
    platform: 'Platform',
    communityHub: 'Community Hub',
    successStories: 'Success Stories',
    blog: 'Blog',
    events: 'Events',
    helpCenter: 'Help Center',
    contact: 'Contact',
    trustSafety: 'Trust & Safety',
    apiDocs: 'API Docs',
    company: 'Company',
    aboutUs: 'About Us',
    careers: 'Careers',
    press: 'Press',
    investors: 'Investors',
    allRightsReserved: 'All rights reserved',
    madeWithLove: 'Made with love',
    privacy: 'Privacy',
    terms: 'Terms',
    cookies: 'Cookies',
    global: 'Global',
    verifiedProfiles: 'Verified Profiles',
    support247: '24/7 Support',
    
    // Error messages
    authInvalidCredential: 'Invalid login credentials. Please check your email and password.',
    authUserNotFound: 'No account found with this email address.',
    authWrongPassword: 'Incorrect password.',
    authEmailAlreadyInUse: 'This email is already registered.',
    authWeakPassword: 'Password is too weak. Please choose a stronger password.',
    authInvalidEmail: 'Invalid email address.',
    authTooManyRequests: 'Too many attempts. Please try again later.',
    authNetworkError: 'Network error. Please check your internet connection.',
    authUserDisabled: 'This account has been disabled.',
    authGenericError: 'An error occurred. Please try again.',
    processing: 'Processing...',
    authErrorTips: 'Tips:',
    authCheckCredentials: 'Check your email and password',
    authRemoveSpaces: 'Remove extra spaces',
    authTryPasswordReset: 'Try password reset if needed',
    authWaitBeforeRetry: 'Please wait 1-2 minutes before trying again',
    authCheckInternet: 'Check your internet connection',
    authTryLoginInstead: 'Try logging in instead of signing up, or reset password',
    
    // Additional homepage keys
    trustedByIndustryLeaders: 'Trusted by industry leaders',
    joinThousandsOfCompanies: 'Join thousands of companies that rely on VWork',
    fortuneCompanies: 'Fortune 500 Companies',
    countriesServed: 'Countries Served',
    uptimeGuarantee: 'Uptime Guarantee',
    readyToJoinThem: 'Ready to join them?',
    startYourProject: 'Start your project today',
    whyLeadingCompanies: 'and see why leading companies choose VWork',
    getStartedNow: 'Get Started Now',
    technology: 'Technology',
    ecommerce: 'E-commerce',
    entertainment: 'Entertainment',
    music: 'Music',
    travel: 'Travel',
    transportation: 'Transportation',
    automotive: 'Automotive',
    socialMedia: 'Social Media',
    software: 'Software',
    crm: 'CRM',
  },
  vi: {
    // Navigation
    home: 'Trang Chủ',
    projects: 'Dự Án',
    freelancers: 'Tìm Freelancer',
    jobs: 'Việc Làm',
    contests: 'Cuộc Thi',
    dashboard: 'Bảng Điều Khiển',
    messages: 'Tin Nhắn',
    support: 'Hỗ Trợ',
    community: 'Cộng Đồng',
    settings: 'Cài Đặt',
    signIn: 'Đăng Nhập',
    signUp: 'Đăng Ký',
    signOut: 'Đăng Xuất',
    vwork: 'VWork',
    vworkAssistant: 'Trợ lý VWork',

    // Homepage
    heroTitle: 'Tìm Freelancer Hoàn Hảo Cho Dự Án Của Bạn',
    heroSubtitle:
      'Kết nối với các chuyên gia hàng đầu và hoàn thành công việc một cách tự tin',
    searchPlaceholder: 'Bạn đang tìm kiếm dịch vụ gì?',
    getStarted: 'Bắt Đầu Ngay',
    learnMore: 'Tìm Hiểu Thêm',
    add: 'Thêm',
    chatAI: 'Chat AI',

    // Categories
    webDevelopment: 'Phát Triển Web',
    mobileApps: 'Ứng Dụng Di Động',
    design: 'Thiết Kế & Sáng Tạo',
    writing: 'Viết & Dịch Thuật',
    marketing: 'Marketing Số',
    business: 'Kinh Doanh & Tư Vấn',

    // Features
    securePayments: 'Thanh Toán An Toàn',
    qualityWork: 'Chất Lượng Đảm Bảo',
    support24_7: 'Hỗ Trợ 24/7',
    easyToUse: 'Dễ Sử Dụng',

    // Projects
    postProject: 'Đăng Dự Án',
    viewDetails: 'Xem Chi Tiết',
    placeBid: 'Đặt Giá Thầu',
    budget: 'Ngân Sách',
    deadline: 'Thời Hạn',
    proposals: 'Đề Xuất',
    viewAllJobs: 'Xem Tất Cả Việc Làm',

    // Common
    loading: 'Đang tải...',
    search: 'Tìm kiếm',
    filter: 'Lọc',
    sort: 'Sắp xếp',
    save: 'Lưu',
    cancel: 'Hủy',
    delete: 'Xóa',
    edit: 'Chỉnh sửa',
    submit: 'Gửi',
    next: 'Tiếp theo',
    previous: 'Trước đó',
    more: 'Thêm',
    user: 'Người dùng',
    searchProjectsFreelancers: 'Tìm dự án, freelancer...',

    // Homepage specific translations
    findYourDreamJob: 'Tìm Công Việc Mơ Ước',
    hireTopTalent: 'Thuê Nhân Tài Hàng Đầu',
    buildYourCareer: 'Xây Dựng Sự Nghiệp',
    growYourBusiness: 'Phát Triển Kinh Doanh',
    perfectMatches: 'Kết Nối Hoàn Hảo',
    creativeSolutions: 'Giải Pháp Sáng Tạo',
    worldsWork: 'Công Việc Toàn Cầu',
    marketplace: 'Thị Trường',
    cursor: 'Con Trỏ',
    connectSubtitle: 'Kết nối mọi người',
    findJobs: 'Tìm Việc Làm',
    jobsDescription: 'Khám phá hàng nghìn cơ hội việc làm từ các công ty hàng đầu',

    // Navigation and actions
    findWork: 'Tìm Việc',
    hireTalent: 'Tìm Nhân Tài',
    postJob: '+ Đăng việc',

    // Stats
    activeUsers: 'Người dùng hoạt động',
    projectsCompleted: 'Dự án hoàn thành',
    successRate: 'Tỷ lệ thành công',
    averageRating: 'Đánh giá trung bình',

    // Search section
    findPerfectMatch: 'Tìm Kết Nối Hoàn Hảo',
    searchThousandsOfTalented: 'Tìm kiếm trong hàng nghìn tài năng',
    searchSkillsOrJobs: 'Tìm kiếm kỹ năng hoặc việc làm',
    location: 'Địa điểm',
    hourlyRate: 'Mức lương theo giờ',
    experienceLevel: 'Mức độ kinh nghiệm',
    availability: 'Tình trạng',
    moreFilters: 'Bộ lọc khác',
    searchNow: 'Tìm ngay',

    // Popular searches
    popularSearches: 'Tìm kiếm phổ biến',
    reactDeveloper: 'React Developer',
    uiUxDesigner: 'UI/UX Designer',
    contentWriter: 'Content Writer',
    digitalMarketing: 'Digital Marketing',
    mobileAppDeveloper: 'Mobile App Developer',
    dataScientist: 'Data Scientist',

    // Popular locations
    popularLocations: 'Địa điểm phổ biến',

    // Why choose section
    whyChooseVWork: 'Tại sao chọn VWork',
    trustedPlatformDesc: 'Nền tảng đáng tin cậy',
    secureAndTrusted: 'An toàn và đáng tin cậy',
    secureDesc: 'Bảo mật cao',
    fairPricing: 'Giá cả công bằng',
    fairPricingDesc: 'Mô tả giá cả công bằng',
    fastDelivery: 'Giao hàng nhanh',
    fastDeliveryDesc: 'Mô tả giao hàng nhanh',
    expertNetwork: 'Mạng lưới chuyên gia',
    expertNetworkDesc: 'Mô tả mạng lưới chuyên gia',
    qualityGuaranteed: 'Chất lượng đảm bảo',
    qualityGuaranteedDesc: 'Mô tả chất lượng đảm bảo',
    globalReach: 'Phạm vi toàn cầu',
    globalReachDesc: 'Mô tả phạm vi toàn cầu',

    // Numbers section
    numbersSpeak: 'Con số nói lên tất cả',
    trustedByThousands: 'Được tin tưởng bởi hàng nghìn người',
    activeFreelancers: 'Freelancer hoạt động',
    skilledProfessionals: 'Chuyên gia có kỹ năng',
    successfullyDelivered: 'Giao hàng thành công',
    totalEarnings: 'Tổng thu nhập',
    paidToFreelancers: 'Đã trả cho freelancer',
    clientSatisfaction: 'Sự hài lòng của khách hàng',
    projectSuccess: 'Thành công dự án',
    countries: 'Quốc gia',

    // Browse categories
    browseByCategory: 'Duyệt theo danh mục',
    findPerfectFreelancer: 'Tìm freelancer hoàn hảo',
    developmentIT: 'Phát triển & IT',
    developmentITDesc: 'Mô tả phát triển & IT',
    designCreative: 'Thiết kế & Sáng tạo',
    designCreativeDesc: 'Mô tả thiết kế & sáng tạo',
    salesMarketing: 'Bán hàng & Marketing',
    salesMarketingDesc: 'Mô tả bán hàng & marketing',
    writingTranslation: 'Viết & Dịch thuật',
    writingTranslationDesc: 'Mô tả viết & dịch thuật',
    videoPhotography: 'Video & Nhiếp ảnh',
    videoPhotographyDesc: 'Mô tả video & nhiếp ảnh',
    dataAnalytics: 'Dữ liệu & Phân tích',
    dataAnalyticsDesc: 'Mô tả dữ liệu & phân tích',
    musicAudio: 'Âm nhạc & Âm thanh',
    musicAudioDesc: 'Mô tả âm nhạc & âm thanh',
    customerService: 'Dịch vụ khách hàng',
    customerServiceDesc: 'Mô tả dịch vụ khách hàng',
    viewAllCategories: 'Xem tất cả danh mục',

    // Featured jobs
    featuredJobs: 'Việc làm nổi bật',
    discoverTopOpportunities: 'Khám phá cơ hội hàng đầu',
    urgent: 'Gấp',
    applyNow: 'Ứng tuyển ngay',

    // Top freelancers
    topFreelancers: 'Freelancer hàng đầu',
    workWithBestTalent: 'Làm việc với tài năng tốt nhất',
    topRated: 'Đánh giá cao',
    available: 'Có sẵn',
    response: 'Phản hồi',
    hire: 'Thuê',
    message: 'Tin nhắn',
    viewAllFreelancers: 'Xem tất cả freelancer',

    // Featured projects
    featuredProjects: 'Dự án nổi bật',
    projectsText: 'Dự án',
    exploreOutstandingWork: 'Khám phá công việc xuất sắc',
    allProjects: 'Tất cả dự án',
    webDesign: 'Thiết kế Web',
    branding: 'Thương hiệu',
    illustration: 'Minh họa',
    byFreelancer: 'bởi freelancer',
    viewAllProjects: 'Xem tất cả dự án',
    
    // How it works
    howItWorks: 'Cách thức hoạt động',
    simpleStepsToSuccess: 'Các bước đơn giản để thành công',
    postYourProject: 'Đăng dự án của bạn',
    postProjectDesc: 'Mô tả đăng dự án',
    describeProjectReq: 'Mô tả yêu cầu dự án',
    setBudgetTimeline: 'Đặt ngân sách và thời gian',
    choosePreferredSkills: 'Chọn kỹ năng ưa thích',
    reviewProposals: 'Xem xét đề xuất',
    reviewProposalsDesc: 'Mô tả xem xét đề xuất',
    receiveProposalsFromQualified: 'Nhận đề xuất từ người có trình độ',
    reviewPortfoliosPastWork: 'Xem xét hồ sơ và công việc trước đây',
    interviewTopCandidates: 'Phỏng vấn ứng viên hàng đầu',
    workPaySafely: 'Làm việc và thanh toán an toàn',
    workPaySafelyDesc: 'Mô tả làm việc và thanh toán an toàn',
    useEscrowProtection: 'Sử dụng bảo vệ ký quỹ',
    trackProgressMilestones: 'Theo dõi tiến độ và mốc quan trọng',
    payOnlyWhenSatisfied: 'Chỉ thanh toán khi hài lòng',
    leaveReview: 'Để lại đánh giá',
    leaveReviewDesc: 'Mô tả để lại đánh giá',
    rateFreelancerWork: 'Đánh giá công việc của freelancer',
    provideFeedback: 'Cung cấp phản hồi',
    buildLongTermRelationships: 'Xây dựng mối quan hệ dài hạn',
    
    // Recent activity
    recentActivity: 'Hoạt động gần đây',
    stayUpdatedWithLatest: 'Cập nhật với những hoạt động mới nhất',
    joinedAs: 'đã tham gia với vai trò',
    newJobPosted: 'Công việc mới đã đăng',
    completedProject: 'đã hoàn thành dự án',
    leftReview: 'đã để lại đánh giá',
    loadMoreActivity: 'Tải thêm hoạt động',

    // Testimonials
    whatOurClients: 'Khách hàng của chúng tôi',
    areSaying: 'nói gì',
    dontJustTakeWord: 'Đừng chỉ tin lời chúng tôi',
    testimonial1: 'VWork đã giúp chúng tôi tìm được đội ngũ phát triển hoàn hảo. Chất lượng công việc vượt quá mong đợi và tiến độ dự án được đảm bảo.',
    testimonial2: 'Nền tảng tuyệt vời để tìm kiếm freelancer chất lượng cao. Chúng tôi đã hoàn thành nhiều dự án thành công thông qua VWork.',
    testimonial3: 'Giao diện thân thiện, quy trình làm việc chuyên nghiệp. VWork là lựa chọn hàng đầu cho các dự án của chúng tôi.',
    testimonial4: 'Đội ngũ hỗ trợ tận tình, freelancer có kỹ năng cao. Chúng tôi rất hài lòng với dịch vụ của VWork.',
    testimonial5: 'VWork đã kết nối chúng tôi với những designer tài năng. Kết quả vượt ngoài mong đợi và đúng thời hạn.',
    mobileAppDevelopment: 'Phát triển ứng dụng di động',
    digitalMarketingCampaign: 'Chiến dịch marketing số',
    uiUxDesign: 'Thiết kế UI/UX',
    fullStackDevelopment: 'Phát triển Full Stack',
    brandIdentityDesign: 'Thiết kế nhận diện thương hiệu',
    months: 'tháng',
    happyClients: 'Khách hàng hài lòng',
    projectSuccessRate: 'Tỷ lệ thành công dự án',

    // CTA section
    readyToGetStarted2: 'Sẵn sàng bắt đầu?',
    started: 'Bắt đầu',
    joinMillionsFreelancers: 'Tham gia cùng hàng triệu freelancer và khách hàng',
    joinAsFreelancer: 'Tham gia với vai trò Freelancer',
    hireFreelancers: 'Thuê Freelancer',

    // Features bottom
    instantMatching: 'Kết nối tức thì',
    instantMatchingDesc: 'Tìm tài năng phù hợp ngay lập tức',
    fastPayments: 'Thanh toán nhanh',
    fastPaymentsDesc: 'Xử lý thanh toán an toàn và nhanh chóng',
    qualityWorkDesc: 'Đảm bảo chất lượng công việc cao',

    // Footer
    footerDescription: 'Nền tảng kết nối freelancer và khách hàng hàng đầu',
    platform: 'Nền tảng',
    communityHub: 'Trung tâm cộng đồng',
    successStories: 'Câu chuyện thành công',
    blog: 'Blog',
    events: 'Sự kiện',
    helpCenter: 'Trung tâm trợ giúp',
    contact: 'Liên hệ',
    trustSafety: 'Tin cậy & An toàn',
    apiDocs: 'Tài liệu API',
    company: 'Công ty',
    aboutUs: 'Về chúng tôi',
    careers: 'Tuyển dụng',
    press: 'Báo chí',
    investors: 'Nhà đầu tư',
    allRightsReserved: 'Tất cả quyền được bảo lưu',
    madeWithLove: 'Được tạo ra với tình yêu',
    privacy: 'Quyền riêng tư',
    terms: 'Điều khoản',
    cookies: 'Cookie',
    global: 'Toàn cầu',
    verifiedProfiles: 'Hồ sơ đã xác minh',
    support247: 'Hỗ trợ 24/7',

    // Auth form translations
    welcomeBack: 'Chào mừng trở lại',
    createYourAccount: 'Tạo tài khoản của bạn',
    signInToAccount: 'Đăng nhập vào tài khoản',
    joinWorldsLargest: 'Tham gia thị trường freelance lớn nhất thế giới',
    iWantTo: 'Tôi muốn',
    firstName: 'Tên',
    lastName: 'Họ',
    emailAddress: 'Địa chỉ email',
    password: 'Mật khẩu',
    confirmPassword: 'Xác nhận mật khẩu',
    
    // Validation messages
    emailRequired: 'Email là bắt buộc',
    emailInvalid: 'Email không hợp lệ',
    passwordRequired: 'Mật khẩu là bắt buộc',
    passwordMinLength: 'Mật khẩu phải có ít nhất 6 ký tự',
    firstNameRequired: 'Tên là bắt buộc',
    lastNameRequired: 'Họ là bắt buộc',
    passwordsNotMatch: 'Mật khẩu không khớp',

    // Additional missing keys
    watchDemo: 'Xem Demo',
    showPassword: 'Hiện mật khẩu',
    hidePassword: 'Ẩn mật khẩu',
    toggleDarkMode: 'Chuyển chế độ tối',
    loadingDashboard: 'Đang tải bảng điều khiển...',
    gatheringData: 'Đang thu thập dữ liệu...',
    webDevelopmentCategory: 'Phát triển Web',
    mobileDevelopmentCategory: 'Phát triển Mobile',
    designCategory: 'Thiết kế',
    writingCategory: 'Viết lách',
    marketingCategory: 'Marketing',
    dataScience: 'Khoa học dữ liệu',
    budgetRange: 'Khoảng ngân sách',
    under500: 'Dưới $500',
    budget500to1000: '$500 - $1,000',
    budget1000to5000: '$1,000 - $5,000',
    over5000: 'Trên $5,000',
    projectDuration: 'Thời gian dự án',
    lessThanWeek: 'Ít hơn một tuần',
    oneToFourWeeks: '1-4 tuần',
    oneToThreeMonths: '1-3 tháng',
    moreThanThreeMonths: 'Hơn 3 tháng',
    category: 'Danh mục',
    allContests: 'Tất cả cuộc thi',
    readyToShowcaseTalent: 'Sẵn sàng thể hiện tài năng?',
    joinThousandsDesigners: 'Tham gia cùng hàng nghìn nhà thiết kế và lập trình viên',
    browseActiveContests: 'Duyệt cuộc thi đang diễn ra',
    startYourOwnContest: 'Tạo cuộc thi của riêng bạn',
    
    // Additional missing keys for pages (Vietnamese)
    findTalentedFreelancers: 'Tìm Freelancer Tài Năng',
    connectWithProfessionals: 'Kết Nối Với Chuyên Gia',
    searchFreelancersPlaceholder: 'Tìm kiếm freelancer...',
    filters: 'Bộ Lọc',
    freelancersFound: 'freelancer tìm thấy',
    highestRated: 'Đánh Giá Cao Nhất',
    projectsCount: 'Dự Án',
    loadMoreFreelancers: 'Tải Thêm Freelancer',
    allCategories: 'Tất Cả Danh Mục',
    enterLocation: 'Nhập địa điểm',
    allLevels: 'Tất Cả Cấp Độ',
    clearFilters: 'Xóa Bộ Lọc',
    designContests: 'Cuộc Thi Thiết Kế',
    competeWinPrizes: 'Thi Đấu & Thắng Giải',
    activeContests: 'Cuộc Thi Đang Diễn Ra',
    totalPrizes: 'Tổng Giải Thưởng',
    participants: 'Người Tham Gia',
    winners: 'Người Thắng',
    communityHubTitle: 'Trung Tâm Cộng Đồng',
    connectLearnGrow: 'Kết Nối, Học Hỏi & Phát Triển',
    membersCount: 'Thành Viên',
    postsCount: 'Bài Viết',
    commentsCount: 'Bình Luận',
    onlineNow: 'Đang Trực Tuyến',
    newPost: 'Bài Viết Mới',
    sortBy: 'Sắp xếp theo',
    mostRecent: 'Mới Nhất',
    allPosts: 'Tất Cả Bài Viết',
    trending: 'Đang Thịnh Hành',
    timeAgo: 'trước',
    hoursAgo: 'giờ trước',
    daysAgo: 'ngày trước',
    loadMorePosts: 'Tải Thêm Bài Viết',

    // Additional keys for homepage
    project: 'Dự án',
    duration: 'Thời gian',
    
    // Missing homepage keys
    
    // Company info
    trustedByIndustryLeaders: 'Được tin tưởng bởi các nhà lãnh đạo ngành',
    joinThousandsOfCompanies: 'Tham gia cùng hàng nghìn công ty tin tưởng VWork',
    fortuneCompanies: 'Công ty Fortune 500',
    countriesServed: 'Quốc gia được phục vụ',
    uptimeGuarantee: 'Đảm bảo thời gian hoạt động',
    readyToJoinThem: 'Sẵn sàng tham gia cùng họ?',
    startYourProject: 'Bắt đầu dự án của bạn ngay hôm nay',
    whyLeadingCompanies: 'và xem tại sao các công ty hàng đầu chọn VWork',
    getStartedNow: 'Bắt Đầu Ngay',
    
    // Technology companies
    technology: 'Công nghệ',
    ecommerce: 'Thương mại điện tử',
    entertainment: 'Giải trí',
    music: 'Âm nhạc',
    travel: 'Du lịch',
    transportation: 'Vận tải',
    automotive: 'Ô tô',
    socialMedia: 'Mạng xã hội',
    software: 'Phần mềm',
    crm: 'CRM',
    
    // Error messages
    authInvalidCredential: 'Thông tin đăng nhập không hợp lệ. Vui lòng kiểm tra email và mật khẩu.',
    authUserNotFound: 'Không tìm thấy tài khoản với email này.',
    authWrongPassword: 'Mật khẩu không chính xác.',
    authEmailAlreadyInUse: 'Email này đã được đăng ký.',
    authWeakPassword: 'Mật khẩu quá yếu. Vui lòng chọn mật khẩu mạnh hơn.',
    authInvalidEmail: 'Địa chỉ email không hợp lệ.',
    authTooManyRequests: 'Quá nhiều lần thử. Vui lòng thử lại sau.',
    authNetworkError: 'Lỗi mạng. Vui lòng kiểm tra kết nối internet.',
    authUserDisabled: 'Tài khoản này đã bị vô hiệu hóa.',
    authGenericError: 'Đã xảy ra lỗi. Vui lòng thử lại.',
    processing: 'Đang xử lý...',
    authErrorTips: 'Gợi ý:',
    authCheckCredentials: 'Kiểm tra lại email và mật khẩu',
    authRemoveSpaces: 'Loại bỏ khoảng trắng thừa',
    authTryPasswordReset: 'Thử đặt lại mật khẩu nếu cần',
    authWaitBeforeRetry: 'Vui lòng đợi 1-2 phút trước khi thử lại',
    authCheckInternet: 'Kiểm tra kết nối internet của bạn',
    authTryLoginInstead: 'Thử đăng nhập thay vì đăng ký, hoặc đặt lại mật khẩu',
  },
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('vi'); // Default to Vietnamese
  const [isReady, setIsReady] = useState(false);

  // Load saved language preference
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language');
    console.log('🌐 Saved language from localStorage:', savedLanguage);
    
    if (savedLanguage && translations[savedLanguage]) {
      console.log('🌐 Using saved language:', savedLanguage);
      setLanguage(savedLanguage);
    } else {
      // Force set to Vietnamese if no valid saved language
      console.log('🌐 No valid saved language, forcing Vietnamese');
      setLanguage('vi');
      localStorage.setItem('language', 'vi');
    }
    
    setIsReady(true);
  }, []);

  // Update HTML lang attribute
  useEffect(() => {
    document.documentElement.lang = language;
  }, [language]);

  const changeLanguage = useCallback((lang) => {
    if (translations[lang]) {
      setLanguage(lang);
      localStorage.setItem('language', lang);
    }
  }, []);

  const t = useCallback((key) => {
    const result = translations[language][key] || translations.en[key] || key;
    
    // Enhanced debug logging
    if (result === key && !translations[language][key] && !translations.en[key]) {
      console.warn(`🌐 Missing translation for key: "${key}" in language: ${language}`);
      console.warn(`🌐 Available keys in ${language}:`, Object.keys(translations[language] || {}).length);
      console.warn(`🌐 Available keys in en:`, Object.keys(translations.en || {}).length);
    }
    
    // Log successful fallbacks for debugging
    if (result !== key && !translations[language][key] && translations.en[key]) {
      console.log(`🌐 Fallback used for "${key}": ${language} -> en`);
    }
    
    return result;
  }, [language]);

  const value = useMemo(() => {
    console.log('🌐 LanguageContext value updated:', {
      language,
      isReady,
      hasTranslations: !!translations[language],
      translationKeys: Object.keys(translations[language] || {}).length
    });
    
    return {
      language,
      changeLanguage,
      t,
      translations: translations[language],
      availableLanguages: Object.keys(translations),
      isReady,
    };
  }, [language, changeLanguage, t, isReady]);

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

LanguageProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
